import { ValueLabelPair } from '@/PlatformStandard/common/defines';

export interface TreeItem {
  id: string;
  pId?: string;
  value: string;
  title: string;
  isLeaf?: boolean;
  /**
   * 自定义扩展数据
   */
  datum?: any;
}

export interface UserItemDto {
  userId?: string;
  account?: string;
  userName?: string;
  datum?: any;
  label?: any;
  value?: string;
  organizationNamePath?: string;
}

export interface OrganizationTreeSelectDto extends ValueLabelPair {
  code: string;
  childCount: number;
  fullPathCode: string;
}
