@import '../../../../../themes/default/variables.less';

.page{
  display: flex;
  .tree_list{
    // margin: @base-size * 2;
    width: 330px;
    height: 510px;
    .tree_card{
      width: 100%;
      display: flex !important;
      flex-direction: column;
      height: 100%;
      :global(.ant-card-body){
        height: 90% !important;
      }
    }
  }
  .content_info {
    width: 100%;
   .edit_info {
     width: 100%;
     // width: calc(100% - 200px);
     // margin-left: @card-padding-base;
     padding-left: @card-padding-base;
     .info_card {
       display: flex !important;
       width: 100%;
       flex-direction: column;
       height: auto;
      // height: calc(100vh - 20px);
      // overflow: auto;
       .buttons {
         height: @btn-height-sm;
       }
     }
   }
  // .edit_info{
  //   width: 100%;
  //   // margin: @base-size * 2;
  //   .info_card{
  //     display: flex !important;
  //     width: 100%;
  //     flex-direction: column;
  //     height: auto;
  //     .buttons{
  //       height: @btn-height-base;
  //       margin-right: @base-size;
  //     }
  //   }
  }
}
.common_btn {
  padding:0;
  margin: 0  0 0 10px;
  width: 70px !important;
  height: 30px !important;
  text-align: center;
  line-height: 32px;
  border-radius: 5px !important;
  border: 1px solid #ddd !important;
  background-color: #fff !important;
  color: #333 !important;
  opacity: 1;
  text-shadow: none !important;
  box-shadow: none !important;
}