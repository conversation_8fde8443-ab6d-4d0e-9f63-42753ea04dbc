import { formHelper, i18nHelper, notificationHelper } from '@/PlatformStandard/common/utils';
import { commonService } from '@/PlatformStandard/services/common';
import { CompCard } from '@/PlatformStandard/components';
import { languageService } from '@/PlatformStandard/services/language';
import { WrappedFormUtils } from 'ant-design-vue/types/form/form';
import { Component, Vue } from 'vue-property-decorator';
import { userService } from '../service';
import { RoleDto, RoleUserRelationDto, UserDto, UserPositionDto, UserReportRelationDto } from '../types';
import styles from './user-edit.module.less';
import { UserPositionEdit } from './UserPositionEdit';
import { UserRoleEdit } from './UserRoleEdit';
import debounce from 'lodash/debounce';
import { ReportRelation } from './ReportRelation';
import { FooterBottom } from '@/PlatformStandard/components/FooterBottom';

@Component({
  components: { CompCard, UserPositionEdit, UserRoleEdit, ReportRelation, FooterBottom }
})
export class UserEdit extends Vue {
  private userId = '';
  private currentUser: UserDto = { roles: [], organizationPositions: [], userExtendInfo: {}, reportRelations: [] };
  private statusItems: any = [];
  private workingStatusItems: any = [];
  private genderItems = i18nHelper.getLocaleObject('platform.user.genderDataset');
  private userForm!: WrappedFormUtils;
  private userExtForm!: WrappedFormUtils;
  private userFetching = false;
  private fetchUser = debounce((value: any) => this.userSearch(value), 500);
  private userItems: any = [];
  private userSaved = false;

  private orgColumns = [
    {
      dataIndex: 'organizationCode',
      slots: { title: 'organizationCode' }
    },
    {
      dataIndex: 'organizationName',
      slots: { title: 'organizationName' },
      scopedSlots: { customRender: 'organizationName' },
    },
    {
      dataIndex: 'positionCode',
      slots: { title: 'positionCode' }
    },
    {
      dataIndex: 'positionName',
      slots: { title: 'positionName' },
    },
    {
      dataIndex: 'primaryPosition',
      slots: { title: 'primaryPosition' },
      scopedSlots: { customRender: 'primaryPosition' },
    },
    {
      dataIndex: 'action',
      slots: { title: 'action' },
      scopedSlots: { customRender: 'action' },
      width: '200px'
    },
  ];
  private orgFieldsSlotMap: any = {};
  private userPositionShow = false;
  private selectUserPosition: UserPositionDto = {};

  private roleColumns = [
    {
      dataIndex: 'roleCode',
      slots: { title: 'roleCode' }
    },
    {
      dataIndex: 'roleName',
      slots: { title: 'roleName' },
    },
    {
      dataIndex: 'description',
      slots: { title: 'description' },
      width: '50%'
    },
    {
      dataIndex: 'action',
      slots: { title: 'action' },
      scopedSlots: { customRender: 'action' },
      width: '200px'
    },
  ];
  private roleRelationShow = false;
  private roleFieldsSlotMap: any = {};

  private reportColumns = [
    {
      dataIndex: 'leaderName',
      slots: { title: 'leaderName' }
    },
    {
      dataIndex: 'jobGrade',
      slots: { title: 'jobGrade' },
    },
    {
      dataIndex: 'positionName',
      slots: { title: 'positionName' },
    },
    {
      dataIndex: 'businessLineName',
      slots: { title: 'businessLineName' },
    },
    {
      dataIndex: 'action',
      slots: { title: 'action' },
      scopedSlots: { customRender: 'action' },
      width: '200px'
    },
  ];
  private reportRelationShow = false;
  private reportFieldsSlotMap: any = {};

  private userSearch(value: string) {
    userService.getUsers(value).subscribe(data => {
      this.userItems = data;
      this.userFetching = false;
      this.$forceUpdate();
    });
  }

  private goBack() {
    commonService.backParentPage(this.$route, this.$router);
  }

  private userSave() {
    const errMsgs = formHelper.validateForm(this.userForm);
    if (errMsgs.length === 0) {
      if (this.userId && this.userId !== '') {
        userService.updateUser(this.userId, this.currentUser).subscribe(() => {
          this.currentUser.userExtendInfo.userId = this.userId;
          userService.updateUserExtendInfo(this.currentUser.userExtendInfo).subscribe(() => {
            notificationHelper.success(i18nHelper.getLocale('messages.success'));
          });
        });
      } else {
        userService.addUser(this.currentUser).subscribe(data => {
          this.userId = data;
          this.userSaved = true;
          this.currentUser.userExtendInfo.userId = this.userId;
          userService.updateUserExtendInfo(this.currentUser.userExtendInfo).subscribe(() => {
            notificationHelper.success(i18nHelper.getLocale('messages.success'));
          });
        });
      }
    } else {
      notificationHelper.error(errMsgs);
    }
  }

  /* private userExtSave() {
    const errMsgs = formHelper.validateForm(this.userExtForm);
    if (errMsgs.length === 0) {
      this.currentUser.userExtendInfo.userId = this.userId;
      userService.updateUserExtendInfo(this.currentUser.userExtendInfo).subscribe(data => {
        notificationHelper.success(i18nHelper.getLocale('messages.success'));
      });
    } else {
      notificationHelper.error(errMsgs);
    }
  } */

  private addUserPostion() {
    this.selectUserPosition = {};
    this.userPositionShow = true;
  }

  private editUserPostion(userPosition: UserPositionDto) {
    this.selectUserPosition = userPosition;
    this.userPositionShow = true;
  }

  private deleteUserPostion(userPosition: UserPositionDto) {
    userService.deletePositionAndUser(String(userPosition.positionId)).subscribe(() => {
      notificationHelper.success(i18nHelper.getLocale('messages.success'));
      this.refreshPositions();
    });
  }

  private cancelUserPosition() {
    this.userPositionShow = false;
  }

  private saveUserPosition() {
    const errorMsgs = (this.$refs.editUserPosition as UserPositionEdit).validateForm() as string[];
    if (errorMsgs.length === 0) {
      const userPosition = (this.$refs.editUserPosition as UserPositionEdit).save() as UserPositionDto;
      userPosition.userId = this.userId;
      userService.savePositionAndUser(userPosition).subscribe(() => {
        this.userPositionShow = false;
        notificationHelper.success(i18nHelper.getLocale('messages.success'));
        this.refreshPositions();
      });
    } else {
      notificationHelper.error(errorMsgs);
    }
  }

  private refreshPositions() {
    userService.getOrganizationPositionsByUserId(this.userId).subscribe(userPositions => {
      this.currentUser.organizationPositions = userPositions;
      this.$forceUpdate();
    });
  }

  private addRoleRelation() {
    this.roleRelationShow = true;
  }

  private cancelRoleRelation() {
    this.roleRelationShow = false;
  }

  private saveRoleRelation() {
    const roleKeys = (this.$refs.editUserRole as UserRoleEdit).save() as string[];
    if (roleKeys.length > 0) {
      const userRoles: RoleUserRelationDto[] = [];
      roleKeys.forEach(key => {
        userRoles.push({ userId: this.userId, roleId: key });
      });
      userService.addMember(userRoles).subscribe(() => {
        this.roleRelationShow = false;
        notificationHelper.success(i18nHelper.getLocale('messages.success'));
        this.refreshRoles();
      });
    } else {
      notificationHelper.error(i18nHelper.getLocale('platform.user.lessOneRole'));
    }
  }

  private refreshRoles() {
    userService.getRolesByUserId(this.userId).subscribe(data => {
      this.currentUser.roles = data;
      this.$forceUpdate();
    });
  }

  private deleteRoleRelation(userRole: RoleDto) {
    userService.deleteUserRole(this.userId, String(userRole.roleId)).subscribe(() => {
      notificationHelper.success(i18nHelper.getLocale('messages.success'));
      this.refreshRoles();
    });
  }

  private addReportRelation() {
    this.reportRelationShow = true;
  }

  private cancelReportRelation() {
    this.reportRelationShow = false;
  }

  private saveReportRelation() {
    const reportRelation = (this.$refs.eidtReportRelation as ReportRelation).save();
    reportRelation.reportUserId = this.userId;
    userService.addReportRelation(reportRelation).subscribe(() => {
      this.reportRelationShow = false;
      notificationHelper.success(i18nHelper.getLocale('messages.success'));
      this.refreshReports();
    });
  }

  private refreshReports() {
    userService.getReportRelationsByUserId(this.userId).subscribe(data => {
      this.currentUser.reportRelations = data;
      this.$forceUpdate();
    });
  }

  private deleteReportRelation(reportRelation: UserReportRelationDto) {
    userService.deleteReportRelation(String(reportRelation.reportRelationId)).subscribe(() => {
      notificationHelper.success(i18nHelper.getLocale('messages.success'));
      this.refreshReports();
    });
  }

  created(): void {
    this.userForm = this.$form.createForm(this, { name: 'userForm' });
    this.userExtForm = this.$form.createForm(this, { name: 'userExtForm' });
    this.userId = this.$route.query['user-id'] as string;
    if (this.userId && this.userId !== '') {
      this.userSaved = true;
      userService.getUserById(this.userId).subscribe(data => {
        this.currentUser = data;
        if (this.currentUser.upperUserName) {
          this.userSearch(this.currentUser.upperUserLoginId as string);
        }
      });
      languageService.language$.subscribe(() => {
        this.statusItems = i18nHelper.getLocaleObject('platform.user.statusDataset');
        this.genderItems = i18nHelper.getLocaleObject('platform.user.genderDataset');
        this.workingStatusItems = i18nHelper.getLocaleObject('platform.user.workingStatusDataset');
      });
    } else {
      this.currentUser = { status: 1, roles: [], organizationPositions: [], userExtendInfo: {}, reportRelations: [] };
    }

    this.statusItems = i18nHelper.getLocaleObject('platform.user.statusDataset');
    this.workingStatusItems = i18nHelper.getLocaleObject('platform.user.workingStatusDataset');

    this.orgFieldsSlotMap['organizationName'] = (text: UserPositionDto[], record: UserPositionDto, index: number) => {
      return (
        <span class={styles.org_title}>
          <a-tooltip>
            <template slot='title'>
              {record.organizationFullName}
            </template>
            {record.organizationName}
            <a-icon type='info-circle' theme='filled' />
          </a-tooltip>
        </span>
      );
    };

    this.orgFieldsSlotMap['primaryPosition'] = (text: UserPositionDto[], record: UserPositionDto, index: number) => {
      return (
        <div>
          {
            record.primaryPosition ?
              '是'
              :
              '否'
          }
        </div>
      );
    };

    this.orgFieldsSlotMap['action'] = (text: UserPositionDto[], record: UserPositionDto, index: number) => {
      return (
        <div>
          <span class='mr-1'>
            <a-button type='link' on-click={() => this.editUserPostion(record)} size='small' class={styles.list_button}>
              {this.$l.getLocale('buttons.edit')}
            </a-button>
          </span>
          <span class='mr-1'>
            <a-popconfirm title={this.$t('messages.delete')} on-confirm={() => this.deleteUserPostion(record)}>
              <a-button type='link' size='small' class={styles.list_button}>
                {this.$l.getLocale('buttons.delete')}
              </a-button>
            </a-popconfirm>
          </span>
        </div>
      );
    };

    this.roleFieldsSlotMap['action'] = (text: RoleDto[], record: RoleDto, index: number) => {
      return (
        <div>
          <span>
            <a-popconfirm title={this.$t('messages.delete')} on-confirm={() => this.deleteRoleRelation(record)}>
              <a-button type='link' size='small' class={styles.list_button}>
                {this.$l.getLocale('buttons.delete')}
              </a-button>
            </a-popconfirm>
          </span>
        </div>
      );
    };

    this.reportFieldsSlotMap['action'] = (text: UserReportRelationDto[], record: UserReportRelationDto, index: number) => {
      return (
        <div>
          <span>
            <a-popconfirm title={this.$t('messages.delete')} on-confirm={() => this.deleteReportRelation(record)}>
              <a-button type='link' size='small' class={styles.list_button}>
                {this.$l.getLocale('buttons.delete')}
              </a-button>
            </a-popconfirm>
          </span>
        </div>
      );
    };
  }

  render() {
    return (
      <div>
        <a-card>
          <div slot='extra'>
            <a-button on-click={() => this.goBack()}>{this.$t('buttons.back')}</a-button>
          </div>
          <div class='cardBorderTop'>
            <a-form form={this.userForm} labelCol={{ span: 5 }} wrapperCol={{ span: 16 }}>
                <comp-card title={this.$t('platform.user.baseInfo')} cardTitleBg={true}>
                  {/* <div slot='extra'>
                    <a-button type='primary' on-click={() => this.userSave()} class='mr-1'>{this.$t('buttons.save')}</a-button>
                  </div> */}
                  <div>
                    <a-row>
                      <a-col span='12'>
                        <a-form-item label={this.$t('platform.fields.account')} required>
                          <a-input on-change={(e: any) => { this.currentUser.account = e.target.value; }}
                            placeholder={this.$l.getLocale(['controls.input', 'platform.fields.account'])}
                            v-decorator={['account', {
                              initialValue: this.currentUser.account,
                              rules: [{ required: true, message: this.$l.getLocale(['controls.input', 'platform.fields.account']) }]
                            }]} />
                        </a-form-item>
                      </a-col>
                      <a-col span='12'>
                        <a-form-item label={this.$t('platform.fields.name')} required>
                          <a-input on-change={(e: any) => { this.currentUser.name = e.target.value; }}
                            placeholder={this.$l.getLocale(['controls.input', 'platform.fields.name'])}
                            v-decorator={['name', {
                              initialValue: this.currentUser.name,
                              rules: [{ required: true, message: this.$l.getLocale(['controls.input', 'platform.fields.name']) }]
                            }]} />
                        </a-form-item>
                      </a-col>
                    </a-row>
                    <a-row>
                      <a-col span='12'>
                        <a-form-item label={this.$t('platform.user.phone')}>
                          <a-input v-model={this.currentUser.phoneNumber}
                            placeholder={this.$l.getLocale(['controls.input', 'platform.user.phone'])} />
                        </a-form-item>
                      </a-col>
                      <a-col span='12'>
                        <a-form-item label={this.$t('platform.user.email')}>
                          <a-input v-model={this.currentUser.email}
                            placeholder={this.$l.getLocale(['controls.input', 'platform.user.email'])} />
                        </a-form-item>
                      </a-col>
                    </a-row>
                    <a-row>
                      <a-col span='12'>
                        <a-form-item label={this.$t('platform.user.gender')}>
                          <a-select
                            placeholder={this.$l.getLocale(['controls.select', 'platform.user.gender'])}
                            style='width:100%;'
                            v-model={this.currentUser.gender}
                          >
                            {this.genderItems.map((item: any) => (
                              <a-select-option value={item.value}>{item.label}</a-select-option>
                            ))}
                          </a-select>
                        </a-form-item>
                      </a-col>
                      <a-col span='12'>
                        <a-form-item label={this.$t('platform.user.workNumber')}>
                          <a-input v-model={this.currentUser.number}
                            placeholder={this.$l.getLocale(['controls.input', 'platform.user.workNumber'])} />
                        </a-form-item>
                      </a-col>
                    </a-row>
                    <a-row>
                      <a-col span='12'>
                        <a-form-item label={this.$t('platform.user.upperUser')}>
                          <a-select
                            show-search
                            allowClear
                            placeholder={this.$l.getLocale(['controls.select', 'platform.user.upperUser'])}
                            default-active-first-option={false}
                            show-arrow={false}
                            filter-option={false}
                            not-found-content={this.userFetching ? undefined : null}
                            on-search={this.fetchUser}
                            on-change={(value: any) => {
                              this.currentUser.upperUserName = (this.userItems.find((d: any) => d.value === value) as any).label;
                            }}
                            v-model={this.currentUser.upperUserId}
                          >
                            {
                              this.userFetching ?
                                <a-spin slot='notFoundContent' size='small' />
                                :
                                null
                            }

                            {
                              this.userItems.map((item: any) => (
                                <a-select-option value={item.value}>
                                  {item.label}
                                </a-select-option>
                              ))
                            }
                          </a-select>
                        </a-form-item>
                      </a-col>
                      {
                        this.userId && this.userId !== '' ?
                          <a-col span='12'>
                            <a-form-item label={this.$t('platform.fields.status')}>
                              <a-select
                                placeholder={this.$l.getLocale(['controls.select', 'platform.fields.status'])}
                                style='width:100%;'
                                on-change={(value: any) => { this.currentUser.status = value; }}
                                v-decorator={[
                                  'status',
                                  {
                                    initialValue: this.currentUser.status,
                                    rules: [{
                                      required: true,
                                      message: this.$l.getLocale(['controls.select', 'platform.fields.status'])
                                    }]
                                  }]}
                              >
                                {this.statusItems.map((item: any) => (
                                  <a-select-option value={item.value}>{item.label}</a-select-option>
                                ))}
                              </a-select>
                            </a-form-item>
                          </a-col>
                          :
                          null
                      }
                      {
                        this.userId && this.userId !== '' ?
                          <a-col span='12'>
                            <a-form-item label={this.$t('platform.user.workingState')}>
                              <a-select
                                placeholder={this.$l.getLocale(['controls.select', 'platform.user.workingState'])}
                                style='width:100%;'
                                on-change={(value: any) => { this.currentUser.userExtendInfo.workingState = value; }}
                                v-decorator={[
                                  'workingState',
                                  {
                                    initialValue: this.currentUser.userExtendInfo.workingState,
                                    rules: [{
                                      required: true,
                                      message: this.$l.getLocale(['controls.select', 'platform.user.workingState'])
                                    }]
                                  }]}
                              >
                                {this.workingStatusItems.map((item: any) => (
                                  <a-select-option value={item.value}>{item.label}</a-select-option>
                                ))}
                              </a-select>
                            </a-form-item>
                          </a-col>
                          :
                          null
                      }
                      <a-col span='12'>
                        <a-form-item label={this.$t('platform.fields.remark')}>
                          <a-textarea v-model={this.currentUser.remark}
                            placeholder={this.$l.getLocale(['controls.input', 'platform.fields.remark'])} rows='4' />
                        </a-form-item>
                      </a-col>
                    </a-row>
                  </div>
                </comp-card>
            </a-form>
          </div>
          <a-tabs default-active-key={1} class='mt-1'>
            <a-tab-pane key={1} tab={this.$t('platform.user.extendInfo')} disabled={!this.userSaved}>
              <a-form form={this.userExtForm} labelCol={{ span: 5 }} wrapperCol={{ span: 16 }}>
                {/* <comp-card title={this.$t('platform.user.extendInfo')}> */}
                <comp-card>
                  {/* {
                    this.userSaved ?
                      <div slot='extra'>
                        <a-button type='primary' on-click={() => this.userExtSave()} class='mr-1'>{this.$t('buttons.save')}</a-button>
                      </div>
                      :
                      null
                  } */}
                  <div>
                    <a-row>
                      <a-col span='12'>
                        <a-form-item label={this.$t('platform.user.jobGrade')}>
                          <a-input v-model={this.currentUser.userExtendInfo.jobGrade}
                            placeholder={this.$l.getLocale(['controls.input', 'platform.user.jobGrade'])} />
                        </a-form-item>
                      </a-col>
                      <a-col span='12'>
                        <a-form-item label={this.$t('platform.user.education')}>
                          <a-input v-model={this.currentUser.userExtendInfo.education}
                            placeholder={this.$l.getLocale(['controls.input', 'platform.user.education'])} />
                        </a-form-item>
                      </a-col>
                    </a-row>
                    <a-row>
                      <a-col span='12'>
                        <a-form-item label={this.$t('platform.user.joinDate')}>
                          <a-date-picker v-model={this.currentUser.userExtendInfo.joinDate}
                            placeholder={this.$l.getLocale(['controls.select', 'platform.user.joinDate'])} />
                        </a-form-item>
                      </a-col>
                    </a-row>
                  </div>
                </comp-card>
              </a-form>
            </a-tab-pane>
            <a-tab-pane key={2} tab={this.$t('platform.user.reportRelation')} disabled={!this.userSaved}>
              {/* <comp-card title={this.$t('platform.user.reportRelation')}> */}
              <comp-card>
                <div clas={styles.row_outer}>
                  <a-row class={styles.row} gutter={8} align='middle' type='flex'>
                    <a-col span='24' class={styles.operation}>
                      <a-button type='primary' on-click={() => this.addReportRelation()}>
                        {this.$t('buttons.add')}
                      </a-button>
                    </a-col>
                  </a-row>
                </div>
                <a-table
                  rowKey='reportRelationId'
                  columns={this.reportColumns}
                  dataSource={this.currentUser.reportRelations}
                  pagination={false}
                  scopedSlots={this.reportFieldsSlotMap}
                >
                  <span slot='leaderName'>{this.$t('platform.user.leaderName')}</span>
                  <span slot='businessLineName'>{this.$t('platform.user.businessLineName')}</span>
                  <span slot='positionName'>{this.$t('platform.organization.positionName')}</span>
                  <span slot='jobGrade'>{this.$t('platform.user.jobGrade')}</span>
                  <span slot='action'>{this.$t('platform.fields.operation')}</span>
                </a-table>
              </comp-card>
              <a-modal
                width='800px'
                title={this.$t('platform.user.reportRelation')}
                visible={this.reportRelationShow}
                destroyOnClose={true}
                maskClosable={false}
                on-cancel={() => this.cancelReportRelation()}
                on-ok={() => this.saveReportRelation()}
              >
                <report-relation
                  ref='eidtReportRelation' />
              </a-modal>
            </a-tab-pane>
            <a-tab-pane key={3} tab={this.$t('platform.user.orgRelation')} disabled={!this.userSaved}>
              {/* <comp-card title={this.$t('platform.user.orgRelation')}> */}
              <comp-card>
                <div clas={styles.row_outer}>
                  <a-row class={styles.row} gutter={8} align='middle' type='flex'>
                    <a-col span='24' class={styles.operation}>
                      <a-button type='primary' on-click={() => this.addUserPostion()}>
                        {this.$t('buttons.add')}
                      </a-button>
                    </a-col>
                  </a-row>
                </div>
                <a-table
                  columns={this.orgColumns}
                  dataSource={this.currentUser.organizationPositions}
                  pagination={false}
                  scopedSlots={this.orgFieldsSlotMap}
                  rowKey='positionId'
                >
                  <span slot='organizationCode'>{this.$t('platform.organization.organizationCode')}</span>
                  <span slot='organizationName'>{this.$t('platform.organization.organizationName')}</span>
                  <span slot='positionCode'>{this.$t('platform.organization.positionCode')}</span>
                  <span slot='positionName'>{this.$t('platform.organization.positionName')}</span>
                  <span slot='primaryPosition'>{this.$t('platform.organization.isPrimary')}</span>
                  <span slot='action'>{this.$t('platform.fields.operation')}</span>
                </a-table>
              </comp-card>
              <a-modal
                width='800px'
                title={this.$t('platform.organization.organizationUser')}
                visible={this.userPositionShow}
                destroyOnClose={true}
                maskClosable={false}
                on-cancel={() => this.cancelUserPosition()}
                on-ok={() => this.saveUserPosition()}
              >
                <user-position-edit
                  ref='editUserPosition'
                  selectUserPosition={this.selectUserPosition} />
              </a-modal>
            </a-tab-pane>
            <a-tab-pane key={4} tab={this.$t('platform.user.roleRelation')} disabled={!this.userSaved}>
              {/* <comp-card title={this.$t('platform.user.roleRelation')}> */}
              <comp-card>
                <div clas={styles.row_outer}>
                  <a-row class={styles.row} gutter={8} align='middle' type='flex'>
                    <a-col span='24' class={styles.operation}>
                      <a-button type='primary' on-click={() => this.addRoleRelation()}>
                        {this.$t('buttons.add')}
                      </a-button>
                    </a-col>
                  </a-row>
                </div>
                <a-table
                  rowKey='roleId'
                  columns={this.roleColumns}
                  dataSource={this.currentUser.roles}
                  pagination={false}
                  scopedSlots={this.roleFieldsSlotMap}
                >
                  <span slot='roleCode'>{this.$t('platform.fields.code')}</span>
                  <span slot='roleName'>{this.$t('platform.fields.name')}</span>
                  <span slot='description'>{this.$t('platform.fields.description')}</span>
                  <span slot='action'>{this.$t('platform.fields.operation')}</span>
                </a-table>
              </comp-card>
              <a-modal
                width='800px'
                title={this.$t('platform.fields.role')}
                visible={this.roleRelationShow}
                destroyOnClose={true}
                maskClosable={false}
                on-cancel={() => this.cancelRoleRelation()}
                on-ok={() => this.saveRoleRelation()}
              >
                <user-role-edit
                  ref='editUserRole' />
              </a-modal>
            </a-tab-pane>
          </a-tabs>
        </a-card>
        <div style='width:100%;overflow:hidden'>
          <footer-bottom>
            <template slot='extra'>
              <a-button type='primary' on-click={() => this.userSave()} class='mr-1'>{this.$t('buttons.save')}</a-button>
            </template>
          </footer-bottom>
        </div>
      </div>
    );
  }
}
