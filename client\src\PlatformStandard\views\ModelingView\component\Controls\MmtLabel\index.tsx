import { dateFormat } from '../../../common/utils';
import { Component, Emit, Prop, Vue } from 'vue-property-decorator';
import { BaseControl } from '../base-control';

@Component({
})
export class MmtLabel extends BaseControl {
  private getFormatText(text: any) {
    if (this.controlConfig.config.format.type === 'date') {
      return text
        ? dateFormat(text, this.controlConfig.config.format.value.displayFormat)
        : '';
    } else if (this.controlConfig.config.format.type === 'number') {
      let num =
        this.controlConfig.config.format.value.precision || this.controlConfig.config.format.value.precision === 0
          ? Number(text).toFixed(this.controlConfig.config.format.value.precision)
          : text;
      if (this.controlConfig.config.format.value.hasThousandth) {
        const arr = num.split('.');
        let result = '';
        arr.forEach((f: any, i: number) => {
          if (i === 0) {
            result += `${f}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
          } else {
            result += `.${f}`;
          }
        });
        num = result;
      }
      return num;
    } else if (this.controlConfig.config.format.type === 'enum') {
      const enumItem = this.controlConfig.config.format.value.enum.find(
        (e: any) => e.value === text.toString()
      );
      return enumItem ? enumItem.label : '';
    }
  }
  render() {
    return this.getFormatText(this.value);
  }
}
