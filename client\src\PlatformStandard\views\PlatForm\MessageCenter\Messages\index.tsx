import { Component, Vue } from 'vue-property-decorator';
import { CompCard, CompTableHeader } from '@/PlatformStandard/components';
import styles from './Message.module.less';
import { MessageDto, MessageQueryDto } from './types';
import { ActionEnum } from '@/PlatformStandard/services/menu';
import { messageService } from './service';
import { i18nHelper, notificationHelper, toCasedStyleObject } from '@/PlatformStandard/common/utils';
import { languageService } from '@/PlatformStandard/services/language';

@Component({ components: { CompCard, CompTableHeader } })
export class MessageList extends Vue {
  private pagination = {
    total: 0,
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    size: 'default',
    showTotal: (total: string) =>
      this.$l
        .getLocale('paginations.total')
        .toString()
        .replace('{{value}}', total),
  };
  private query: MessageQueryDto = {};
  private dataSource: any;
  private fieldsSlotMap: any = {};
  private sendStatusDataset: any = [];
  private messageTypeDataset: any = [];

  private columns = [
    {
      dataIndex: 'keyValue',
      slots: { title: 'keyValue' },
      width: '13%'
    },
    {
      dataIndex: 'msgType',
      slots: { title: 'msgType' },
      scopedSlots: { customRender: 'msgType' },
      width: '10%'
    },
    {
      dataIndex: 'category',
      slots: { title: 'category' },
      width: '10%'
    },
    {
      dataIndex: 'fromSys',
      slots: { title: 'fromSys' },
      width: '10%'
    },
    {
      dataIndex: 'msgTitle',
      slots: { title: 'msgTitle' },
    },
    {
      dataIndex: 'sendingStatus',
      slots: { title: 'sendingStatus' },
      scopedSlots: { customRender: 'sendingStatus' },
      width: '10%'
    },
    {
      dataIndex: 'sendTime',
      slots: { title: 'sendTime' },
      width: '8%'
    },
    {
      dataIndex: 'tryTimes',
      slots: { title: 'tryTimes' },
      width: '8%'
    },
    {
      dataIndex: 'action',
      slots: { title: 'action' },
      scopedSlots: { customRender: 'action' },
      width: '12%'
    },
  ];

  private viewShow = false;
  private actions = ActionEnum;
  private opration!: string;
  private currentData: MessageDto = {};

  private loadData(reset: boolean = false) {
    if (reset) {
      this.pagination.current = 1;
    }
    const params = { 'page-index': this.pagination.current, 'page-size': this.pagination.pageSize };
    messageService.getMessageList({ ...toCasedStyleObject(this.query), ...params }).subscribe(data => {
      this.dataSource = data.items;
      this.pagination.total = data.total;
      this.$forceUpdate();
    });
  }

  private reset() {
    this.query = {};
  }

  private view(record: MessageDto | null) {
    if (record) {
      // this.currentData = JSON.parse(JSON.stringify(record));
      messageService.getMessage(String(record.msgId)).subscribe(rs => {
        this.currentData = rs;
        this.viewShow = true;
      });
    }
  }

  private cancel() {
    this.viewShow = false;
    this.currentData = {};
  }

  private resetMessage(messageId: string | undefined) {
    if (messageId) {
      messageService.resetMessage(messageId).subscribe(() => {
        notificationHelper.success(i18nHelper.getLocale('messages.success'));
        this.loadData(true);
      });
    }
  }

  created() {
    languageService.langeAsync$.subscribe(() => {
      this.sendStatusDataset = i18nHelper.getLocaleObject('platform.message.sendStatus');
      this.messageTypeDataset = i18nHelper.getLocaleObject('platform.message.messageTypeItems');
    });
    this.fieldsSlotMap['action'] = (text: MessageDto[], record: MessageDto, index: number) => {
      return (
        <div>
          <span class='mr-1'>
            <a-tooltip>
              <template slot='title'>
                {this.$l.getLocale('buttons.read')}
              </template>
              <span>
                <a-button type='link' on-click={() => this.view(record)} size='small' class={styles.list_button}>
                  {this.$l.getLocale('buttons.read')}
                </a-button>
              </span>
            </a-tooltip>
          </span>
          <span v-permission={this.actions.edit} class='mr-1'>
            <a-tooltip>
              <template slot='title'>
                {this.$l.getLocale('buttons.reset')}
              </template>
              <a-popconfirm title={this.$t('messages.reset')} on-confirm={() => this.resetMessage(record.msgId)}>
                <span style='color: red; cursor: pointer;'>
                  {this.$l.getLocale('buttons.reset')}
                </span>
              </a-popconfirm>
            </a-tooltip>
          </span>
        </div>
      );
    };
    this.fieldsSlotMap['msgType'] = (text: MessageDto[], record: MessageDto, index: number) => {
      return this.messageTypeDataset.find((d: any) => d.value === record.msgType).label;
    };
    this.fieldsSlotMap['sendingStatus'] = (text: MessageDto[], record: MessageDto, index: number) => {
      return this.sendStatusDataset.find((d: any) => d.value === record.sendingStatus).label;
    };
    this.loadData(true);
  }

  render() {
    return (
      <div>
        <comp-card class={styles.card_top}>
          <comp-table-header
            on-search={() => {
              this.loadData(true);
            }}
            on-reset={() => {
              this.reset();
            }}
          >
            <template slot='base'>
              <a-row>
                <a-col span='5' class='mr-1'>
                  <a-form-item  label={this.$t('platform.message.keyValue')}>
                      <a-input v-model={this.query.keyValue} placeholder={this.$t('platform.message.keyValue')}></a-input>
                  </a-form-item>
                </a-col>
                <a-col span='5' class='mr-1'>
                  <a-form-item  label={this.$t('platform.message.category')}>
                      <a-input v-model={this.query.category} placeholder={this.$t('platform.message.category')}></a-input>
                  </a-form-item>
                </a-col>
                <a-col span='5' class='mr-1'>
                  <a-form-item  label={this.$t('platform.message.msgType')}>
                    <a-select v-model={this.query.msgType} placeholder={this.$t('platform.message.msgType')} allowClear>
                        {this.messageTypeDataset.map((item: any) => (
                          <a-select-option value={item.value}>{item.label}</a-select-option>
                        ))}
                      </a-select>
                  </a-form-item>
                </a-col>
                <a-col span='5' class='mr-1'>
                  <a-form-item  label={this.$t('platform.fields.status')}>
                    <a-select v-model={this.query.sendingStatus} placeholder={this.$t('platform.fields.status')} allowClear>
                        {this.sendStatusDataset.map((item: any) => (
                          <a-select-option value={item.value}>{item.label}</a-select-option>
                        ))}
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
            </template>
          </comp-table-header>
        </comp-card>
        <comp-card>
          <a-card class={styles.base_table} bordered={false}>
            <a-table
              size='small'
              columns={this.columns}
              data-source={this.dataSource}
              pagination={this.pagination}
              scopedSlots={this.fieldsSlotMap}
              on-change={(pagination: any) => {
                this.pagination = pagination;
                this.loadData(false);
              }}
            >
              <span slot='keyValue'>{this.$t('platform.message.keyValue')}</span>
              <span slot='msgType'>{this.$t('platform.message.msgType')}</span>
              <span slot='category'>{this.$t('platform.message.category')}</span>
              <span slot='fromSys'>{this.$t('platform.message.fromSys')}</span>
              <span slot='msgTitle'>{this.$t('platform.message.msgTitle')}</span>
              <span slot='sendingStatus'>{this.$t('platform.fields.status')}</span>
              <span slot='sendTime'>{this.$t('platform.message.sendTime')}</span>
              <span slot='tryTimes'>{this.$t('platform.message.tryTimes')}</span>
              <span slot='action'>{this.$t('platform.fields.operation')}</span>
            </a-table>
          </a-card>
        </comp-card>
        <a-modal
          width='800px'
          title={this.$l.getLocale(['buttons.read', 'platform.message.message'])}
          visible={this.viewShow}
          destroyOnClose={true}
          maskClosable={false}
          on-cancel={this.cancel}
        >
          <template slot='footer'>
            <a-button
              type='primary'
              on-click={() => this.cancel()}
              class={styles.common_btn}
            >
              {this.$t('buttons.cancel')}
            </a-button>
          </template>
          <a-form labelCol={{ span: 4 }} wrapperCol={{ span: 18 }}>
            <a-row>
              <a-col span='12'>
                <a-form-item label={this.$t('platform.message.keyValue')}>
                  {this.currentData.keyValue}
                </a-form-item>
              </a-col>
              <a-col span='12'>
                <a-form-item label={this.$t('platform.message.msgType')}>
                  {this.currentData.msgType !== undefined ?
                    this.messageTypeDataset.find((d: any) => d.value === this.currentData.msgType).label : ''}
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col span='12'>
                <a-form-item label={this.$t('platform.message.category')}>
                  {this.currentData.category}
                </a-form-item>
              </a-col>
              <a-col span='12'>
                <a-form-item label={this.$t('platform.message.fromSys')}>
                  {this.currentData.fromSys}
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col span='12'>
                <a-form-item label={this.$t('platform.message.msgTitle')}>
                  {this.currentData.msgTitle}
                </a-form-item>
              </a-col>
              <a-col span='12'>
                <a-form-item label={this.$t('platform.message.priority')}>
                  {this.currentData.priority}
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col span='12'>
                <a-form-item label={this.$t('platform.message.sendTime')}>
                  {this.currentData.sendTime}
                </a-form-item>
              </a-col>
              <a-col span='12'>
                <a-form-item label={this.$t('platform.message.sendingStatus')}>
                  {this.currentData.sendingStatus !== undefined ?
                    this.sendStatusDataset.find((d: any) => d.value === this.currentData.sendingStatus).label : ''}
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col span='24'>
                <a-form-item label={this.$t('platform.message.msgTitle')} labelCol={{ span: 2 }} wrapperCol={{ span: 22 }}>
                  {this.currentData.msgTitle}
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col span='24'>
                <a-form-item label={this.$t('platform.message.sendTo')} labelCol={{ span: 2 }} wrapperCol={{ span: 22 }}>
                  {this.currentData.sendTo}
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col span='24'>
                <a-form-item label={this.$t('platform.message.msgBody')} labelCol={{ span: 2 }} wrapperCol={{ span: 22 }}>
                  <a-textarea v-model={this.currentData.msgBody} auto-size={{ minRows: 10, maxRows: 15 }}>
                  </a-textarea>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-modal>
      </div>
    );
  }
}
