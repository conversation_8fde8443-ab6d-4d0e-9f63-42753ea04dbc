import { i18nHelper } from '@/PlatformStandard/common/utils';
import { WrappedFormUtils } from 'ant-design-vue/types/form/form';
import { Component, Prop, Watch, Vue } from 'vue-property-decorator';

@Component({ components: {} })
export class EditHoliday extends Vue {
  private visible = false;
  private form!: WrappedFormUtils;
  private _parentObj: any;
  @Prop() parentObj: any;
  @Prop() data: any;
  private date = '';
  private holiday: any;
  private statusDataset = i18nHelper.getLocaleObject('platform.holiday.statusDataset');
  /* @Watch('parentObj', { deep: true }) parentObjChange(v: any) {

  }

  @Watch('date', { deep: true }) dataChange(v: any) {

  } */
  created() {
    this.statusDataset = i18nHelper.getLocaleObject('platform.holiday.statusDataset');
    this.form = this.$form.createForm(this, { name: 'dateForm' });
    this.holiday = JSON.parse(JSON.stringify(this.data));
  }

  openForm(data: any) {
    this.date = data.holidayDate;
    this.holiday = data;
    this.visible = true;
  }

  save() {
    this.parentObj.holidaySave(this.holiday);
    this.visible = false;
  }

  render() {
    return (
        <div>
            <a-modal
                title={this.$t('platform.holiday.maintain')}
                width='600px'
                dialog-style={{ top: '10px' }}
                bodyStyle={{ padding: '0 10px', height: '360px', overflow: 'auto' }}
                visible={this.visible}
                on-ok={this.save}
                on-cancel={() => {
                this.visible = false;
                }}
            >
                <a-form form={this.form} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
                    <a-form-item label={this.$t('platform.holiday.date')}>
                        <a-input value={this.date}
                            disabled={true}></a-input>
                    </a-form-item>
                    <a-form-item label={this.$t('platform.holiday.type')}>
                        <a-select v-model={this.holiday.status}>
                            {this.statusDataset.map((item: any) => (
                                <a-select-option value={item.value}>{item.label}</a-select-option>
                            ))}
                        </a-select>
                    </a-form-item>
                    <a-form-item label={this.$t('platform.fields.description')}>
                        <a-textarea v-model={this.holiday.remark} rows={8}
                            placeholder={this.$l.getLocale(['controls.input', 'platform.fields.description'])}></a-textarea>
                    </a-form-item>
                </a-form>
            </a-modal>
        </div>
    );
  }
}
