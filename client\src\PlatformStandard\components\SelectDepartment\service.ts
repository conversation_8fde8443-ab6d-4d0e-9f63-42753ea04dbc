import { Observable } from 'rxjs';
import { OrganizationDto } from './types';
import { httpHelper } from '@/PlatformStandard/common/utils';

class DepartmentService {
  getDepartmentTree(params: any): Observable<any[]> {
    const _url = `/api/platform/v1/manage/tree-organizations`;
    return httpHelper.get(_url, { params });
  }

  getDepartmentList(id: string): Observable<any[]> {
    const _url = `/api/platform/v1/manage/tree-organizations?parent-id=${id}`;
    return httpHelper.get(_url);
  }

  searchOrganizations(params: any) {
    const _url = '/api/platform/v1/manage/search-organizations';
    return httpHelper.get(_url, { params });
  }
}

export const departmentService = new DepartmentService();
