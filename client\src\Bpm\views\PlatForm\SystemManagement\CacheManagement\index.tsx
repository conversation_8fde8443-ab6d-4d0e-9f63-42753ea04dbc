import { Component, Vue } from 'vue-property-decorator';
import { CompCard } from '@/PlatformStandard/components';
import styles from './CacheManagement.module.less';
import { cacheService } from '@/Bpm/services/cache';
import { CacheDto } from './types';
import { i18nHelper, notificationHelper } from '@/PlatformStandard/common/utils';

@Component({ components: { CompCard } })
export class CacheManage extends Vue {
  private dataSource: CacheDto[] = [];

  private columns = [
    {
      dataIndex: 'cacheCode',
      slots: { title: 'cacheCode' },
      width: '20%'
    },
    {
      dataIndex: 'desc',
      slots: { title: 'desc' },
    },
    {
      dataIndex: 'createTime',
      slots: { title: 'createTime' },
      scopedSlots: { customRender: 'createTime' },
      width: '15%'
    },
    {
      dataIndex: 'action',
      slots: { title: 'action' },
      scopedSlots: { customRender: 'action' },
      width: '10%'
    },
  ];

  private fieldsSlotMap: any = {};

  private loadData() {
    cacheService.getCacheList().subscribe(data => {
      this.dataSource = data;
      this.$forceUpdate();
    });
  }

  private refreshItem(record: CacheDto) {
    if (record.cacheCode === 'db-process-map') {
      cacheService.refreshDbProcessMap().subscribe(() => {
        notificationHelper.success(i18nHelper.getLocale('messages.success'));
        this.loadData();
      });
    } else {
      cacheService.refreshCacheItem(record.cacheCode).subscribe(() => {
        notificationHelper.success(i18nHelper.getLocale('messages.success'));
        this.loadData();
      });
    }
  }

  private initSystemCache() {
    cacheService.initSystemCache().subscribe(() => {
      cacheService.refreshDbProcessMap().subscribe(() => {
        notificationHelper.success(i18nHelper.getLocale('messages.success'));
        this.loadData();
      });
    });
  }

  created() {
    this.loadData();
    this.fieldsSlotMap['action'] = (text: CacheDto[], record: CacheDto, index: number) => {
      return (
        <div>
          <span>
            <a-button type='link' on-click={() => this.refreshItem(record)} size='small' class={styles.list_button}>
              {this.$l.getLocale('buttons.refresh')}
            </a-button>
          </span>
        </div>
      );
    };

    this.fieldsSlotMap['createTime'] = (text: string, record: CacheDto, index: number) => {
      return (text === null ? <span style='margin-left: 40%;'>/</span> : text);
    };
  }

  render() {
    return (
      <div>
        <comp-card>
          <a-card class={styles.base_table} bordered={false}>
            <div slot='extra'>
              <a-button type='primary' class={styles.common_btn}
                on-click={() => this.loadData()}>{this.$t('buttons.refresh')}</a-button>
              <a-button type='primary' class={styles.common_btn}
                on-click={() => this.initSystemCache()}>{this.$t('platform.cacheManage.initSystemCache')}</a-button>
            </div>
            <a-table
              size='small'
              pagination={false}
              columns={this.columns}
              data-source={this.dataSource}
              scopedSlots={this.fieldsSlotMap}
            >
              <span slot='cacheCode'>{this.$t('platform.cacheManage.cacheCode')}</span>
              <span slot='desc'>{this.$t('platform.cacheManage.desc')}</span>
              <span slot='createTime'>{this.$t('platform.cacheManage.createTime')}</span>
              <span slot='action'>{this.$t('platform.fields.operation')}</span>
            </a-table>
          </a-card>
        </comp-card>
      </div>
    );
  }
}
