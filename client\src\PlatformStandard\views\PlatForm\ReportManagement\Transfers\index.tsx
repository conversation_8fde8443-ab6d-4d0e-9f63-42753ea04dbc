import { dateHelper, form<PERSON><PERSON>per, i18n<PERSON>elper, notificationHelper } from '@/PlatformStandard/common/utils';
import { WrappedFormUtils } from 'ant-design-vue/types/form/form';
import { Component, Vue, Watch } from 'vue-property-decorator';

import styles from './transfers.module.less';
import banner from '@/assets/images/banner.png';

@Component
export class Transfers extends Vue {
  private leftHeight = 500;
  private iframeSrc = '';
  private iframeIf = true;
  private iframeHtml = '';

  // 监听路由iframeSrc参数是否变化
  @Watch('$route') onValueChange(to: any, from: any) {
    if (to.query['iframeSrc'] !== from.query['iframeSrc'] && to.query['iframeSrc'] != null) {
      this.iframeSrc = decodeURIComponent(to.query['iframeSrc'] as string);
      this.iframeHtml = this.getIframeHtml(this.iframeSrc);
    }
  }

  created() {
    this.iframeSrc = decodeURIComponent(this.$route.query['iframeSrc'] as string);
    this.iframeHtml = this.getIframeHtml(this.iframeSrc);
  }

  mounted() {
    // 微调页面，使得iframe嵌入的时候美观一些
    this.leftHeight = document.documentElement.offsetHeight - 160;
    this.iframeHtml = this.getIframeHtml(this.iframeSrc);
  }

  getIframeHtml(src: string): string {
    return `<iframe src='${src}' style='border:0px;padding:0px;margin:0px;width:100%;height:${this.leftHeight}px'></iframe>`;
  }

  render(h: any, data: any) {
    return h('div', {
      domProps: {
        innerHTML: this.iframeHtml // 这里是要渲染的数据
      }
    });
  }
}
