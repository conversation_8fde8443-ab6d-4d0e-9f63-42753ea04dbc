import { Observable } from 'rxjs';
import { httpHelper } from '@/PlatformStandard/common/utils';
import { ValueLabelPair } from '@/PlatformStandard/common/defines';
import { AppMenuLevelDto } from './types';

class MenuService {
  /**
   * 获取菜单列表
   */
  getMenus(): Observable<any> {
    const url = `/api/platform/v1/manage/menus`;
    return httpHelper.get(url);
  }

  /**
   * 获取数据权限
   */
  getDataPermissions(): Observable<any> {
    const url = `/api/platform/v1/manage/data-permissions`;
    return httpHelper.get(url);
  }

  /**
   * 更新菜单
   * @param id 菜单ID
   */
  updateMenu(id: string, data: any): Observable<void> {
    const url = `/api/platform/v1/manage/menus/${id}`;
    return httpHelper.put(url, data);
  }

  /**
   * 添加菜单
   * @param data 菜单数据
   */
  addMenu(data: any): Observable<void> {
    const url = `/api/platform/v1/manage/menus`;
    return httpHelper.post(url, data);
  }

  /**
   * 删除菜单
   * @param id 菜单ID
   */
  deleteMenu(id: string): Observable<void> {
    const url = `/api/platform/v1/manage/menus/${id}`;
    return httpHelper.delete(url);
  }

  /**
   * 更新菜单序号
   * @param id 菜单Id
   * @param relyId 依赖菜单Id
   */
  updateMenuOrder(id: string, relyId: string): Observable<void> {
    const url = `/api/platform/v1/manage/menus/${id}/order`;
    return httpHelper.put(url, { id: relyId });
  }

  /**
   * 获取应用选择列表
   */
  getApplications(): Observable<ValueLabelPair[]> {
    const url = `/api/platform/v1/manage/select-applications`;
    return httpHelper.get(url);
  }

  /**
   * 获取应用菜单最大层级
   * @returns 菜单最大层级实体集合
   */
  getApplicationMenuLevel(): Observable<AppMenuLevelDto[]> {
    const url = `/api/platform/v1/manage/application-menu-level`;
    return httpHelper.get(url);
  }
}

export const menuService = new MenuService();
