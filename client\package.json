{"name": "mt-enterprise-web-customer", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build", "lint": "vue-cli-service lint", "i18n:report": "vue-cli-service i18n:report --src './src/**/*.?(js|vue)' --locales './src/locales/**/*.json'"}, "dependencies": {"@babel/polyfill": "^7.4.4", "@babel/runtime": "^7.4.4", "@types/lodash": "~4.14.119", "ant-design-vue": "1.7.2", "axios": "^0.19.0", "lodash": "~4.17.11", "compression-webpack-plugin": "^5.0.1", "core-js": "^2.6.5", "crypto-js": "^3.1.9-1", "mockjs": "^1.0.1-beta3", "oidc-client": "^1.10.1", "rxjs": "^6.5.2", "socket.io-client": "^2.2.0", "vue": "^2.6.10", "vue-class-component": "^7.0.2", "vue-i18n": "^8.12.0", "vue-infinite-scroll": "^2.0.2", "vue-property-decorator": "^8.1.0", "vue-router": "^3.0.3", "vue-smooth-dnd": "^0.8.0", "vue-template-compiler": "^2.6.12", "vue-virtual-scroller": "0.12.2", "vuedraggable": "^2.24.3", "xe-utils": "^3.1.7", "vue-clipboard2": "^0.3.3"}, "devDependencies": {"@babel/plugin-syntax-dynamic-import": "^7.2.0", "@babel/plugin-transform-runtime": "^7.4.4", "@types/clipboard": "^2.0.1", "@types/crypto-js": "^3.1.43", "@types/mockjs": "^1.0.2", "@types/socket.io-client": "^1.4.32", "@types/webpack": "^4.4.0", "@vue/cli": "^5.0.8", "@vue/cli-plugin-babel": "^3.7.0", "@vue/cli-plugin-typescript": "^3.7.0", "@vue/cli-service": "^3.7.0", "babel-plugin-import": "^1.11.0", "less": "^2.7.3", "less-loader": "^4.1.0", "typescript": "^3.4.3", "vue-cli-plugin-ant-design": "^1.0.0", "vue-cli-plugin-i18n": "^0.6.0", "vue-svg-loader": "^0.12.0", "vue-template-compiler": "^2.6.12"}}