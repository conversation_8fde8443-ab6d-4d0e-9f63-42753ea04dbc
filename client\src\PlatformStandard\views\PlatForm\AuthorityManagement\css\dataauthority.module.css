/*
变量参考 https://github.com/vueComponent/ant-design-vue/blob/master/components/style/themes/default.less
或 https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/components/style/themes/default.less
*/
/* 重写 AntDesign 的主题样式 */
/* 重写 AntDesign 结束 */
/* 自定义相关 */
/* 基础边距 */
.base_table :global(.ant-card-body) {
  padding: 0;
}
.list_button {
  padding: 0 3px !important;
}
.card_top {
  padding: 15px 0px 5px 0px !important;
  margin-bottom: 10px !important;
  width: 100%;
}
.card_head {
  margin: 0 !important;
  padding: 0 !important;
}
.row {
  margin: 0;
  padding: 0;
}
.operation {
  text-align: right;
  cursor: pointer;
}
.nowarp {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}
:global(.ant-card-extra) {
  padding: 0 !important;
}
.common_btn {
  padding: 0;
  margin: 0;
  width: 70px !important;
  height: 30px;
  text-align: center;
  line-height: 32px;
  border-radius: 5px !important;
  border: 1px solid #ddd !important;
  background-color: #fff !important;
  color: #333 !important;
  opacity: 1;
  text-shadow: none !important;
  box-shadow: none !important;
}
.searchBtn {
  width: 72px;
  height: 32px;
  opacity: 1;
  background: #2165d9 !important;
  text-align: center;
  line-height: 32px;
  border-radius: 5px !important;
  margin-right: 10px !important;
}
