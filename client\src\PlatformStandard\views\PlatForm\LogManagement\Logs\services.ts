import { Observable } from 'rxjs';

import { httpHelper } from '@/PlatformStandard/common/utils';

// import { LogDto } from './types';

class LogService {
  getloggerList(params: any): Observable<any> {
    const _url = '/api/platform/v1/manage/logs/loggers';
    return httpHelper.get(_url, { params });
  }

    getLogs(params: any): Observable<any> {
        const _url = '/api/platform/v1/manage/logs';
        return httpHelper.get(_url, { params });
    }

    // 获取日志模块
    getLogsLoggers(): Observable<any> {
        const _url = '/api/platform/v1/manage/logs';
        return httpHelper.get(_url);
    }
}
export const logService = new LogService();
