import { httpHelper } from '@/PlatformStandard/common/utils';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

class MessageService {
  /**
   * 获取消息列表
   * @param params 查询参数
   * @returns 消息列表
   */
   getMessageList(params: any): Observable<any> {
    const url = '/api/platform/v1/messages';
    return httpHelper.get(url, { params: params }).pipe(
      map(data => ({
        total: data.total,
        items: data.items.map((m: any) => ({
          ...m,
          key: m.msgId
        })),
      }))
    );
  }

  /**
   * 获取消息
   * @param messageId 消息Id
   * @returns 消息实体
   */
  getMessage(messageId: string): Observable<any> {
    const url = `/api/platform/v1/message/${messageId}`;
    return httpHelper.get(url);
  }

  /**
   * 重新消息发送状态和发送次数
   * @param messageId 消息Id
   * @returns 返回状态
   */
  resetMessage(messageId: string): Observable<any> {
    const url = `/api/platform//v1/reset-message/${messageId}`;
    return httpHelper.post(url);
  }
}
export const messageService = new MessageService();
