import { Component, Prop, Vue } from 'vue-property-decorator';
import { BaseControl } from '../base-control';

@Component({
})
export class MmtRadio extends BaseControl {
  get defaultLable() {
    if (this.value) {
      const item = this.controlConfig.config.options.find((f: any) => f.value === this.value);
      return item ? item.label : '';
    }
    return '';
  }
  created(): void {
  }
  render() {
    return (
      this.pageModel === 'view' ? (
        <div>{this.defaultLable}</div>
      ) : (
        <a-radio-group
          value={this.value}
          disabled={!this.controlConfig.config.disabled}
          options={this.controlConfig.config.options}
          style={{ width: '100%' }}
          on-change={(e: any) => this.onChange(e.target.value)}
        >
        </a-radio-group>
      ));
  }
}
