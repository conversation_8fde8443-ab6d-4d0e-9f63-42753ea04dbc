import { Observable } from 'rxjs';
import { downloadHelper, httpHelper } from '@/PlatformStandard/common/utils';
import { HolidayEntity } from './types';

class HolidayService {
    getHolidays(params: any): Observable<HolidayEntity[]> {
        const _url = `/api/platform/v1/manage/holidays`;
        return httpHelper.get(_url, { params });
    }
    updateHoliday(data: any): Observable<void> {
        const _url = `/api/platform/v1/manage/holiday`;
        return httpHelper.put(_url, data);
    }
    export(dto: any, fileName: string) {
        const url = `/api/platform/v1/manage/holiday/export`;
        return downloadHelper.post(url, fileName + '.xlsx', null, dto);
    }
    upload(data: FormData): Observable<any> {
        const url = '/api/platform/v1/manage/holiday/import';
        return httpHelper.post(url, data);
    }
}
export const holidayService = new HolidayService();
