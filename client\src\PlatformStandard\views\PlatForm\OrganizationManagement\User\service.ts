import { Observable } from 'rxjs';
import { httpHelper } from '@/PlatformStandard/common/utils';
import { map } from 'rxjs/operators';
import { RoleUserRelationDto, UserDto, UserPositionDto } from './types';

class UserService {
  /**
   * 获取角色列表
   * @param params 查询参数
   */
  getUserList(params: any): Observable<any> {
    const _url = '/api/platform/v1/manage/users';
    return httpHelper.get(_url, { params: params }).pipe(
      map(data => ({
        total: data.total,
        items: data.items.map((m: any) => ({
          ...m,
          key: m.id
        })),
      }))
    );
  }

  /**
   * 获取用户详情
   * @param userId 用户Id
   * @returns UserDto
   */
  getUserById(userId: string): Observable<UserDto> {
    const _url = `/api/platform/v1/manage/users/${userId}`;
    return httpHelper.get(_url);
  }

  /**
   * 获取组织
   * @param organizationId 组织Id
   * @returns 组织
   */
  getOrganization(organizationId: string): Observable<any> {
    const _url = `/api/platform/v1/manage/organization/${organizationId}`;
    return httpHelper.get(_url);
  }

  /**
   * 添加用户
   * @param user 用户对象
   * @returns userId
   */
  addUser(user: UserDto): Observable<any> {
    const _url = `/api/platform/v1/manage/users`;
    return httpHelper.post(_url, user);
  }

  /**
   * 更新用户
   * @param userId 用户Id
   * @param user 用户对象
   */
  updateUser(userId: string, user: UserDto): Observable<any> {
    const _url = `/api/platform/v1/manage/users/${userId}`;
    return httpHelper.put(_url, user);
  }

  /**
   * 保存用户组织关系
   * @param userPosition 用户组织关系对象
   */
  savePositionAndUser(userPosition: UserPositionDto): Observable<any> {
    const _url = '/api/platform/v1/manage/organization-position-user';
    return httpHelper.post(_url, userPosition);
  }

  /**
   * 删除岗位和用户关系
   * @param positionId 岗位Id
   */
  deletePositionAndUser(positionId: string): Observable<any> {
    const _url = `/api/platform/v1/manage/organization-position-user/${positionId}`;
    return httpHelper.delete(_url);
  }

  /**
   * 根据用户Id获取用户组织关系
   * @param userId 用户Id
   * @returns 用户组织关系
   */
  getOrganizationPositionsByUserId(userId: string): Observable<any> {
    const _url = `/api/platform/v1/manage/organization-positions/${userId}`;
    return httpHelper.get(_url);
  }

  /**
   * 添加角色成员
   * @param member 成员对象
   */
  addMember(members: RoleUserRelationDto[]): Observable<void> {
    const path = `/api/platform/v1/manage/role-members`;
    return httpHelper.post(path, members);
  }

  /**
   * 获取角色列表
   * @param params 查询参数
   */
  getRoles(params: any): Observable<any> {
    const _url = `/api/platform/v1/manage/roles`;
    return httpHelper.get(_url, { params: params }).pipe(
      map(data => ({
        total: data.total,
        items: data.items.map((m: any) => ({
          ...m,
          key: m.roleId
        })),
      }))
    );
  }

  /**
   * 根据用户Id获取用户所在角色
   * @param userId 用户Id
   * @returns roles
   */
  getRolesByUserId(userId: string): Observable<any> {
    const _url = `/api/platform/v1/manage/roles/${userId}`;
    return httpHelper.get(_url);
  }

  /**
   * 删除成员
   * @param type 成员类型
   */
  deleteUserRole(userId: string, roleId: string): Observable<void> {
    const path = `/api/platform/v1/manage/role-user/${userId}/${roleId}`;
    return httpHelper.delete(path);
  }

  getUsers(kw: string): Observable<any> {
    const _url = `/api/platform/v1/select-users`;
    return httpHelper.get(_url, { params: { keyword: kw, count: '20' } }, { loading: false }).pipe(
      map(data =>
        data.map((item: any) => ({
          value: item.value,
          label: `${item.label}(${item.account})`
        }))
      )
    );
  }

}
export const userService = new UserService();
