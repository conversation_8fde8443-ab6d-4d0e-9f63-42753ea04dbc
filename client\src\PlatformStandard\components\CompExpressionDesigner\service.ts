import { httpHelper } from '@/PlatformStandard/common/utils';
import { Observable } from 'rxjs';

class CompExpressionDesignerService {
    // 获取业务对象详细
    getBusinessObjectDetail(code: string): Observable<any> {
      const url = `/api/process/v1/business-objects/${code}/select-fields`;
      return httpHelper.get(url);
  }

  // 检查表达式
  checkExpression(expression: string, businessObjectCode: string): Observable<any> {
      const url = '/api/process/v1/manage/expression/check';
      return httpHelper.post(url, {
          expression: expression,
          businessObjectCode: businessObjectCode
      });
  }
}
export const expressionDesignerService = new CompExpressionDesignerService();
