import { form<PERSON><PERSON>per, i18n<PERSON>elper, notification<PERSON>elper, toCasedStyleObject } from '@/PlatformStandard/common/utils';
import { CompCard, CompTableHeader } from '@/PlatformStandard/components';
import { ActionEnum } from '@/PlatformStandard/services/menu';
import { WrappedFormUtils } from 'ant-design-vue/types/form/form';
import { Component, Vue } from 'vue-property-decorator';
import { tableRelationService } from './service';
import { TableRelationDto, TableRelationQueryDto } from './types';
import styles from './TableRelations.module.less';

@Component({ components: { CompCard, CompTableHeader } })
export class TableRelations extends Vue {
  private pagination = {
    total: 0,
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    size: 'default',
    showTotal: (total: string) =>
      this.$l
        .getLocale('paginations.total')
        .toString()
        .replace('{{value}}', total),
  };
  private query: TableRelationQueryDto = {};
  private dataSource: any;
  private fieldsSlotMap: any = {};
  private statusDataset = i18nHelper.getLocaleObject('platform.dataPermission.statusDataset');
  private columns = [
    {
      dataIndex: 'tableName',
      slots: { title: 'tableName' },
      width: '20%'
    },
    {
      dataIndex: 'columnName',
      slots: { title: 'columnName' },
      width: '20%'
    },
    {
      dataIndex: 'targetTableName',
      slots: { title: 'targetTableName' },
      width: '20%'
    },
    {
      dataIndex: 'targetColumnName',
      slots: { title: 'targetColumnName' },
      width: '20%'
    },
    {
      dataIndex: 'status',
      key: 'status',
      slots: { title: 'status' },
      scopedSlots: { customRender: 'status' },
      width: '10%'
    },
    {
      dataIndex: 'action',
      slots: { title: 'action' },
      scopedSlots: { customRender: 'action' },
      width: '10%'
    },
  ];

  private editShow = false;
  private actions = ActionEnum;
  private opration!: string;
  private form!: WrappedFormUtils;
  private currentData: TableRelationDto = {};

  private loadData(reset: boolean = false) {
    if (reset) {
      this.pagination.current = 1;
    }
    const params = { 'page-index': this.pagination.current, 'page-size': this.pagination.pageSize };
    tableRelationService.getTableRelationList({ ...toCasedStyleObject(this.query), ...params }).subscribe(data => {
      this.dataSource = data.items;
      this.pagination.total = data.total;
      this.$forceUpdate();
    });
  }

  private reset() {
    this.query = {};
  }

  private edit(record: TableRelationDto | null) {
    if (record) {
      this.currentData = JSON.parse(JSON.stringify(record));
      this.opration = 'edit';
    } else {
      this.currentData = {};
      this.opration = 'add';
    }
    this.editShow = true;
  }

  private delete(relationId: number | undefined) {
    if (relationId !== undefined) {
      tableRelationService.deleteTableRelation(relationId).subscribe(() => {
        notificationHelper.success(i18nHelper.getLocale('messages.success'));
        this.loadData(true);
      });
    }
  }

  private save() {
    const errMsg = formHelper.validateForm(this.form);
    if (errMsg && errMsg.length > 0) {
      notificationHelper.error(errMsg);
      return;
    }

    tableRelationService.saveTableRelation(this.currentData).subscribe(() => {
      notificationHelper.success(i18nHelper.getLocale('messages.success'));
      this.cancel();
      this.loadData(true);
    });
  }

  private cancel() {
    this.editShow = false;
    this.currentData = {};
  }

  created() {
    this.form = this.$form.createForm(this, { name: 'tableForm' });
    this.fieldsSlotMap['action'] = (text: TableRelationDto[], record: TableRelationDto, index: number) => {
      return (
        <div>
          <span v-permission={this.actions.edit} class='mr-1'>
            <a-tooltip>
              <template slot='title'>
                {this.$l.getLocale('buttons.edit')}
              </template>
               {/* <a-icon
                type='edit'
                class='text-primary'
                on-click={() => this.edit(record)} /> */}
              <span v-permission={this.actions.edit}>
                <a-button type='link' on-click={() => this.edit(record)} size='small' class={styles.list_button}>
                  {this.$l.getLocale('buttons.edit')}
                </a-button>
              </span>
            </a-tooltip>
          </span>
          <span v-permission={this.actions.delete} class='mr-1'>
            <a-tooltip>
              <template slot='title'>
                {this.$l.getLocale('buttons.delete')}
              </template>
              <a-popconfirm title={this.$t('messages.delete')} on-confirm={() => this.delete(record.relationId)}>
                {/* <a-icon
                  type='delete'
                  class='text-primary'
                  style='color: red; cursor: pointer;'
                /> */}
                <span v-permission={this.actions.delete} style='color: red; cursor: pointer;'>
                  {this.$l.getLocale('buttons.delete')}
                </span>
              </a-popconfirm>
            </a-tooltip>
          </span>
        </div>
      );
    };
    this.fieldsSlotMap['status'] = (text: TableRelationDto[], record: TableRelationDto, index: number) => {
      return record.status === 1 ? this.$l.getLocale('platform.commonRole.valid') : this.$l.getLocale('platform.commonRole.invalid');
    };
    this.loadData(true);
  }

  render() {
    return (
      <div>
        <comp-card  class={styles.card_top}>
          <comp-table-header
            on-search={() => {
              this.loadData(true);
            }}
            on-reset={() => {
              this.reset();
            }}
          >
            <template slot='base'>
              <a-row>
                <a-col span='6' class='mr-1'>
                  <a-form-item label={this.$t('platform.tableRelations.tableName')}>
                    <a-input v-model={this.query.tableName} placeholder={this.$t('platform.tableRelations.tableName')}></a-input>
                  </a-form-item>
                </a-col>
                <a-col span='6' class='mr-1'>
                  <a-form-item label={this.$t('platform.tableRelations.targetTableName')}>
                    <a-input v-model={this.query.targetTableName}
                     placeholder={this.$t('platform.tableRelations.targetTableName')}></a-input>
                  </a-form-item>
                </a-col>
                <a-col span='6' class='mr-1'>
                <a-form-item label={this.$t('platform.fields.status')}>
                  <a-select v-model={this.query.status} placeholder={this.$t('platform.fields.status')}>
                    {this.statusDataset.map((item: any) => (
                      <a-select-option value={item.value}>{item.label}</a-select-option>
                    ))}
                  </a-select>
                </a-form-item>
                </a-col>
              </a-row>
            </template>
          </comp-table-header>
        </comp-card>
        <comp-card>
          <a-card class={styles.base_table} bordered={false}>
            <div slot='extra'>
              <a-button
                type='primary'
                v-permission={this.actions.add}
                on-click={() => this.edit(null)}
                class={styles.common_btn}
              >
                {this.$t('buttons.add')}
              </a-button>
            </div>
            <a-table
              size='small'
              columns={this.columns}
              data-source={this.dataSource}
              pagination={this.pagination}
              scopedSlots={this.fieldsSlotMap}
              on-change={(pagination: any) => {
                this.pagination = pagination;
                this.loadData(false);
              }}
            >
              <span slot='tableName'>{this.$t('platform.tableRelations.tableName')}</span>
              <span slot='columnName'>{this.$t('platform.tableRelations.columnName')}</span>
              <span slot='targetTableName'>{this.$t('platform.tableRelations.targetTableName')}</span>
              <span slot='targetColumnName'>{this.$t('platform.tableRelations.targetColumnName')}</span>
              <span slot='status'>{this.$t('platform.fields.status')}</span>
              <span slot='action'>{this.$t('platform.fields.operation')}</span>
            </a-table>
          </a-card>
        </comp-card>
        <a-modal
          width='800px'
          title={this.opration === 'add' ?
            this.$l.getLocale(['buttons.add', 'platform.tableRelations.tableRelation'])
            :
            this.$l.getLocale(['buttons.edit', 'platform.tableRelations.tableRelation'])}
          visible={this.editShow}
          destroyOnClose={true}
          maskClosable={false}
          on-cancel={this.cancel}
          on-ok={this.save}
        >
          <a-form form={this.form} labelCol={{ span: 5 }} wrapperCol={{ span: 16 }}>
            <a-row>
              <a-col span='12'>
                <a-form-item label={this.$t('platform.tableRelations.tableName')} required>
                  <a-input on-change={(e: any) => { this.currentData.tableName = e.target.value; }}
                    placeholder={this.$l.getLocale(['controls.input', 'platform.tableRelations.tableName'])}
                    v-decorator={['tableName', {
                      initialValue: this.currentData.tableName,
                      rules: [{ required: true, message: this.$l.getLocale(['controls.input', 'platform.tableRelations.tableName']) }]
                    }]} />
                </a-form-item>
              </a-col>
              <a-col span='12'>
                <a-form-item label={this.$t('platform.tableRelations.columnName')} required>
                  <a-input on-change={(e: any) => { this.currentData.columnName = e.target.value; }}
                    placeholder={this.$l.getLocale(['controls.input', 'platform.tableRelations.columnName'])}
                    v-decorator={['columnName', {
                      initialValue: this.currentData.columnName,
                      rules: [{ required: true, message: this.$l.getLocale(['controls.input', 'platform.tableRelations.columnName']) }]
                    }]} />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col span='12'>
                <a-form-item label={this.$t('platform.tableRelations.targetTableName')} required>
                  <a-input on-change={(e: any) => { this.currentData.targetTableName = e.target.value; }}
                    placeholder={this.$l.getLocale(['controls.input', 'platform.tableRelations.targetTableName'])}
                    v-decorator={['targetTableName', {
                      initialValue: this.currentData.targetTableName,
                      rules: [{ required: true, message: this.$l.getLocale(['controls.input', 'platform.tableRelations.targetTableName']) }]
                    }]} />
                </a-form-item>
              </a-col>
              <a-col span='12'>
                <a-form-item label={this.$t('platform.tableRelations.targetColumnName')} required>
                  <a-input on-change={(e: any) => { this.currentData.targetColumnName = e.target.value; }}
                    placeholder={this.$l.getLocale(['controls.input', 'platform.tableRelations.targetColumnName'])}
                    v-decorator={['targetColumnName', {
                      initialValue: this.currentData.targetColumnName,
                      rules: [
                        {
                          required: true,
                          message: this.$l.getLocale(['controls.input', 'platform.tableRelations.targetColumnName'])
                        }
                      ]
                    }]} />
                </a-form-item>
              </a-col>
            </a-row>
            {
              this.opration === 'edit' ?
                <a-row>
                  <a-col span='12'>
                    <a-form-item label={this.$t('platform.fields.status')} required>
                      <a-select
                        options={this.statusDataset}
                        placeholder={this.$l.getLocale(['controls.select', 'platform.fields.status'])}
                        style='width:100%;'
                        on-change={(value: any) => { this.currentData.status = value; }}
                        v-decorator={[
                          'status',
                          {
                            initialValue: this.currentData.status,
                            rules: [{
                              required: true,
                              message: this.$l.getLocale(['controls.select', 'platform.fields.status'])
                            }]
                          }]}
                      >
                      </a-select>
                    </a-form-item>
                  </a-col>
                </a-row>
                :
                null
            }
          </a-form>
        </a-modal>
      </div>
    );
  }
}
