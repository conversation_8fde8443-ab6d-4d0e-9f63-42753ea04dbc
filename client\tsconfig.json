{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "experimentalDecorators": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "baseUrl": ".", "types": ["webpack-env", "webpack"], "lib": ["esnext", "dom", "dom.iterable", "scripthost"], "paths": {"@/*": ["./src/*"]}}, "include": ["./src/**/*.ts", "./src/**/*.tsx", "./src/**/*.vue", "./tests/**/*.ts", "./tests/**/*.tsx"], "exclude": ["./node_modules"]}