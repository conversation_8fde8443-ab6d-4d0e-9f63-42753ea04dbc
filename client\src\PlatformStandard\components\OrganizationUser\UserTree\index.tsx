import { Component, Vue, Prop, Emit, Watch } from 'vue-property-decorator';

import { ValueLabelPair } from '@/PlatformStandard/common/defines';
import { organizationUserService } from '../service';
import { TreeItem } from '../types';

@Component
export class UserTree extends Vue {
  @Prop() value!: ValueLabelPair[] | ValueLabelPair | undefined | null;
  @Prop({ default: true }) multiple!: boolean;
  @Prop() placeholder!: string;

  private parentId = '';
  private organizationTreeData: TreeItem[] = [];

  @Emit('change')
  idEmit(value: TreeItem[] | TreeItem) {}

  created(): void {
    organizationUserService.getOrganizationsTree('').subscribe((data: TreeItem[]) => {
      this.organizationTreeData = data.map(m => ({ ...m, selectable: false }));
    });
  }

  private onChange(value: TreeItem[] | TreeItem): void {
    this.idEmit(value);
  }

  private onLoadOrganizationTreeData(treeNode: any) {
    const { value } = treeNode.dataRef;
    this.parentId = value;
    return new Promise<void>(resolve => {
      if (treeNode.dataRef.children) {
        resolve();
        return;
      }
      organizationUserService.getOrganizationsTree(value).subscribe((data: TreeItem[]) => {
        if (!data || data.length === 0) {
          organizationUserService.getUsersByOrganization(this.parentId).subscribe((userData: ValueLabelPair[]) => {
            treeNode.dataRef.children = userData.map(m => ({ ...m, isLeaf: true, selectable: true }));
            this.organizationTreeData = [...this.organizationTreeData];
            resolve();
          });
        } else {
          treeNode.dataRef.children = data.map(m => ({ ...m, selectable: false }));
          organizationUserService.getUsersByOrganization( this.parentId).subscribe((userData: ValueLabelPair[]) => {
            treeNode.dataRef.children = [...treeNode.dataRef.children, ...userData.map(m => ({ ...m, isLeaf: true, selectable: true }))];
            this.organizationTreeData = [...this.organizationTreeData];
            resolve();
          });
        }
      });
    });
  }

  render(): JSX.Element {
    return (
      <a-tree
        showIcon
        treeData={this.organizationTreeData}
        loadData={this.onLoadOrganizationTreeData}
        on-select={(selectedKeys: string[], e: { selected: boolean; selectedNodes: any[]; node: any }) => {
          this.onChange(e.node ? e.node.dataRef : {});
        }}
        allowClear={true}
        scopedSlots={{
          icon: (value: any) => {
            return <a-icon type={value.isLeaf ? 'user' : 'cluster'} />;
          },
        }}
      />
    );
  }
}
