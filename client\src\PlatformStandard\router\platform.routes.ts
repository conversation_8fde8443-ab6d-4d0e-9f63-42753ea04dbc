import { RouteConfig } from 'vue-router';
import { routerGuard, routerLoginGuard, routerLogoutGuard, routerPermissionGuard } from '../common/utils';
import { MainLayout } from '@/layout/MainLayout';
import { UnAuthorized } from '../views/401';
import { Login } from '../views/Login';
import { LogManagementRoutes } from './platform/log-management/log-management.routes';
import { OrganizationManagementRoutes } from './platform/organization-management/organization-management.router';
import { SystemManagementRoutes } from './platform/system-management/system-management.routes';
import { ProductManagementRoutes } from './platform/product-management/product-management.routes';
import { HomeManagementRoutes } from './platform/home-management/home-management.routes';
import { MessageCenterRoutes } from './platform/message-center/message-center.routes';
import { PageModelingView } from '../views/ModelingView';
import { AuthorityManagementRoutes } from './platform/authority-management/authority-management.routes';
import { ReportManagementRoutes } from './platform/report-management/report-management.routes';
import { HolidayManagementRoutes } from './platform/holiday-management/holiday-management.routes';
import { FileManagementRoutes } from './platform/file-management/file-management.routes';

export const platformRoutes: RouteConfig[] = [
  {
    path: '/home-management/',
    component: MainLayout,
    children: HomeManagementRoutes,
    beforeEnter: routerGuard
  },
  {
    path: '/organization-management/',
    component: MainLayout,
    children: OrganizationManagementRoutes,
    beforeEnter: routerPermissionGuard
  },
  {
    path: '/log-management/',
    component: MainLayout,
    children: LogManagementRoutes,
    beforeEnter: routerPermissionGuard
  },
  {
    path: '/system-management/',
    component: MainLayout,
    children: SystemManagementRoutes,
    beforeEnter: routerPermissionGuard
  },
  {
    path: '/authority-management/',
    component: MainLayout,
    children: AuthorityManagementRoutes,
    beforeEnter: routerPermissionGuard
  },
  {
    path: '/product-management/',
    component: MainLayout,
    children: ProductManagementRoutes,
    beforeEnter: routerPermissionGuard
  },
  {
    path: '/reports-management/',
    component: MainLayout,
    children: ReportManagementRoutes,
    beforeEnter: routerPermissionGuard
  },
  {
    path: '/message-center/',
    component: MainLayout,
    children: MessageCenterRoutes,
    beforeEnter: routerPermissionGuard
  },
  {
    path: '/modeling-view/',
    component: MainLayout,
    children: [
      {
        path: ':string',
        component: PageModelingView
      }
    ],
    beforeEnter: routerPermissionGuard,
  },
  {
    path: '/holiday-management/',
    component: MainLayout,
    children: HolidayManagementRoutes,
    beforeEnter: routerPermissionGuard
  },
  {
    path: '/file-management/',
    component: MainLayout,
    children: FileManagementRoutes,
    beforeEnter: routerPermissionGuard
  },
  {
    path: '/login',
    beforeEnter: routerLoginGuard,
    component: Login,
  },
  {
    path: '/logout',
    beforeEnter: routerLogoutGuard,
  },
  {
    path: '/401',
    component: UnAuthorized,
  },
  {
    path: '*',
    redirect: '/home-management/',
  },
];
