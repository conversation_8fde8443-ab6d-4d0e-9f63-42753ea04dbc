/*
变量参考 https://github.com/vueComponent/ant-design-vue/blob/master/components/style/themes/default.less
或 https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/components/style/themes/default.less
*/
/* 重写 AntDesign 的主题样式 */
/* 重写 AntDesign 结束 */
/* 自定义相关 */
/* 基础边距 */
.box {
  display: flex;
}
.box .box_left {
  width: 330px;
  height: 500px;
  margin-right: 16px;
}
.box .box_right {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}
.box .box_right .dictionary_table {
  margin-top: 16px;
}
.box .box_right .dictionary_table button {
  margin-bottom: 8px;
}
.box .box_right .common_btn {
  padding: 0;
  margin: 0  0 0 10px;
  width: 70px !important;
  height: 30px !important;
  text-align: center;
  line-height: 25px;
  border-radius: 5px !important;
  border: 1px solid #ddd !important;
  background-color: #fff !important;
  color: #333 !important;
  opacity: 1;
  text-shadow: none !important;
  box-shadow: none !important;
}
