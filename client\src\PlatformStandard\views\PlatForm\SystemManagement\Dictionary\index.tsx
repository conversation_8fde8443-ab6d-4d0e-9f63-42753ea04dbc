import { Component } from 'vue-property-decorator';
import { WrappedFormUtils } from 'ant-design-vue/types/form/form';
import { CompEditForm, CompTreeList, CompBaseTable, CompModalForm, CompCard } from '@/PlatformStandard/components';
import { TableColumnDto, TableValueDto } from '@/PlatformStandard/components/CompTable';
import { dictionaryService } from '@/PlatformStandard/services/dictionary';
import { TreeData, FetchDataSourceDto } from '@/PlatformStandard/common/defines';
import { FormFieldDto } from '@/PlatformStandard/components/CompEditForm/comp-edit-form.types';
import { VueModule } from '@/PlatformStandard/common/defines/module-i18n';
import { formHelper, i18nHelper, notificationHelper } from '@/PlatformStandard/common/utils';
import { TreeRefreshEnum } from '@/PlatformStandard/components/CompTreeList/types';
import styles from './dictionary.module.less';
import { languageService } from '@/PlatformStandard/services/language';

@Component({ components: { CompEditForm, CompTreeList, CompBaseTable, CompModalForm, CompCard } })
export class DictionaryManagement extends VueModule {
  pageActions: string[] = [this.actions.add, this.actions.delete, this.actions.edit];
  typeData: FormFieldDto[] = [];
  private editData: any = {};
  private ediType!: 'typeEdit' | 'typeAdd' | 'detailEdit' | 'detailAdd';
  private selectTypeCode = '';
  private selectTypeId = '';
  private selectType: any;
  private tableColumns: TableColumnDto[] = [];
  private detailLookVisible = false;
  private detailEditVisible = false;
  private detailFieldData: FormFieldDto[] = [];
  private form!: WrappedFormUtils;
  private currentTypeData = {};

  onLoadTypeData(value: FetchDataSourceDto<TreeData[]>) {
    value.callback(dictionaryService.getTreeDictionaries(value.params ? value.params.key + '' : ''));
  }

  onTypeSelected(value: TreeData, selected: boolean) {
    if (!selected) {
      this.typeData = [];
      this.selectTypeCode = '';
      this.selectTypeId = '';
      this.selectType = {};
      this.editData = {};
    } else {
      this.selectTypeCode = value.datum;
      this.selectType = value;
      this.selectTypeId = value.key;
      dictionaryService.getDictionary(value.key).subscribe(s => {
        this.selectType = { ...this.selectType, ...s, id: value.key };
        this.initTypeDetail(s);
      });
    }

    (this.$refs.table as any).refreshData(1);
  }

  onAddDictionary() {
    this.form = this.$form.createForm(this, { name: 'detailForm' });
    this.ediType = 'detailAdd';
    this.detailEditVisible = true;
    this.editData = {};
  }

  onAddType(v: any) {
    if (v.key !== '' && !this.selectTypeCode) {
      this.$message.warning(this.locale[this.platformI18n].dictionary.selectCheck);
      return;
    }
    this.form = this.$form.createForm(this, { name: 'detailForm' });
    this.ediType = 'typeAdd';
    this.detailEditVisible = true;
    this.editData = { parentId: v.key };
  }

  onDeleteType() {
    dictionaryService.deleteDictionary(this.selectType.key).subscribe(s => {
      (this.$refs.tree as CompTreeList).refreshData({ type: TreeRefreshEnum.tree });
      this.clearTypeData();
    });
  }

  onEditType() {
    this.form = this.$form.createForm(this, { name: 'detailForm' });
    this.ediType = 'typeEdit';
    this.detailEditVisible = true;
    this.editData = { ...this.selectType };
    console.log(this.editData);
  }

  onDelete(id: string) {
    dictionaryService.deleteDictionary(id).subscribe(s => {
      (this.$refs.table as CompBaseTable).refreshData(1);
    });
  }

  detailEdit(row: any) {
    this.form = this.$form.createForm(this, { name: 'detailForm' });
    this.ediType = 'detailEdit';
    this.editData = { ...row };
    this.detailEditVisible = true;
  }

  dicDetail(row: any) {
    this.detailFieldData = [];
    this.detailFieldData.push({ label: this.locale[this.fieldsI18n].code, value: row.code });
    this.detailFieldData.push({ label: this.locale[this.fieldsI18n].name, value: row.name });
    this.detailFieldData.push({ label: this.locale[this.platformI18n].dictionary.value, value: row.value });
    this.detailFieldData.push({ label: this.locale[this.fieldsI18n].order, value: row.order });
    this.detailFieldData.push({ label: this.locale[this.fieldsI18n].remark, value: row.remark, columnCount: 1 });
    this.detailLookVisible = true;
  }

  detailOk() {
    const msgs = formHelper.validateForm(this.form);
    console.log(msgs);
    if (msgs.length > 0) {
      notificationHelper.error(msgs);
    } else {
      switch (this.ediType) {
        case 'detailEdit':
        case 'typeEdit':
          dictionaryService.updateDictionary(this.editData.id, this.editData).subscribe(() => {
            this.detailEditVisible = false;
            if (this.ediType === 'typeEdit') {
              this.initTypeDetail(this.editData);
              (this.$refs.tree as CompTreeList).refreshData({
                type: TreeRefreshEnum.node,
                title: this.editData.name,
              });
            } else {
              (this.$refs.table as CompBaseTable).refreshData(1);
            }
          });
          break;
        case 'detailAdd':
          dictionaryService.addDictionary({ ...this.editData, groupCode: this.selectTypeCode }).subscribe(() => {
            this.detailEditVisible = false;
            (this.$refs.table as CompBaseTable).refreshData(1);
          });
          break;
        case 'typeAdd':
          dictionaryService.addDictionary({ ...this.editData, groupCode: '' }).subscribe(() => {
            this.detailEditVisible = false;
            (this.$refs.tree as CompTreeList).refreshData({
              type: this.editData.parentId ? TreeRefreshEnum.children : TreeRefreshEnum.tree,
            });
          });
          break;
      }
    }
  }

  private initTypeDetail(v: any) {
    this.typeData = [];
    this.typeData.push({ label: this.locale[this.fieldsI18n].code, value: v.code });
    this.typeData.push({ label: this.locale[this.fieldsI18n].name, value: v.name });
    this.typeData.push({ label: this.locale[this.platformI18n].dictionary.value, value: v.value });
    this.typeData.push({ label: this.locale[this.fieldsI18n].order, value: v.order });
    this.typeData.push({ label: this.locale[this.fieldsI18n].remark, value: v.remark, columnCount: 1 });
  }

  private clearTypeData() {
    this.selectTypeCode = '';
    this.selectTypeId = '';
    this.typeData = [];
    (this.$refs.table as CompBaseTable).refreshData(1);
  }

  created() {
    languageService.langeAsync$.subscribe(() => {
      this.tableColumns = [
        { title: this.locale[this.fieldsI18n].code, dataIndex: 'code' },
        { title: this.locale[this.fieldsI18n].name, dataIndex: 'name' },
        { title: this.locale[this.fieldsI18n].order, dataIndex: 'order' },
        { title: this.locale[this.fieldsI18n].operation, dataIndex: 'operation', scopedSlots: { customRender: 'actions' } },
      ];

      if (this.selectType) {
        this.initTypeDetail(this.selectType);
      }
    });
    languageService.language$.subscribe(() => {
      if (this.$refs.tree) {
        (this.$refs.tree as CompTreeList).refreshData({ type: TreeRefreshEnum.tree });
        this.clearTypeData();
      }
    });
  }

  render() {
    return (
      <div class={styles.box}>
        <div class={styles.box_left}>
          <comp-tree-list
            ref='tree'
            title={this.locale[this.platformI18n].dictionary.category}
            on-load-data={this.onLoadTypeData}
            on-select={(v: any, selected: boolean) => {
              this.onTypeSelected(v, selected);
            }}
          ></comp-tree-list>
        </div>
        <div class={styles.box_right}>
          <comp-edit-form title={this.locale[this.platformI18n].dictionary.edit} textFieldsClone={this.typeData}>
            <template slot='title-right'>
              <a-dropdown v-permission={this.actions.add}>
                <a-menu slot='overlay' on-click={this.onAddType}>
                  <a-menu-item key=''>{this.locale[this.platformI18n].dictionary.topType}</a-menu-item>
                  <a-menu-item key={this.selectTypeId}>{this.locale[this.platformI18n].dictionary.subtype}</a-menu-item>
                </a-menu>
                <a-button type='primary' class={styles.common_btn}>
                  {this.locale[this.buttonsI18n].add} <a-icon type='down' />
                </a-button>
              </a-dropdown>
              {this.selectTypeCode ? (
                <span>
                  <a-button v-permission={this.actions.edit} class='ml-1' type='primary' on-click={this.onEditType}>
                    {this.locale[this.buttonsI18n].edit}
                  </a-button>
                  <a-popconfirm
                    v-permission={this.actions.delete}
                    title={this.locale[this.frameworkI18n]['delete-info']}
                    ok-text={this.locale[this.fieldsI18n].yes}
                    cancel-text={this.locale[this.fieldsI18n].no}
                    on-confirm={this.onDeleteType}
                  >
                    <a-button class='ml-1' type='danger'>
                      {this.locale[this.buttonsI18n].delete}
                    </a-button>
                  </a-popconfirm>
                </span>
              ) : null}
            </template>
          </comp-edit-form>
          <div class={styles.dictionary_table}>
            <comp-card title={this.locale[this.platformI18n].dictionary.list}>
              <template slot='extra'>
                {this.selectTypeCode ? (
                  <a-button v-permission={this.actions.add} type='primary' on-click={this.onAddDictionary}>
                    {this.locale[this.buttonsI18n].add}
                  </a-button>
                ) : null}
              </template>
              <comp-base-table
                ref='table'
                rowKey='id'
                cardStyle='true'
                columns={this.tableColumns}
                scoped-slots={{
                  actions: (cell: string, row: any) => {
                    return (
                      <span>
                        <span class='mr-1'>
                          <a-icon
                            type='eye-o'
                            class='text-primary'
                            on-click={() => {
                              this.dicDetail(row);
                            }}
                          ></a-icon>
                        </span>
                        <span v-permission={this.actions.edit} class='mr-1'>
                          <a-icon
                            type='edit'
                            class='text-primary'
                            on-click={() => {
                              this.detailEdit(row);
                            }}
                          ></a-icon>
                        </span>
                        <a-tooltip v-permission={this.actions.delete}>
                          <a-popconfirm
                            title={this.locale[this.frameworkI18n]['delete-info']}
                            ok-text={this.locale[this.fieldsI18n].yes}
                            cancel-text={this.locale[this.fieldsI18n].no}
                            on-confirm={() => {
                              this.onDelete(row.id);
                            }}
                          >
                            <a-icon class='text-primary' type='delete'></a-icon>
                          </a-popconfirm>
                        </a-tooltip>
                      </span>
                    );
                  },
                }}
                on-load-data={(v: FetchDataSourceDto<TableValueDto>) => {
                  v.callback(dictionaryService.getDictionaryList(this.selectTypeCode, v.params['page-index'], v.params['page-size']));
                }}
              ></comp-base-table>
            </comp-card>
          </div>
        </div>
        <comp-modal-form
          v-model={this.detailLookVisible}
          footer={null}
          title={this.locale[this.buttonsI18n].read}
          textFields={this.detailFieldData}
        ></comp-modal-form>
        <comp-modal-form v-model={this.detailEditVisible} title={this.locale[this.frameworkI18n].operate} on-ok={this.detailOk}>
          <a-form form={this.form}>
            <a-form-item label={this.locale[this.fieldsI18n].code} required>
              <a-input
                v-decorator={[
                  'code',
                  {
                    initialValue: this.editData.code,
                    rules: [{ required: true, message: `${this.locale[this.controlsI18n].input}${this.locale[this.fieldsI18n].code}` }],
                  },
                ]}
                v-model={this.editData.code}
                on-change={() => {
                  console.log(this.editData);
                }}
              />
            </a-form-item>
            <a-form-item label={this.locale[this.fieldsI18n].name} required>
              <a-input
                v-decorator={[
                  'name',
                  {
                    initialValue: this.editData.name,
                    rules: [{ required: true, message: `${this.locale[this.controlsI18n].input}${this.locale[this.fieldsI18n].name}` }],
                  },
                ]}
                v-model={this.editData.name}
              />
            </a-form-item>
            <a-form-item label={this.locale[this.platformI18n].dictionary.value}>
              <a-input v-decorator={['value', { initialValue: this.editData.value }]} v-model={this.editData.value} />
            </a-form-item>
            <a-form-item label={this.locale[this.fieldsI18n].order} required>
              <a-input-number
                style='width:100%'
                v-decorator={[
                  'order',
                  {
                    initialValue: this.editData.order,
                    rules: [{ required: true, message: `${this.locale[this.controlsI18n].input}${this.locale[this.fieldsI18n].order}` }],
                  },
                ]}
                v-model={this.editData.order}
              />
            </a-form-item>
            <a-form-item label={this.locale[this.fieldsI18n].remark}>
              <a-input v-decorator={['remark', { initialValue: this.editData.remark }]} v-model={this.editData.remark} />
            </a-form-item>
          </a-form>
        </comp-modal-form>
      </div>
    );
  }
}
