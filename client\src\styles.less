@import './themes/default/index.less';

#app {
  font-family: '微软雅黑', 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.global-loading {
  top: 200px;

  .anticon {
    display: none;
  }

  .ant-modal-body {
    padding: 8px;
  }

  .ant-modal-confirm-btns {
    display: none;
  }

  .ant-modal-confirm-content {
    margin: 20px 0;
    padding: 0;
    text-align: center;
  }
}

.ant-calendar-picker,
.ant-select {
  width: 100%;
}

.red-preview {
  color: red;
}

.blue-preview {
  color: blue;
}

.black-preview {
  color: black;
}

.ant-form-explain {
  display: none;
}

.ant-form-item-with-help {
  margin-bottom: 0px;
}

.ant-form-item {
  margin-bottom: 8px;
}
.card_top2{
  .ant-form-item {
    margin-bottom: 0px !important;
  }
}
.ant-table-fixed-columns-in-body {
  color: @text-color !important;
}
