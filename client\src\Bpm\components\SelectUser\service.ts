import { Observable } from 'rxjs';

import { httpHelper } from '@/PlatformStandard/common/utils';

class DepartmentService {
  getDepartmentTree(params: any): Observable<any[]> {
    const _url = '/api/platform/v1/manage/tree-organizations';
    return httpHelper.get(_url, { params });
  }

  getOrganization(organizationId: string): Observable<any> {
    const _url = `/api/platform/v1/manage/bpm/organization/${organizationId}`;
    return httpHelper.get(_url);
  }

  getUserList(id: string): Observable<any> {
    const _url = `/api/platform/v1/organizations/${id}/select-users`;
    return httpHelper.get(_url);
  }
}

export const departmentService = new DepartmentService();
