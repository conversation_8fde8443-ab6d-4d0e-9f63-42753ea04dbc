/*
变量参考 https://github.com/vueComponent/ant-design-vue/blob/master/components/style/themes/default.less
或 https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/components/style/themes/default.less
*/
/* 重写 AntDesign 的主题样式 */
/* 重写 AntDesign 结束 */
/* 自定义相关 */
/* 基础边距 */
.card_top {
  margin-bottom: 8px !important;
  padding: 0px 0px 0 0px !important;
}
.card_top :global(.ant-card-body) {
  padding: 0px 10px 0 10px !important;
}
.card_top :global(.ant-card-body) :global(.ant-layout) {
  padding-bottom: 0 !important;
}
.card_top :global(.ant-form-item-label) {
  line-height: 20px !important;
}
.button_top {
  text-align: right;
  float: right;
  padding-top: 22px;
}
:global(.ant-tabs-top-bar) {
  border: 0px solid #ddd !important;
}
:global(.ant-tabs-bar) {
  background: linear-gradient(180deg, #f8fafc, #eef3f7);
  margin: 0 !important;
}
:global(.ant-tabs-tab) {
  margin: 0 !important;
  font-size: 14px !important;
  font-weight: bold !important;
  color: #333;
}
:global(.ant-tabs-tab-active) {
  background: #fff;
}
:global(.ant-tabs-ink-bar) {
  top: 0;
}
:global(.ant-tabs-nav-wrap) {
  padding: 0 20px;
}
.cardTitle {
  position: relative;
  padding: 8px 8px !important;
  box-sizing: border-box;
  font-size: 16px;
  background: #f4f7fa;
  border-radius: 4px;
  width: 100%;
}
.cardTitle:before {
  content: '';
  position: absolute;
  height: 16px;
  width: 4px;
  background-color: #2165d9;
  top: 12px;
  left: 10px;
}
.cardTitle font {
  font-weight: bold;
  padding-left: 15px;
  color: #333;
}
.colorSpan {
  width: 35px;
  height: 15px;
  margin: 7px 10px;
  display: inline-block;
}
