import { Component, Vue, Prop, Watch } from 'vue-property-decorator';

import { getComponentFromProp } from '@/PlatformStandard/common/utils';
import styles from './component.footer-bottom.module.less';

@Component
export class FooterBottom extends Vue {

  render() {
    const extraDom = getComponentFromProp(this, 'extra');
    const titleDom = getComponentFromProp(this, 'title');
    return (<div class={styles.footer}>
      {extraDom ? <div slot='extra'>{extraDom}</div> : null}
    </div>
    );
  }
}
