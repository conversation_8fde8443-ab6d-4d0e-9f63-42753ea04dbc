import { RouteConfig } from 'vue-router';
import { DictionaryManagement } from '@/PlatformStandard/views/PlatForm/SystemManagement/Dictionary';
import { GatewayManagement } from '@/PlatformStandard/views/PlatForm/SystemManagement/Gateway';
import { InitManagement } from '@/PlatformStandard/views/PlatForm/SystemManagement/Init';
import { MenuManagement } from '@/PlatformStandard/views/PlatForm/SystemManagement/Menu';
import { RightsManagement } from '@/PlatformStandard/views/PlatForm/SystemManagement/Rights';
import { DataPermission } from '@/PlatformStandard/views/PlatForm/SystemManagement/DataPermission';
import { RightsMenu } from '@/PlatformStandard/views/PlatForm/SystemManagement/Rights/Menu';
import { Apis } from '@/PlatformStandard/views/PlatForm/SystemManagement/Apis';
import { DataPermissionTable } from '@/PlatformStandard/views/PlatForm/SystemManagement/DataPermissionTable';
import { TableRelations } from '@/PlatformStandard/views/PlatForm/SystemManagement/TableRelations';
import { RightsMember } from '@/PlatformStandard/views/PlatForm/SystemManagement/Rights/Member';
import { JobsManagement } from '@/PlatformStandard/views/PlatForm/SystemManagement/Jobs';

export const SystemManagementRoutes: RouteConfig[] = [
  {
    path: 'menu',
    component: MenuManagement,
  },
  {
    path: 'rights',
    component: RightsManagement,
  },
  {
    path: 'rights/member',
    component: RightsMember
  },
  {
    path: 'rights/menu',
    component: RightsMenu
  },
  {
    path: 'dictionary',
    component: DictionaryManagement,
  },
  {
    path: 'init',
    component: InitManagement,
  },
  {
    path: 'gateway',
    component: GatewayManagement,
  },
  {
    path: 'data-permissions',
    component: DataPermission,
  },
  {
    path: 'applicationInterfaces',
    component: Apis,
  },
  {
    path: 'data-permission-tables',
    component: DataPermissionTable
  },
  {
    path: 'table-relations',
    component: TableRelations
  },
  {
    path: 'job-management',
    component: JobsManagement
  }
];
