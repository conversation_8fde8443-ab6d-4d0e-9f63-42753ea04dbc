import { httpHelper } from '@/PlatformStandard/common/utils';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { OrganizationDto, UserPositionDto } from './types';

class OrganizationService {
  getOrganizationTree(params: any): Observable<any[]> {
    const _url = '/api/platform/v1/manage/tree-organizations';
    return httpHelper.get(_url, { params });
  }

  searchOrganizations(params: any): Observable<any[]> {
    const _url = '/api/platform/v1/manage/search-organizations';
    return httpHelper.get(_url, { params });
  }

  getOrganization(organizationId: string): Observable<any> {
    const _url = `/api/platform/v1/manage/organization/${organizationId}`;
    return httpHelper.get(_url);
  }

  getPositionUsers(params: any): Observable<any> {
    const _url = '/api/platform/v1/manage/organization-position-user';
    return httpHelper.get(_url, { params: params }).pipe(
      map(data => ({
        total: data.total,
        items: data.items.map((m: any) => ({
          ...m,
          key: m.positionId
        })),
      }))
    );
  }

  saveOrganization(organization: OrganizationDto): Observable<any> {
    const _url = '/api/platform/v1/manage/save-organization';
    return httpHelper.post(_url, organization);
  }

  deleteOrganization(organization: any): Observable<any> {
    const _url = `/api/platform/v1/manage/organization/${organization.organizationId}`;
    return httpHelper.delete(_url, { params: organization });
  }

  getUsers(kw: string): Observable<any> {
    const _url = `/api/platform/v1/select-users`;
    return httpHelper.get(_url, { params: { keyword: kw, count: '20' } }, { loading: false }).pipe(
      map(data =>
        data.map((item: any) => ({
          value: item.value,
          label: `${item.label}(${item.account})`
        }))
      )
    );
  }

  savePositionAndUser(userPosition: UserPositionDto): Observable<any> {
    const _url = '/api/platform/v1/manage/organization-position-user';
    return httpHelper.post(_url, userPosition);
  }

  deletePositionAndUser(userPosition: any): Observable<any> {
    const _url = `/api/platform/v1/manage/organization-position-user/${userPosition.positionId}`;
    return httpHelper.delete(_url, { params: userPosition });
  }

}
export const organizationService = new OrganizationService();
