
export interface GlobleSetting {
  systemName?: string;
  logo?: string;
  logoThumbnail?: string;
  systemIcon?: string;
  watermarkType?: string;
  watermark?: string;
  enableWatermark?: string;
  enableWatermarkValue: boolean;
  isShowSystemMenu: string;
  isShowSystemMenuValue: boolean;
  logoReception?: string;
}

export interface CustomerSetting {
  // systemName?: string;
  // logo?: string;
  logoThumbnail?: string;
  // systemIcon?: string;
  watermarkType?: string;
  watermark?: string;
  enableWatermark?: string;
  enableWatermarkValue: boolean;
  isShowSystemMenu: string;
  isShowSystemMenuValue: boolean;
  logoReception?: string;
  buttonColor?: string;
  fontColor?: string;
  borderColor?: string;
  tableheadColor?: string;
  heightLightColor?: string;
  fixedTopHeaderColorLeft?: string;
  fixedTopHeaderColorRight?: string;
}
