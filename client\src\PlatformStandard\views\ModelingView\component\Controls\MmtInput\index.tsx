import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator';
import { BaseControl } from '../base-control';

@Component({
})
export class MmtInput extends BaseControl {
  render() {
    return (
      this.pageModel === 'view' ? (
        <div>{this.value}</div>
      ) : (
        <a-input
          value={this.value}
          placeholder={this.controlConfig.config.placeholder}
          disabled={!this.controlConfig.config.disabled}
          on-change={(e: any) => this.onChange(e.target.value)}
        ></a-input >
      )
    );
  }
}
