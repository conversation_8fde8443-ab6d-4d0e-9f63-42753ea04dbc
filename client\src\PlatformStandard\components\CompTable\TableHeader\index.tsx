import { Component, Vue, Prop, Emit, Watch } from 'vue-property-decorator';
import { VueModule } from '@/PlatformStandard/common/defines';
import { getComponentFromProp } from '@/PlatformStandard/common/utils';
import styles from './component.table-header.module.less';

@Component
export class CompTableHeader extends VueModule {
  @Prop() advanced!: boolean;
  @Prop() hideReset!: boolean;
  @Prop() hideBack!: boolean;
  pageActions: string[] = [];
  private _advanced = false;

  created() { }

  @Emit('search') onSearch() { }
  @Emit('reset') onReset() { }
  @Emit('back') onBack() { }
  render() {
    const baseSearchDom = getComponentFromProp(this, 'base');
    const moreSearchDom = getComponentFromProp(this, 'more');
    return (
      <div class={styles.condition} style='padding:5px 0 0 0;'>
        <a-row justify='space-between'>
          <a-col span='18'>{baseSearchDom}</a-col>
          <a-col span='6' class={styles.float_button}>
            <a-row>
              {this.advanced ? (
                <a-col span='6'>
                  <a-button
                    type='link'
                    on-click={() => {
                      this._advanced = !this._advanced;
                      this.$forceUpdate();
                    }}
                  >
                    {this.locale[this.componentsI18n]['advanced-search']} <a-icon type={this._advanced ? 'up' : 'down'} />
                  </a-button>
                </a-col>
              ) : null}
              <a-col span={this.advanced ? 18 : 24} style='float:right'>
                <a-button-group style='padding-top:22px;'>
                  <a-button class={styles.searchBtn} size='default' type='primary' on-click={this.onSearch}>
                    {this.locale[this.buttonsI18n].search}
                  </a-button>
                  {
                    (!this.hideReset) ?
                      <a-button class={styles.commonBtn} size='default' on-click={this.onReset}>
                        {this.locale[this.buttonsI18n].reset}
                      </a-button>
                      :
                      null
                  }
                   {
                    (this.hideBack) ?
                      <a-button class={styles.commonBtn} size='default' on-click={this.onBack}
                      style='margin-left: 10px'>
                        {this.locale[this.buttonsI18n].back}
                        {/* {this.$t('buttons.back')} */}
                      </a-button>
                      :
                      null
                  }
                </a-button-group>
              </a-col>
            </a-row>
          </a-col>
        </a-row>
        {this._advanced ? <div class='mt-2'>{moreSearchDom} </div> : null}
      </div>
    );
  }
}
