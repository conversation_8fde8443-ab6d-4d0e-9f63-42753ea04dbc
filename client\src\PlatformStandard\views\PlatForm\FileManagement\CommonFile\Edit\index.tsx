import { Component, Prop, Vue } from 'vue-property-decorator';
import { CommonFileDto } from '../types';
import { WrappedFormUtils } from 'ant-design-vue/types/form/form';
import { formHelper, i18nHelper, notificationHelper } from '@/PlatformStandard/common/utils';
import { languageService } from '@/PlatformStandard/services/language';

@Component
export class CommonFileEdit extends Vue {
    private form!: WrappedFormUtils;
    private currentCommonFile!: CommonFileDto;
    @Prop() selectCommonFile!: CommonFileDto;
    @Prop() modelType!: string;
    private statusDataset = i18nHelper.getLocaleObject('platform.dataPermission.statusDataset');
    private action = '../api/platform/v1/common-file/upload';
    private fileList: any = [];
    private fileRemove(file: any) {
        const index = this.fileList.indexOf(file);
        const newFileList = this.fileList.slice();
        newFileList.splice(index, 1);
        this.fileList = newFileList;
      }
      private beforeUpload(file: any) {
        this.fileList = [file];
        // return false;
      }
    getCommonFileInfo(): CommonFileDto {
        if (this.modelType === 'add') {
            if (this.fileList.length > 0) {
                this.currentCommonFile.fileId = this.fileList[0].response[0].fileId;
            }
        }
        return this.currentCommonFile;
    }
    validateForm(): string[] {
        const msgs = formHelper.validateForm(this.form);
        if (this.modelType === 'add' &&  this.fileList.length === 0) {
            msgs.push(this.$l.getLocale(['controls.select', 'platform.fields.commonFile']));
        }
        return msgs;
    }
    created() {
        this.form = this.$form.createForm(this, { name: 'commonFileForm' });
        languageService.language$.subscribe(lang => {
            this.statusDataset = i18nHelper.getLocaleObject('platform.dataPermission.statusDataset');
        });
        this.currentCommonFile = JSON.parse(JSON.stringify(this.selectCommonFile));
    }
    render() {
        return (
            <div>
                <a-form form={this.form} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
                    <a-form-item label={this.$t('platform.fields.name')} required>
                        <a-input
                            on-change={(v: any) => this.currentCommonFile.fileName = v.target.value}
                            placeholder={this.$t('platform.fields.name')}
                            v-decorator={[
                                'name',
                                {
                                    initialValue: this.currentCommonFile.fileName,
                                    rules: [
                                        {
                                            required: true,
                                            message: this.$l.getLocale(['controls.input', 'platform.fields.name']),
                                        },
                                    ],
                                },
                            ]}></a-input>
                    </a-form-item>
                    <a-form-item label={this.$t('platform.fields.keyword')}>
                        <a-input
                            on-change={(v: any) => this.currentCommonFile.fileTag = v.target.value}
                            placeholder={this.$t('platform.fields.keyword')}
                            v-decorator={[
                                'keyword',
                                {
                                    initialValue: this.currentCommonFile.fileTag,
                                    rules: [
                                        {
                                            message: this.$l.getLocale(['controls.input', 'platform.fields.keyword']),
                                        },
                                    ],
                                },
                            ]}></a-input>
                    </a-form-item>
                    {
                        this.modelType === 'add' ?
                        <a-form-item label={this.$t('platform.fields.commonFile')} required>
                            <a-upload
                            multiple={false}
                            file-list={this.fileList}
                            remove={(file: any) => this.fileRemove(file)}
                            before-upload={(file: any) => this.beforeUpload(file)}
                            action={this.action}>
                            <a-button> <a-icon type='upload' />
                                {this.$l.getLocale(['controls.select', 'platform.fields.commonFile'])}
                            </a-button>
                            </a-upload>
                        </a-form-item>
                        :
                        <a-form-item label={this.$t('platform.fields.inUse')}>
                            <a-select default-value={this.currentCommonFile.state}
                            on-change={(value: any) => { this.currentCommonFile.state = value; }}
                            >
                                {this.statusDataset.map((item: any) => (
                                    <a-select-option value={item.value}>{item.label}</a-select-option>
                                ))}
                            </a-select>
                        </a-form-item>
                    }
                </a-form>
            </div>
        );
    }
}
