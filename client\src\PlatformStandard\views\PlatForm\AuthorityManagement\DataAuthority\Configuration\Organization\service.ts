import { Observable } from 'rxjs';
import { httpHelper } from '@/PlatformStandard/common/utils';
import { DataAuthorityOrganizationDto, OrganizationDto } from './types';
import { map } from 'rxjs/operators';
class OrganizationService {
    getOrganizations(params: any): Observable<OrganizationDto> {
        const _url = '/api/platform/v1/manage/tree-organizations';
        return httpHelper
            .get(_url, { params })
            .pipe(
                map(data =>
                    data.map((m: { label: any; code: any; fullPathCode: any; value: string; childCount: number }) => ({
                        name: m.label,
                        code: m.code,
                        id: m.value.toLocaleLowerCase(),
                        path: m.fullPathCode,
                        children: m.childCount > 0 ? [] : undefined,
                    }))
                )
            );
    }
    getDataAuthoritysOrganization(authorityId: string): Observable<DataAuthorityOrganizationDto[]> {
        const _url = `/api/platform/v1/manage/data-authoritys-organization/${authorityId}`;
        return httpHelper.get(_url);
    }
    saveDataAuthoritysOrganization(authorityId: string, data: OrganizationDto[]): Observable<string> {
        const url = `/api/platform/v1/manage/data-authoritys/${authorityId}/organization`;
        return httpHelper.post(url, data);
    }
}
export const organizationService = new OrganizationService();
