import { Component, Prop, Vue } from 'vue-property-decorator';
import { BaseControl } from '../base-control';

@Component({
})
export class MmtInputNumber extends BaseControl {
  private formatter(value: any) {
    if (this.controlConfig.config.thousandths && value) {
      const arr = value.toString().split('.');
      let result = '';
      arr.forEach((f: any, i: number) => {
        if (i === 0) {
          result += `${f}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        } else {
          result += `.${f}`;
        }
      });
      return result;
    } else {
      return value;
    }
  }
  private parser(value: any) {
    if (this.controlConfig.config.thousandths) {
      return value.replace(/\$\s?|(,*)/g, '');
    } else {
      return value;
    }
  }
  get defaultLable() {
    if (this.value || this.value === 0) {
      return this.formatter(this.value);
    }
    return '';
  }
  created(): void {
  }
  render() {
    return (
      this.pageModel === 'view' ? (
        <div>{this.defaultLable}</div>
      ) : (
        <a-input-number
          value={this.value}
          placeholder={this.controlConfig.config.placeholder}
          disabled={!this.controlConfig.config.disabled}
          precision={this.controlConfig.config.precision || this.controlConfig.config.precision === 0 ?
            this.controlConfig.config.precision : null}
          formatter={this.formatter}
          parser={this.parser}
          style={{ width: '100%' }}
          on-change={(e: any) => this.onChange(e)}
        ></a-input-number>
      ));
  }
}
