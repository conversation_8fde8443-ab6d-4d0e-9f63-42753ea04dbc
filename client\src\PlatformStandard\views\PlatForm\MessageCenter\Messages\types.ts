export interface MessageQueryDto {
  keyValue?: string;
  msgType?: number;
  category?: string;
  sendingStatus?: number;
}

export interface MessageDto {
  msgId?: string;
  keyValue?: string;
  msgType?: number;
  category?: string;
  fromSys?: string;
  msgTitle?: string;
  msgBody?: string;
  sendTo?: string;
  priority?: number;
  sendTime?: Date;
  sendingStatus?: number;
  createTime?: Date;
  phoneNumber?: string;
  templateId?: string;
  signCode?: string;
  emailAddress?: string;
  sender?: string;
  isBodyHtml?: boolean;
  senderName?: string;
  cc?: string;
  bcc?: string;
  tryTimes?: number;
  description?: string;
}
