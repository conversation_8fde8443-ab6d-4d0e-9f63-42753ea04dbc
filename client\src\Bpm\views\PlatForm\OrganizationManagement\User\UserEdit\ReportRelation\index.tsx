import { formHelper } from '@/PlatformStandard/common/utils';
import { WrappedFormUtils } from 'ant-design-vue/types/form/form';
import { Component, Prop, Vue } from 'vue-property-decorator';
import { UserReportRelationDto } from '../../types';
import debounce from 'lodash/debounce';
import { userService } from '../../service';

@Component
export class ReportRelation extends Vue {
  private currentReport: UserReportRelationDto = {};
  private form!: WrappedFormUtils;
  private userFetching = false;
  private fetchUser = debounce((value: any) => this.userSearch(value), 500);
  private userItems: any = [];
  private businessLineItems: any = [];

  save(): UserReportRelationDto {
    return this.currentReport;
  }

  validateForm(): string[] {
    return formHelper.validateForm(this.form);
  }

  private userSearch(value: string) {
    userService.getUsers(value).subscribe(data => {
      this.userItems = data;
      this.userFetching = false;
      this.$forceUpdate();
    });
  }

  private businessLineChange(id: string) {
    this.currentReport.businessLineId = id;
    const item = this.businessLineItems.find((d: any) => d.dictionaryId === id);
    this.currentReport.businessLineCode = item.code;
    this.currentReport.businessLineName = item.name;
  }

  created() {
    this.form = this.$form.createForm(this, { name: 'reportRelationForm' });
    userService.getBusinessLines().subscribe(data => {
      this.businessLineItems = data;
    });
  }
  render() {
    return (
      <div>
        <a-form form={this.form} labelCol={{ span: 5 }} wrapperCol={{ span: 16 }}>
          <a-row>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.user.lineLeader')} required>
                <a-select
                  show-search
                  placeholder={this.$l.getLocale(['controls.select', 'platform.user.lineLeader'])}
                  default-active-first-option={false}
                  show-arrow={false}
                  filter-option={false}
                  not-found-content={this.userFetching ? undefined : null}
                  on-search={this.fetchUser}
                  on-change={(value: any) => {
                    this.currentReport.leaderId = value;
                    this.currentReport.leaderName = (this.userItems.find((d: any) => d.value === value) as any).label;
                  }}
                  v-decorator={[
                    'leaderId',
                    {
                      initialValue: this.currentReport.leaderId,
                      rules: [{
                        required: true,
                        message: this.$l.getLocale(['controls.select', 'platform.user.lineLeader'])
                      }]
                    }]}
                >
                  {
                    this.userFetching ?
                      <a-spin slot='notFoundContent' size='small' />
                      :
                      null
                  }

                  {
                    this.userItems.map((item: any) => (
                      <a-select-option value={item.value}>
                        {item.label}
                      </a-select-option>
                    ))
                  }
                </a-select>
              </a-form-item>
            </a-col>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.user.businessLineName')} required>
                <a-select
                  placeholder={this.$l.getLocale(['controls.select', 'platform.user.businessLineName'])}
                  style='width:100%;'
                  on-change={(value: any) => this.businessLineChange(value)}
                  v-decorator={[
                    'businessLineId',
                    {
                      initialValue: this.currentReport.businessLineId,
                      rules: [{
                        required: true,
                        message: this.$l.getLocale(['controls.select', 'platform.user.businessLineName'])
                      }]
                    }]}
                >
                  {this.businessLineItems.map((item: any) => (
                    <a-select-option value={item.dictionaryId}>{item.name}</a-select-option>
                  ))}
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div >
    );
  }
}
