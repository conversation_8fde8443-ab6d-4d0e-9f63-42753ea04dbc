export interface SearchUserDto {
    keyWord?: string;
    organizationId?: string;
    status?: number;
}

export interface UserListDto {
    id?: string;
    name?: string;
    account?: string;
    phoneNumber?: string;
    email?: string;
    number?: string;
    positionName?: string;
    organizationName?: string;
    organizationFullName?: string;
    status?: number;
}

export interface UserDto {
    id?: string;
    name?: string;
    account?: string;
    phoneNumber?: string;
    email?: string;
    number?: string;
    lock?: number;
    status?: number;
    gender?: string;
    remark?: string;
    upperUserId?: string;
    upperUserLoginId?: string;
    upperUserName?: string;
    upperUserDisplayName?: string;
    roles: RoleDto[];
    organizationPositions: UserPositionDto[];
    userExtendInfo: UserExtend;
    reportRelations: UserReportRelationDto[];
}

export interface UserExtend {
    userId?: string;
    jobGrade?: string;
    education?: string;
    joinDate?: Date;
    leaveDate?: Date;
    workingState?: number;
}

export interface UserReportRelationDto {
    reportRelationId?: string;
    reportUserId?: string;
    leaderId?: string;
    leaderName?: string;
    businessLineId?: string;
    businessLineCode?: string;
    businessLineName?: string;
    positionName?: string;
    jobGrade?: string;
}

export interface RoleDto {
    roleId?: string;
    roleCode?: string;
    roleType?: string;
    roleName?: string;
    description?: string;
    status?: number;
}

export interface UserPositionDto {
    organizationId?: string;
    organizationCode?: string;
    organizationName?: string;
    organizationFullName?: string;
    positionId?: string;
    positionCode?: string;
    positionName?: string;
    primaryPosition?: boolean;
    userId?: string;
    userName?: string;
}

/**
 * 系统角色用户关系
 */
export interface RoleUserRelationDto {
    roleUserRelationId?: string;
    roleId?: string;
    userId: string;
    userLoginId?: string;
    userName?: string;
}

/**
 * 角色查询
 */
export interface RoleQueryDto {
    roleCode?: string;
    roleName?: string;
    status?: number;
}

/**
 * 系统角色
 */
export interface RoleDto {
    roleId?: string;
    roleCode?: string;
    roleType?: string;
    roleName?: string;
    description?: string;
    status?: number;
}
