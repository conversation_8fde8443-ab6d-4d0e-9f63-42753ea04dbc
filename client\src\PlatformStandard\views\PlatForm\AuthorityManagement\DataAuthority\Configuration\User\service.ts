import { Observable } from 'rxjs';
import { httpHelper } from '@/PlatformStandard/common/utils';
import { UserDto, UserListDto } from './types';
import { map } from 'rxjs/operators';
class UserService {
    getUsers(params: any): Observable<UserListDto> {
        const _url = `/api/platform/v1/manage/data-authority-users`;
        return httpHelper.get(_url, { params });
    }
    addUser(data: any): Observable<void> {
        const _url = `/api/platform/v1/manage/data-authority-user`;
        return httpHelper.post(_url, data);
    }
    deleteUser(id: string): Observable<void> {
        const path = `/api/platform/v1/manage/data-authority-user/${id}`;
        return httpHelper.delete(path);
    }
}
export const userService = new UserService();
