/*
变量参考 https://github.com/vueComponent/ant-design-vue/blob/master/components/style/themes/default.less
或 https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/components/style/themes/default.less
*/
/* 重写 AntDesign 的主题样式 */
/* 重写 AntDesign 结束 */
/* 自定义相关 */
/* 基础边距 */
.card_top {
  margin-bottom: 8px !important;
  padding: 0px 0px 0 0px !important;
}
.card_top :global(.ant-card-body) {
  padding: 0px 10px 0 10px !important;
}
.card_top :global(.ant-card-body) :global(.ant-layout) {
  padding-bottom: 0 !important;
}
.card_top :global(.ant-form-item-label) {
  line-height: 20px !important;
}
.button_top {
  text-align: right;
  float: right;
  padding-top: 22px;
}
.page {
  display: flex;
}
.page .tree_list {
  width: 330px;
  height: 510px;
}
.page .tree_list .tree_card {
  width: 100%;
  display: flex !important;
  flex-direction: column;
  height: 100%;
}
.page .content_info {
  width: 100%;
}
.page .content_info .edit_info {
  width: 100%;
  padding-left: 8px;
}
.page .content_info .edit_info .info_card {
  display: flex !important;
  width: 100%;
  flex-direction: column;
  height: auto;
}
.page .content_info .edit_info .info_card .buttons {
  height: 22px;
}
.page .content_info .edit_info2 {
  width: 100%;
  margin-top: 8px;
  padding-left: 8px;
}
.page .content_info .edit_info2 .info_card {
  display: flex !important;
  width: 100%;
  flex-direction: column;
  height: auto;
}
.page .content_info .edit_info2 .info_card .buttons {
  height: 22px;
}
.page .content_info .edit_info2 .info_card .row_outer {
  background-color: #f8f8f8;
  margin: 8px 0px !important;
}
.page .content_info .edit_info2 .info_card .operation {
  text-align: right;
  cursor: pointer;
}
.page .content_info .list_button {
  padding: 0 3px !important;
}
.page .labelStyle {
  color: #333;
}
.page .labelStyle font {
  color: #666;
}
.marginRightWidth {
  padding-right: 8px !important;
}
.marginRightWidth :global(.ant-form-item-label) {
  line-height: 20px !important;
}
.common_btn {
  padding: 0;
  margin: 0  0 0 10px;
  width: 70px !important;
  height: 30px !important;
  text-align: center;
  line-height: 32px;
  border-radius: 5px !important;
  border: 1px solid #ddd !important;
  background-color: #fff !important;
  color: #333 !important;
  opacity: 1;
  text-shadow: none !important;
  box-shadow: none !important;
}
.searchBtn {
  width: 72px;
  height: 32px;
  opacity: 1;
  background: #2165d9;
  text-align: center;
  line-height: 32px;
  border-radius: 5px !important;
}
.noResultTips {
  display: block;
  margin: 0 auto;
}
.textlength {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 80px;
}
.labelIcon {
  width: 40px;
  height: 41px;
  background: url(../../../../../assets/images/icon_com.png) no-repeat;
  background-size: 100% 100%;
  float: left;
  margin-left: 40px;
}
