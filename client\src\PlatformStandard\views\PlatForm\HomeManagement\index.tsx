import { date<PERSON><PERSON>per, form<PERSON><PERSON>per, i18n<PERSON>elper, notification<PERSON>elper } from '@/PlatformStandard/common/utils';
import { WrappedFormUtils } from 'ant-design-vue/types/form/form';
import { Component, Vue } from 'vue-property-decorator';

import styles from './home.module.less';
import banner from '@/assets/images/banner.png';

@Component
export class Homes extends Vue {
  created() {
    if (this.$route.query['app-code']) {
      sessionStorage.setItem('appCode', this.$route.query['app-code'] as string);
    }
  }

  render() {
    return (
      <div class={styles.main}>
         <a-row>
           <a-col span='24'>
            <div class={styles.leftBox}>
              <p class={styles.title}>欢迎来到BPM流程管理平台</p>
              <p class={styles.content}>
                我的假期、待办流程、常用流程一目了然<br/>
                让我们的办公更便捷
              </p>
            </div>
           </a-col>
           {/* <a-col span='6'>
              <div class={styles.rightBox}>
                <p class={styles.title}>我的假期</p>
                <p class={styles.content}>您当前可用年假<font>5.0天</font></p>
                <p class={styles.content}>上年年假结余1.0天</p>
                <p class={styles.content}>本年年假结享有天数9.0天</p>
              </div>
            </a-col> */}
         </a-row>
      </div>
    );
  }
}
