@import '../../../../../themes/default/variables.less';

.row_outer{
    background-color: @content-bg-1;
    margin: @base-size 0px !important;
}

.rowNew{
  margin: 0;
    // padding: @content-padding-size;
    padding:0px;
}

// .card_top{
//   margin-bottom:10px !important;
//   // padding: 10px 10px 0 10px !important;
//   padding: 0px 0px 0 0px !important;
//   :global(.ant-card-body){
//     padding: 0px 10px 0 10px !important;
//     // padding: 0px 0px 0 0px !important;
//     :global(.ant-layout){
//       padding-bottom:0 !important
//     }
//   }
//   :global(.ant-form-item-label){
//     line-height: 20px !important;
//   }
// }
.operation{
    text-align: right;
    cursor: pointer;
}

.status_select{
    width: 100%;
}

.anticon{
    margin-left: 5px;
}

.base_table {
    :global(.ant-card-body) {
      padding: 0 !important
    }
    :global(.ant-table .ant-table-body){
      margin: 0 !important;
    }
  }

.list_button {
    padding: 0 3px !important
}
.common_btn {
  padding:0;
  margin: 0;
  width: 70px !important;
  height: 30px;
  text-align: center;
  line-height: 32px;
  border-radius: 5px !important;
  border: 1px solid #ddd !important;
  background-color: #fff !important;
  color: #333 !important;
  opacity: 1;
  text-shadow: none !important;
  box-shadow: none !important;
}


.org_title {
  cursor: default;
  display: inline;
  padding-right: @base-size - 4px;
  font-weight: normal;
  &:extend(.parent_title);

  i {
    color: @text-color-desc;
    margin-left: 2px;
  }
}