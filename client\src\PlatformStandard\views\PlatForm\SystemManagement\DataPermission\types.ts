export interface PermissionQueryDto {
  description?: string;
  status?: number;
}

export interface PermissionTable {
  tableId?:   number;
  tableName?: string;
  descCn?:    string;
  descEn?:    string;
  locales: { [key: string]: string };
}

export interface PermissionColumn {
  columnId?:   number;
  tableName?:  string;
  columnName?: string;
  descCn?:     string;
  descEn?:     string;
  columnType?: string;
  locales: { [key: string]: string };
}

export interface DataPermissionDto {
  permissionId?:   string;
  code?:           string;
  description?:    string;
  expressionJson?: string;
  expression?:     string;
  status?:         number;
  details?:        Detail[];
}

export interface Detail {
  permissionDetailId?: number;
  permissionId?:       string;
  tableName?:          string;
  columnName?:         string;
  optionType:         number;
  conditionType:      number;
  conditionValue?:     string;
  expression?:         string;
  orderNum?:           number;
  rowKey?:             string;
  columnOptions?:      PermissionColumn[];
}

export interface ConditionRelation {
  relationId?: number;
  logicType?: string; // condition, group
  logicRelation?: string;  // and, or
  conditionCode?: number;  // orderNum
  childRelations?: ConditionRelation[];
}
