import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator';
import { FormList } from '../../FormList';
import { SearchList } from '../../SearchList';
import { modelingViewService } from '../../service';
import { PageModelingDto } from '../../types';
@Component({
    components: {
        FormList,
        SearchList
    }
})
export class ComModal extends Vue {
    @Prop() visible!: boolean;
    @Prop() buttonConfig!: any;
    private pageDesgin: PageModelingDto = {};
    private title = '';
    @Watch('visible')
    visibleChange(newVal: boolean) {
        if (newVal) {
            this.onLoad();
        }
    }
    @Emit('change')
    private onChange(isChange: boolean) {
    }
    private onOk() {
        // 校验表单
        if (this.pageDesgin.type === 2) {
            (this.$refs.formModal as FormList).check();
        }
    }
    private onLoad() {
        if (this.buttonConfig.config.format.page) {
            switch (this.buttonConfig.config.format.editModel) {
                case 'add':
                    this.title = '新增';
                    break;
                case 'edit':
                    this.title = '编辑';
                    break;
                case 'view':
                    this.title = '查看';
                    break;
            }
            modelingViewService.getPageModeling(this.buttonConfig.config.format.page).subscribe(data => {
                this.pageDesgin = data;
            });
        }
    }
    created(): void {

    }
    render() {
        return (
            <a-modal
                title={this.title}
                visible={this.visible}
                width='1100px'
                dialog-style={{ top: '20px' }}
                bodyStyle={{ padding: '0px' }}
                on-cancel={() => this.onChange(false)}
                on-ok={this.onOk}
            >
                {
                    this.visible ? (
                        this.pageDesgin.type ? ([
                            this.pageDesgin.type === 1 ? (
                                <search-list
                                    pageDesginJson={this.pageDesgin.pageDesginJson} />
                            ) : null,
                            this.pageDesgin.type === 2 ? (
                                <form-list
                                    pageDesginJson={this.pageDesgin.pageDesginJson}
                                    pageModel={this.buttonConfig.config.format.editModel}
                                    openModel={this.buttonConfig.config.format.openModel}
                                    parameter={this.buttonConfig.config.format.parameter}
                                    on-success={(isChange: boolean) => this.onChange(isChange)}
                                    ref='formModal'
                                />
                            ) : null
                        ]) : null
                    ) : null
                }
            </a-modal>
        );
    }
}
