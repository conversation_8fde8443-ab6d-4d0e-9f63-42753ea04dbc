import { Component, Vue, Watch } from 'vue-property-decorator';
import styles from './main-layout.module.less';
import { authService } from '@/PlatformStandard/services/auth';
import { MenuGroup, menuService, MenuViewState, PageMenu } from '@/PlatformStandard/services/menu';
import { commonService } from '@/PlatformStandard/services/common';
import { Settings } from '@/PlatformStandard/common/defines';
import { languageService } from '@/PlatformStandard/services/language';
import '@/assets/iconfont/iconFont/iconfont.css';
import pt_icon_jcpt from '@/assets/images/platIcon/pt_icon_jcpt.svg';
import { cacheService } from '@/Bpm/services/cache';
import { i18nHelper, notificationHelper } from '@/PlatformStandard/common/utils';

@Component
export class MainLayout extends Vue {
  private selectedModules = [''];
  private selectedMenus!: string[];
  private openedMenus!: string[];
  private menus: MenuGroup[] = [];
  private currentLanguageCode = languageService.current.code;
  private collapsed = menuService.menuViewState === MenuViewState.Collapsed;
  private breadcrumbs: string[] = [];
  private activeKey = '';
  private visible = false;
  private openKeys!: string[];
  private panes!: any[];
  private newTabIndex = 0;
  private data() {
    const panes: never[] = [
      // { title: '首页', content: '', key: '0', path: ''},
    ];
    return {
      activeKey: '',
      panes,
      newTabIndex: '',
    };
  }
  private onCollapse() {
    this.collapsed = !this.collapsed;
    menuService.switchMenuViewState(this.collapsed ? MenuViewState.Collapsed : MenuViewState.Expanded);
    if (this.collapsed) {
      this.openedMenus = [];
    }
  }

  private getMenus() {
    const module = Settings.AppKey;
    this.selectedModules = [module];
    this.menus = menuService.authorizedMenus.filter(menu => menu.key.startsWith(module));
  }

  private switchLanguage(e: any) {
    languageService.set(e.key);
    this.currentLanguageCode = languageService.current.code;
  }

  private switchModule(e: any) {
    console.log(e);
    this.selectedModules = [e.key];
    window.location.href = '/' + this.selectedModules + '/' ;
    // window.location.href = `/${Settings.UrlKey}/` + this.selectedModules + '/';
    // this.menus = menuService.authorizedMenus.filter(menu => menu.key.startsWith(e.key));
  }

  private getMenuUrl(menuRoute: string) {
    if (this.selectedModules
      && this.selectedModules.length > 0
      && this.selectedModules[0] === Settings.AppKey) {
      return `/${menuRoute}`;
    } else {
      return '';
    }
  }

  // private menuClick(menuRoute: any) {
  //   if (this.selectedModules
  //     && this.selectedModules.length > 0
  //     && this.selectedModules[0] !== Settings.AppKey) {
  //     window.location.href = (`${window.location.protocol}//${window.location.host}/${this.selectedModules[0]}/${menuRoute}`);
  //   }
  // }
  private menuClick(menuRoute: any) {
    const panes = this.panes;
    const activeKey = `${this.newTabIndex++}`;
    const path = '/' + menuRoute.route;

    if (JSON.stringify(this.panes).indexOf(JSON.stringify(path)) === -1) {
      panes.push({ title: menuRoute.locales.zh, content: '', key: activeKey, path: path });
      this.panes = panes;
    }
    this.$router.push(path);
    this.activeKey = activeKey;
    for (let i = 0; i < this.panes.length; i++) {
      if (path === this.panes[i].path) {
        this.activeKey = (i).toString();
      }
    }
    this.$router.push(path);
    this.activeKey = activeKey;

    // if (this.selectedModules
    //   && this.selectedModules.length > 0
    //   && this.selectedModules[0] !== Settings.AppKey) {
    //   // window.location.href = (`${window.location.protocol}//${window.location.host}/${this.selectedModules[0]}/${menuRoute}`);
    // }
  }

  private onOpenChange(openKeys: any) {
    const latestOpenKey = openKeys.find((key: string) => this.openedMenus.indexOf(key) === -1);
    const rootSubmenuKeys: string | any[] = [];
    this.menus.forEach(module => {
      rootSubmenuKeys.push(module.key);
    });
    if (rootSubmenuKeys.indexOf(latestOpenKey) === -1) {
      this.openKeys = openKeys;
    } else {
      this.openKeys = latestOpenKey ? [latestOpenKey] : [];
    }
    this.openedMenus = this.openKeys;
    this.$forceUpdate();
  }
  @Watch('$route')
  onInstanceChange() {
    // menuService.currentRoute = this.$route.path;
    this.breadcrumbs = menuService.getBreadcrumbs(this.$route.path, this.currentLanguageCode);
    console.log(this.breadcrumbs);
  }

  created() {
    this.getMenus();
    this.onInstanceChange();
    languageService.language$.subscribe(lang => {
      this.currentLanguageCode = lang;
      this.onInstanceChange();
    });

    const paths = this.$route.path.split('/');
    if (paths && paths.length > 2) {
      const moduleId = this.GetModule(this.$route.path);
      if (moduleId) {
        this.openedMenus = [moduleId];
      } else {
        this.openedMenus = [Settings.AppKey + '/' + paths[1]];
      }
      // this.openedMenus  = []
      this.selectedMenus = [Settings.AppKey + '/' + paths[1] + '/' + paths[2]];
    }
  }

  private GetModule(path: string): string {
    let moduleId = '';
    this.menus.forEach(module => {
      if (module.children && module.children.length > 0) {
        const page = module.children.find(d => `/${d.route}` === path && d.hidden === false);
        if (page) {
          moduleId = module.key;
        }
      }
    });
    return moduleId;
  }

  destroyed() {
    /* if (this.totalSub) {
      this.totalSub.unsubscribe();
    } */
  }

  private tabRemove(item: any, index: any) {
    console.log(item);
    this.panes.splice(index, 1);
    // console.log(this.activeKey);
    console.log(this.panes);
    // const _this = this;
    if (this.panes && this.panes.length >= 1) {
      this.panes = this.panes;
      this.$forceUpdate();
      const path = this.panes[this.panes.length - 1].path;
      this.$router.push(path);
      this.activeKey = (Number(this.panes[this.panes.length - 1].key)).toString();
      // this.activeKey = '9';
      console.log(this.activeKey);
    } else {
      // this.$router.push();
    }
    this.$forceUpdate();
  }

  private tabMenu(item: any) {
    this.$router.push(item.path);
  }

  private afterVisibleChange(val: any) {
    console.log('visible', val);
  }

  private showDrawer() {
    this.visible = true;
    console.log(this.visible);
  }

  private onClose() {
    this.visible = false;
  }

  private refreshCache() {
    cacheService.refreshUserCache(String(authService.user.id)).subscribe(() => {
      notificationHelper.success(i18nHelper.getLocale('messages.success'));
    });
  }

  render() {
    return (
      // style='min-height: 120vh;'
      <a-layout class={styles.main} style='height: calc(100vh - 20px); overflow:hidden'>
        <a-layout id='components-layout-demo-side'>
          <a-layout-sider
            class={styles.menu_container}
            width={Settings.MenuExpandedWidth}
            style='background:#172748'
            collapsedWidth={Settings.MenuCollapsedWidth}
            collapsed={this.collapsed}
            on-collapse={this.onCollapse}
            trigger={<a-icon type={this.collapsed ? 'menu-unfold' : 'menu-fold'} />}
            collapsible
            // icon='menu-fold-outlined'
          >
            {this.collapsed ? <div class={styles.logo} style={{ 'background': '#172748', 'padding': '10px', 'text-align': 'center' }}>
              <img src={commonService.logoM} alt='Movitech BPM' style={{ width: `40%`, margin: '0 auto' }} />
            </div> : <div class={styles.logo} style={{
              background: '#172748', padding: '0px 20px', height: '36px'
            }}>
              <img src={commonService.logo} alt='Movitech BPM'
               style={{ width: `65%`, margin: `3px 0 0 0` }} />
            </div>}
            <div>
              {/* <a-button type='primary' on-click={this.showDrawer}>
                  Open
                </a-button> */}
              {/* 后台管理系统 on-click={this.showDrawer} on-mouseover={this.hover} on-mouseout={this.houerOut}*/}
              <div class={styles.tabSystem} style={!this.collapsed ? 'padding: 12px 15px 12px 25px' : 'paddinfg:12px 30px'}>
                <a-popover placement='rightTop' left='0%' overlayClassName={this.collapsed ? styles.sysPop : styles.sysPopZhan}>
                  <template slot='content'>
                    {menuService.modules.map((item, i) => (
                      <p on-click={() => this.switchModule(item)} key={item.key}>
                        <img class={styles.sysIcon} src={pt_icon_jcpt}></img>{item.locales[String(this.currentLanguageCode)]}</p>
                    ))}
                  </template>
                  {/* <template slot="title">
                      <span>Title</span>
                    </template> */}
                <div>
                  <font style='float: left' v-show={!this.collapsed}>平台管理</font>
                  <a-button class={styles.sysbtn}></a-button>
                </div>
                </a-popover>
              </div>
              <div style='display: none'>
                <a-drawer
                  title='平台切换'
                  placement='left'
                  on-closable='true'
                  width='200'
                  visible={this.visible}
                  after-visible-change={this.afterVisibleChange}
                  on-close={this.onClose}
                  v-model={this.selectedModules}
                >
                  {menuService.modules.map((item, i) => (
                    <p on-click={() => this.switchModule(item)} key={item.key}>
                      <img src={pt_icon_jcpt}></img>{item.locales[String(this.currentLanguageCode)]}</p>
                  ))}
                </a-drawer>
              </div>
            </div>
            <a-menu
              mode='inline'
              theme='dark'
              class={styles.menu + (this.collapsed ? ` ${styles.collapsed}` : '')}
              style='border-top:1px solid #343D4C;height:auto'
              // default-open-keys={this.openedMenus}
              // default-selected-keys={this.selectedMenus}
              openKeys={this.openedMenus}
              onOpenChange={this.onOpenChange}
            >
              {this.menus.filter(d => d.hidden === false).map(item => (
                <a-sub-menu key={item.key}>
                  <span slot='title' class={styles.subMenu}>
                    {/* <a-icon type={item.icon} /> */}
                    <i class={'iconfont ' + item.icon}></i>
                    <span style='padding:0 10px'>
                      {this.collapsed ? '' : item.locales[String(this.currentLanguageCode)]}
                    </span>
                  </span>
                  {(item['children'] as PageMenu[])
                    .filter(child => !child.hidden)
                    .map(child => (
                      <a-menu-item key={child.key} on-click={() => this.menuClick(child)}>
                        {/* <router-link to={this.getMenuUrl(child.route)} nativeOnClick={() => this.menuClick(child.route)}> */}
                        <span style=''>
                          {child.locales[String(this.currentLanguageCode)]}</span>
                        {/* </router-link> */}
                      </a-menu-item>
                    ))}
                </a-sub-menu>
              ))}
            </a-menu>
          </a-layout-sider>
          <a-layout>
            <a-layout-header class={styles.top_bar} style='padding:0;height:auto;border-bottom:0px solid #ddd;'>
              <a-row type='flex' style='padding: 0px 20px; background: #fff !important;box-shadow: 0 1px 4px rgb(0 21 41 / 8%);'>
                <a-col flex='4'>
                  <a-breadcrumb class={styles.breadcrumb}>
                    {this.breadcrumbs.map((item, i) => (
                      <a-breadcrumb-item>{item}</a-breadcrumb-item>
                    ))}
                  </a-breadcrumb>
                </a-col>
                <a-col flex='4' style='text-align:right'>
                  <div class={styles.actions}>
                    <a-dropdown>
                      <a on-click={(e: any) => e.preventDefault()} style='padding:0 20px;' class={styles.linkAbout}>
                        <a-icon type='link' style='font-size:16px;margin-right:5px'/>友情链接
                      </a>
                      <a-menu slot='overlay' style='width: 64px;'>
                          <a-menu-item><a href='#' target='_blank'>友情链接</a></a-menu-item>
                      </a-menu>
                    </a-dropdown>
                    <a-dropdown>
                      <a on-click={(e: any) => e.preventDefault()} style='border-left: 1px solid #ddd; padding:0 20px;'
                       class={styles.linkAbout}>
                        {/* {this.currentLanguageCode === 'zh' ? '中文1' : 'English'} <a-icon type='down' /> */}
                        <i class={'iconfont icon-topbtn_duoyuyan'} style='font-size:16px;margin-right:5px'></i>{this.currentLanguageCode === 'zh' ? '中文' : 'English'}
                      </a>
                      <a-menu slot='overlay' style='width: 64px;' on-click={this.switchLanguage}>
                        {languageService.languages.map((item, i) => (
                          <a-menu-item key={item.code}>{item.name}</a-menu-item>
                        ))}
                      </a-menu>
                    </a-dropdown>
                    <a-dropdown class={styles.user_info}>
                      <a on-click={(e: any) => e.preventDefault()} style='border-left: 1px solid #ddd; padding:0 20px;'
                       class={styles.linkAbout}>
                        {/* <a-avatar icon='user' /> */}
                        {/* <i class={'iconfont icon-Avatar_default'}></i> */}
                        <img src={commonService.logo_head} style='width:20px; vertical-align: -5px;margin-right:5px' />
                        {authService.user.name}
                      </a>
                      <a-menu slot='overlay' style='width: 128px;'>
                        <a-menu-item>
                          <a-icon type='user' />
                          <span>{authService.user.name}</span>
                        </a-menu-item>
                        <a-menu-item on-click={() => this.refreshCache()}>
                          <a-icon type='reload' />
                          <span>{this.$t('boost.layout.refreshCache')}</span>
                        </a-menu-item>
                        <a-menu-item>
                          <router-link to={'/logout'}>
                            <a-icon type='logout' />
                            <span class='ml-1'>{this.$t('boost.layout.logout')}</span>
                          </router-link>
                        </a-menu-item>
                      </a-menu>
                    </a-dropdown>
                  </div>
                </a-col>
              </a-row>
              <a-row>
                  {/* <a-tab-pane closable={false}>
                  <span slot='tab'>
                    <a-icon type='home' style='margin:0 auto'/>
                  </span>
                </a-tab-pane> */}
                {/* <a-tabs type='card' v-model={this.activeKey} style='margin-bottom:-27px;margin-top:-1px;padding: 0px 20px;'>
                  {this.panes.map((item, index) => (
                    <a-tab-pane key={item.key} closable={false} style='padding:0'>
                      <span slot='tab' on-click={() => this.tabMenu(item)} style='display:block;padding:0 10px'>
                        <font>{item.title}</font>
                        <a-icon type='close' on-click={() => this.tabRemove(item, index)} style='float:right;margin:14px 0 0 10px;' />
                      </span>
                    </a-tab-pane>
                  ))}
                </a-tabs> */}
              </a-row>
            </a-layout-header>
            <a-layout-content style='margin: 0px 0;'>
              <div style='padding-bottom:20px;background: #F0F2F5'>
                <a-layout-content class={styles.content_container + ' bg-content '} >
                  <router-view></router-view>
                </a-layout-content>
              </div>
              {/* <div class={!this.collapsed ? styles.aboutLink : styles.aboutLinkMore}>
                友情链接：<a href='http://172.19.50.111:10080/todo?
                mode=urgency&page-index=1&page-size=10' target='_blank'>BPMCustomer</a></div> */}
            </a-layout-content>
          </a-layout>
        </a-layout>
      </a-layout >
    );
  }
}
