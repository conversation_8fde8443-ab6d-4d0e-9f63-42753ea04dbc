import { Component, Prop, Vue } from 'vue-property-decorator';
import { BaseControl } from '../base-control';

@Component({
})
export class MmtTextArea extends BaseControl {
  created(): void {
  }
  render() {
    return (
      this.pageModel === 'view' ? (
        <div
          style={{ 'white-space': 'pre-line', 'line-height': 'normal' }}
        >{this.value}</div>
      ) : (
        <a-textarea
          value={this.value}
          placeholder={this.controlConfig.config.placeholder}
          disabled={!this.controlConfig.config.disabled}
          rows={this.controlConfig.config.rows}
          on-change={(e: any) => this.onChange(e.target.value)}
        ></a-textarea>
      ));
  }
}
