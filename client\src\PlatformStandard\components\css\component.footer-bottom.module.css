/*
变量参考 https://github.com/vueComponent/ant-design-vue/blob/master/components/style/themes/default.less
或 https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/components/style/themes/default.less
*/
/* 重写 AntDesign 的主题样式 */
/* 重写 AntDesign 结束 */
/* 自定义相关 */
/* 基础边距 */
.card_top {
  margin-bottom: 8px !important;
  padding: 0px 0px 0 0px !important;
}
.card_top :global(.ant-card-body) {
  padding: 0px 10px 0 10px !important;
}
.card_top :global(.ant-card-body) :global(.ant-layout) {
  padding-bottom: 0 !important;
}
.card_top :global(.ant-form-item-label) {
  line-height: 20px !important;
}
.button_top {
  text-align: right;
  float: right;
  padding-top: 22px;
}
.footer {
  position: fixed;
  bottom: 0;
  width: calc(100% - 200px);
  padding: 10px;
  background: #F0F2F5;
  box-sizing: border-box;
  margin-left: -10px;
  text-align: center;
}
