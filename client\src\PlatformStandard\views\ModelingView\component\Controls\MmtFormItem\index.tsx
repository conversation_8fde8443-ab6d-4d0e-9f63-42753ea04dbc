import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator';
import styles from './form-item.module.less';
import * as coms from '../../Controls';
import { BaseControl } from '../base-control';
@Component({
    components: {
        ...coms
    }
})
export class MmtFormItem extends BaseControl {
    @Prop() currentCheckedItemId!: string;
    @Prop({ default: false }) isFormTable!: boolean;
    @Prop({ default: true }) isMove!: boolean;
    @Prop({ default: true }) isDeleted!: boolean;
    @Emit('checked')
    checked() {
    }
    @Emit('deleted')
    deleted() {
    }
    created(): void {
    }
    render() {
        const CustomTag = this.controlConfig.info.key;
        return (
            this.controlConfig ? (
                <div class={styles.div}>
                    <a-form-item
                        class={styles.form_item +
                            ` ${this.currentCheckedItemId === this.controlConfig.info.id
                                ? styles.active : null}`}
                        required={this.controlConfig.config.required}
                        label={this.controlConfig.config.vshow ? this.controlConfig.config.label : ''}
                        nativeOnClick={(e: any) => {
                            this.checked();
                            e.stopPropagation();
                        }}
                    >
                        <CustomTag
                            controlConfig={this.controlConfig}
                            pageModel={this.pageModel}
                            value={this.controlConfig.cdata}
                            on-change={(value: any) => this.controlConfig.cdata = value}
                        />
                    </a-form-item>
                    {
                        this.isDeleted &&
                            this.currentCheckedItemId === this.controlConfig.info.id ? (
                            <div class={styles.form_item_action}>
                                <a-icon type='delete' title='删除'
                                    nativeOnClick={(e: any) => {
                                        this.deleted();
                                        e.stopPropagation();
                                    }}
                                />
                            </div>
                        ) : null
                    }
                    {
                        this.isMove &&
                        this.currentCheckedItemId === this.controlConfig.info.id ? (
                            <div class={styles.form_item_drag}>
                                <a-icon type='drag' class='drag-widget' />
                            </div>
                        ) : null
                    }
                </div>
            ) : null
        );
    }
}
