{"version": "0.2.0", "configurations": [{"name": "Debug TypeScript App", "type": "node", "request": "launch", "program": "${workspaceFolder}/src/app.ts", "runtimeArgs": ["--inspect-brk=9229", "-r", "ts-node/register"], "env": {"NODE_ENV": "development"}, "sourceMaps": true, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"]}]}