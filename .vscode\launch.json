{"version": "0.2.0", "configurations": [{"name": "Debug Koa Server", "type": "node", "request": "launch", "program": "${workspaceFolder}/src/app.ts", "runtimeArgs": ["-r", "ts-node/register"], "env": {"NODE_ENV": "development"}, "sourceMaps": true, "restart": true, "protocol": "inspector", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"name": "Debug with Nodemon", "type": "node", "request": "launch", "runtimeExecutable": "nodemon", "program": "${workspaceFolder}/src/app.ts", "restart": true, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "env": {"NODE_ENV": "development"}}]}