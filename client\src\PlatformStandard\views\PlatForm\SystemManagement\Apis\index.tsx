import { form<PERSON><PERSON><PERSON>, i<PERSON>n<PERSON><PERSON><PERSON>, notification<PERSON><PERSON><PERSON>, toCasedStyleObject } from '@/PlatformStandard/common/utils';
import { languageService } from '@/PlatformStandard/services/language';
import { ActionEnum } from '@/PlatformStandard/services/menu';
import { Component, Vue } from 'vue-property-decorator';
import { apiService } from './service';
import { ApiDto, ApiQueryDto } from './types';
import { CompCard, CompTableHeader } from '@/PlatformStandard/components';
import styles from './Apis.module.less';
import { DataPermission } from '../DataPermission';
import { DataPermissionDto } from '../DataPermission/types';
import { dataPermissionService } from '../DataPermission/service';
import { WrappedFormUtils } from 'ant-design-vue/types/form/form';

@Component({ components: { CompCard, CompTableHeader, DataPermission } })
export class Apis extends Vue {
  private pagination = {
    total: 0,
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    size: 'default',
    showTotal: (total: string) =>
      this.$l
        .getLocale('paginations.total')
        .toString()
        .replace('{{value}}', total),
  };
  private query: ApiQueryDto = {};
  private dataSource: ApiDto[] = [];
  private fieldsSlotMap: any = {};
  private columns = [
    {
      dataIndex: 'moduleCode',
      slots: { title: 'moduleCode' }
    },
    {
      dataIndex: 'path',
      slots: { title: 'path' },
      width: '20%'
    },
    {
      dataIndex: 'summary',
      slots: { title: 'summary' }
    },
    {
      dataIndex: 'operationId',
      slots: { title: 'operationId' },
      width: '20%'
    },
    {
      dataIndex: 'tableNames',
      slots: { title: 'tableNames' },
      scopedSlots: { customRender: 'tableNames' },
      width: '20%'
    },
    {
      dataIndex: 'status',
      slots: { title: 'status' },
      scopedSlots: { customRender: 'status' },
    },
    {
      dataIndex: 'permissionIds',
      slots: { title: 'permissionIds' },
      scopedSlots: { customRender: 'permissionIds' },
      width: '20%'
    }
  ];
  private statusDataset = i18nHelper.getLocaleObject('platform.dataPermission.statusDataset');

  private actions = ActionEnum;

  private permissionItems: DataPermissionDto[] = [];
  private dataPermissionShow = false;

  private importForm!: WrappedFormUtils;
  private importShow = false;
  private importModuleCode!: string | undefined;
  private importJson!: string | undefined;
  private moduleCodeDataset = i18nHelper.getLocaleObject('platform.apis.moduleCodeDataset');

  private loadData(reset: boolean = false) {
    if (reset) {
      this.pagination.current = 1;
    }
    const params = { 'page-index': this.pagination.current, 'page-size': this.pagination.pageSize };
    apiService.getApiList({ ...toCasedStyleObject(this.query), ...params }).subscribe(data => {
      this.dataSource = data.items;
      this.pagination.total = data.total;
      this.$forceUpdate();
    });
  }

  private reset() {
    this.query = {};
  }

  private saveDataPermission(index: number) {
    this.dataSource[index].permissionIds = this.dataSource[index].permissionId ? [this.dataSource[index].permissionId as string] : [];
    apiService.saveApiDataPermission(this.dataSource[index]).subscribe(rs => {
      notificationHelper.success(i18nHelper.getLocale('messages.success'));
    });
  }

  private getPermissionItems() {
    dataPermissionService.getAllDataPermissions().subscribe(data => {
      this.permissionItems = data.items;
    });
  }

  private importApis() {
    try {
      const errMsgs = formHelper.validateForm(this.importForm);
      if (errMsgs && errMsgs.length > 0) {
        notificationHelper.error(errMsgs);
        return;
      }
      const apiList = JSON.parse(this.importJson || '');

      const apiDtoes: ApiDto[] = [];
      Object.keys(apiList.paths).forEach((key: any) => {
        if (apiList.paths[key].get) {
          apiDtoes.push({
            path: key,
            summary: apiList.paths[key].get.summary,
            operationId: apiList.paths[key].get.operationId
          });
        }
      });
      apiService.importApis(String(this.importModuleCode), apiDtoes).subscribe(rs => {
        notificationHelper.success(i18nHelper.getLocale('messages.success'));
        this.importModuleCode = undefined;
        this.importJson = undefined;
        this.importShow = false;
        this.loadData(true);
      });
    } catch (error) {
      notificationHelper.error(i18nHelper.getLocale('platform.apis.dataError'));
      console.log('error====>', error);
    }
  }

  private updateApiTables() {
    apiService.updateApiTables().subscribe(() => {
      this.loadData(true);
      notificationHelper.success(i18nHelper.getLocale('messages.success'));
    });
  }

  created() {
    this.importForm = this.$form.createForm(this, { name: 'importForm' });
    languageService.language$.subscribe(lang => {
      this.statusDataset = i18nHelper.getLocaleObject('platform.dataPermission.statusDataset');
      this.moduleCodeDataset = i18nHelper.getLocaleObject('platform.apis.moduleCodeDataset');
    });
    this.fieldsSlotMap['status'] = (text: any, record: any, index: number) => {
       // return text ? this.$l.getLocale('platform.commonRole.valid') : this.$l.getLocale('platform.commonRole.invalid');
       return (
        <div>
          {text ? <a-tag color='blue'>
          { this.$l.getLocale('platform.commonRole.valid') } </a-tag> : <a-tag color='red'> { this.$l.getLocale('platform.commonRole.invalid') } </a-tag>}
        </div>
      );
    };

    this.fieldsSlotMap['tableNames'] = (text: any, record: any, index: number) => {
      return (
        <div>
          {
            record.tableNames.map((name: string) => (
              <a-tag>{name}</a-tag>
            ))
          }
        </div>
      );
    };

    this.fieldsSlotMap['permissionIds'] = (text: any, record: any, index: number) => {
      return (
        <div>
          <a-select
            v-model={this.dataSource[index].permissionId}
            allowClear
            showSearch
            style='width: 95%'
            placeholder={this.$t('platform.role.dataRights')}
            class='mr-1'
            on-change={() => this.saveDataPermission(index)}
          >
            {
              this.permissionItems.map(permission => (
                <a-select-option key={permission.permissionId}>
                  {permission.description}
                </a-select-option>
              ))
            }
          </a-select>
          <a-icon
            type='profile'
            style='font-size: 16px; position: absolute; margin-top: 5px; cursor: pointer;'
            on-click={() => { this.dataPermissionShow = true; }}
          />
        </div>
      );
    };
    this.loadData(true);
    this.getPermissionItems();
  }

  render() {
    return (
      <div>
        <comp-card class={styles.card_top}>
          <comp-table-header
            on-search={() => {
              this.loadData(true);
            }}
            on-reset={() => {
              this.reset();
            }}
          >
            <template slot='base'>
              <a-row>
                <a-col span='6' class='mr-1'>
                  <a-form-item label={this.$t('platform.apis.path')}>
                    <a-input v-model={this.query.path}></a-input>
                  </a-form-item>
                </a-col>
                <a-col span='6' class='mr-1'>
                  <a-form-item label={this.$t('platform.apis.summary')}>
                    <a-input v-model={this.query.summary}></a-input>
                  </a-form-item>
                </a-col>
                <a-col span='6' class='mr-1'>
                  <a-form-item label={this.$t('platform.fields.status')}>
                    <a-select v-model={this.query.status}>
                      {this.statusDataset.map((item: any) => (
                        <a-select-option value={item.value}>{item.label}</a-select-option>
                      ))}
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
            </template>
          </comp-table-header>
        </comp-card>
        <comp-card>
          <a-card class={styles.base_table} bordered={false}>
            <div slot='extra'>
            <a-button type='primary' class={styles.common_btn} on-click={() => { this.importShow = true; }}>
                {this.$t('buttons.import')}</a-button>
              <a-button type='primary' class={styles.common_btn} on-click={() => this.updateApiTables()}>
                {this.$t('buttons.refresh')}</a-button>
            </div>
            <a-table
              size='small'
              columns={this.columns}
              data-source={this.dataSource}
              pagination={this.pagination}
              scopedSlots={this.fieldsSlotMap}
              on-change={(pagination: any) => {
                this.pagination = pagination;
                this.loadData(false);
              }}
            >
              <span slot='moduleCode'>{this.$t('platform.apis.moduleCode')}</span>
              <span slot='path'>{this.$t('platform.apis.path')}</span>
              <span slot='summary'>{this.$t('platform.apis.summary')}</span>
              <span slot='operationId'>{this.$t('platform.apis.operationId')}</span>
              <span slot='tableNames'>{this.$t('platform.apis.tableRelation')}</span>
              <span slot='status'>{this.$t('platform.fields.status')}</span>
              <span slot='permissionIds'>{this.$t('platform.apis.permission')}</span>
            </a-table>
          </a-card>
        </comp-card>
        <a-modal
          width='1000px'
          title={this.$t('platform.dataPermission.permission')}
          visible={this.dataPermissionShow}
          destroyOnClose={true}
          maskClosable={false}
          on-cancel={() => { this.dataPermissionShow = false; }}
          on-ok={() => { this.dataPermissionShow = false; this.getPermissionItems(); }}
        >
          <data-permission></data-permission>
        </a-modal>
        <a-modal
          width='800px'
          title={this.$t('platform.apis.impostApi')}
          visible={this.importShow}
          destroyOnClose={true}
          maskClosable={false}
          on-cancel={() => { this.importShow = false; this.importModuleCode = undefined; this.importJson = undefined; }}
          on-ok={() => this.importApis()}
        >
          <a-form form={this.importForm} labelCol={{ span: 3 }} wrapperCol={{ span: 21 }}>
            <a-row>
              <a-col span='24'>
                <a-form-item label={this.$t('platform.apis.moduleCode')} required>
                  <a-select options={this.moduleCodeDataset} on-change={(value: any) => { this.importModuleCode = value; }}
                    placeholder={this.$l.getLocale(['controls.select', 'platform.apis.moduleCode'])}
                    v-decorator={['moduleCode', {
                      initialValue: this.importModuleCode,
                      rules: [{ required: true, message: this.$l.getLocale(['controls.select', 'platform.apis.moduleCode']) }]
                    }]}>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col span='24'>
                <a-form-item label={this.$t('platform.apis.apiJson')} required>
                  <a-textarea
                    on-change={(e: any) => { this.importJson = e.target.value; }}
                    placeholder={this.$l.getLocale(['controls.input', 'platform.apis.apiJson'])} rows='10'
                    v-decorator={['importJson', {
                      initialValue: this.importJson,
                      rules: [{ required: true, message: this.$l.getLocale(['controls.input', 'platform.apis.apiJson']) }]
                    }]}
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-modal>
      </div>
    );
  }
}
