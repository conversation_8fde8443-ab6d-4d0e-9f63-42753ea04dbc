/*
变量参考 https://github.com/vueComponent/ant-design-vue/blob/master/components/style/themes/default.less
或 https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/components/style/themes/default.less
*/
/* 重写 AntDesign 的主题样式 */
/* 重写 AntDesign 结束 */
/* 自定义相关 */
/* 基础边距 */
.card_top {
  margin-bottom: 8px !important;
  padding: 0px 0px 0 0px !important;
}
.card_top :global(.ant-card-body) {
  padding: 0px 10px 0 10px !important;
}
.card_top :global(.ant-card-body) :global(.ant-layout) {
  padding-bottom: 0 !important;
}
.card_top :global(.ant-form-item-label) {
  line-height: 20px !important;
}
.button_top {
  text-align: right;
  float: right;
  padding-top: 22px;
}
body {
  background: #f0f2f5 !important;
}
:global(.ant-card-head) {
  min-height: 35px !important;
  border: 0px solid #ddd !important;
}
.main {
  min-width: 1200px;
}
.main .header {
  display: flex;
  background-color: #305382;
  align-items: center;
  box-shadow: 0 2px 5px rgba(23, 39, 72, 0.3);
  z-index: 11;
}
.main .header .logo {
  color: #333;
  display: flex;
  padding-left: 16px;
  align-items: flex-end;
  background: #2C3340;
}
.main .header .logo img {
  width: 120px;
}
.main .header .logo :global(.ant-divider-vertical) {
  height: 1em;
}
.main .header .logo span {
  display: inline-block;
  padding-top: 1px;
  height: 14px;
  line-height: 14px;
  font-size: 16px;
  font-weight: 600;
}
.main .header .blank {
  flex: 1 1 auto;
}
.main .header .top_menu {
  flex: 1 1 auto;
  background: transparent;
  font-size: 14px;
}
.main .header .actions {
  cursor: default;
  color: #ffffff;
  padding-right: 16px;
  display: flex;
  align-items: center;
}
.main .header .actions a {
  color: #ffffff;
}
.main .header .actions button {
  color: #ffffff;
  padding: 0;
}
.main .header .actions .avatar,
.main .header .actions .impersonate_avatar {
  display: flex;
  align-items: center;
}
.main .header .actions .impersonate_avatar {
  padding: 0 8px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  margin-right: 1px;
}
.main .header .actions .impersonate_exit {
  padding: 0 8px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.main .header .actions :global(.ant-avatar) {
  width: 33.6px;
  height: 33.6px;
  background: #557eb5;
}
.main .header .actions .user_info {
  display: flex;
  align-items: center;
  padding: 0 12px;
}
@supports (grid-area: auto) {
  .main .menu_container :global(.ant-layout-sider-children) {
    height: calc(100vh - 48px);
  }
}
.main .menu_container,
.main .menu_container ul,
.main .menu_container li,
.main .menu_container :global(.ant-layout-sider-trigger) {
  transition-duration: 0.2s !important;
}
.main .menu_container :global(.ant-menu.ant-menu-dark ) {
  padding-bottom: 80px !important;
}
.main .menu_container :global(.menu_container > ul) {
  padding-bottom: 80px !important;
}
.main .menu_container ul,
.main .menu_container li {
  width: 100% !important;
}
.main .menu {
  height: calc(100vh - 20px) !important;
  overflow-x: hidden;
  overflow-y: auto;
}
.main .menu::-webkit-scrollbar {
  width: 5px;
  height: 1px;
}
.main .menu::-webkit-scrollbar-thumb {
  border-radius: 5px;
}
.main .menu::-webkit-scrollbar-track {
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
}
.main .menu .subMenu {
  font-size: 14px;
}
.main .menu a {
  color: #b1b3b6;
}
.main .menu a :global(.anticon) {
  font-size: 14px;
}
.main .menu :global(.ant-badge sup) {
  box-shadow: none;
  margin-left: 24px;
  margin-top: -4px;
  transition: all ease 0.2s;
}
.main .menu :global(.ant-drawer-content-wrapper) {
  width: 200px !important;
}
.main .menu :global(.ant-drawer-body) {
  font-size: 16px !important;
}
.main .menu.collapsed :global(.ant-badge sup) {
  margin-left: -6px;
  margin-top: -20px;
}
.main .menu:hover::-webkit-scrollbar-thumb {
  background: #eee;
}
.main :global(.ant-drawer-body) {
  font-size: 16px !important;
}
.main .tabSystem {
  border-top: 1px solid #343D4C;
}
.main .tabSystem:hover {
  background: #182E5A !important;
}
.main .tabSystem,
.main .tabSystemPop {
  text-align: right;
  padding: 20px 30px 20px 30px;
  width: 100%;
  font-size: 16px;
  background: #172748;
  margin-top: 1px;
  color: #fff;
  cursor: pointer;
  overflow: auto;
}
.main .tabSystem .sysbtn,
.main .tabSystemPop .sysbtn {
  width: 20px;
  margin-left: 0px;
  background-color: none;
  border: 0;
  background: url(../../assets/images/leftnav.png) no-repeat;
  background-size: 20px 20px;
  margin-top: 2px;
  height: 20px;
  display: inline-block;
  text-align: right;
  padding: 0 !important;
}
.main .tabSystem > font {
  float: left;
}
.main .top_bar {
  padding: 0px 0 4px 0 !important;
  border-bottom: 0px #e8e8e8 solid !important;
  height: 38px;
  line-height: 38px;
}
.main .top_bar :global(.ant-tabs-tab) {
  padding: 0 !important;
}
.main .top_bar > a {
  display: inline-block;
  margin-right: 10px;
}
.main .breadcrumb {
  display: inline-block;
}
.main .top_bar .menu-fold {
  font-size: 14px;
}
.main .main_content {
  overflow: hidden;
}
.main .content_container {
  background-color: #f2f2f2;
  padding: 8px 8px;
  box-sizing: border-box;
}
.helper_dropdown > li {
  text-align: center !important;
}
@supports (grid-area: auto) {
  .main .menu_container :global(.ant-layout-sider-children) {
    height: calc(100vh - 68px);
  }
}
:global(.ant-popover) :global(.ant-popover-arrow) {
  color: #172748;
}
:global(.ant-popover) :global(.ant-popover-inner) {
  color: #fff !important;
  background: #172748;
}
:global(.ant-popover) :global(.ant-popover-inner-content) {
  color: #B1B3B6 !important;
  padding: 12px 0px 1px 0px;
}
:global(.ant-popover) :global(.ant-popover-inner-content) p {
  cursor: pointer;
  padding: 0 16px;
}
:global(.ant-popover) :global(.ant-popover-inner-content p:hover) {
  color: #fff !important;
  background: #182E5A !important;
}
:global(.ant-popover) .sysIcon {
  width: 30%;
  margin-top: -5px;
}
.sysPop {
  left: 80px !important;
}
.sysPopZhan {
  left: 200px !important;
}
.aboutLink {
  z-index: 100;
  background: #F0F2F5;
  color: #999;
  width: calc(100% - 200px);
  padding: 5px;
  text-align: left;
  position: fixed;
  bottom: 0;
  right: 0;
  text-align: center;
}
.aboutLink a {
  color: #999;
}
.aboutLinkMore {
  z-index: 100;
  background: #F0F2F5;
  color: #999;
  width: calc(100% - 80px);
  padding: 5px;
  text-align: left;
  position: fixed;
  bottom: 0;
  right: 0;
  text-align: center;
}
.aboutLinkMore a {
  color: #999;
}
:global(.ant-menu-sub) {
  background: #061739 !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
}
:global(.ant-menu-sub .ant-menu-item:hover) {
  background: #2165d9 !important;
}
:global(.ant-menu li.ant-menu-submenu) {
  margin-top: -5px;
}
:global(.ant-menu li.ant-menu-submenu:hover) {
  background: #182E5A !important;
}
:global(.ant-popover-placement-rightTop > .ant-popover-content > .ant-popover-arrow) {
  border-bottom-color: #172748 !important;
  border-left-color: #172748 !important;
}
:global(.ant-table .ant-table-body) {
  margin: 0 !important;
}
.card_top {
  margin-bottom: 8px !important;
  padding: 0px 0px 0 0px !important;
}
.card_top :global(.ant-card-body) {
  padding: 0px 10px 0 10px !important;
}
.card_top :global(.ant-card-body) :global(.ant-layout) {
  padding-bottom: 0 !important;
}
:global(.ant-layout-sider-dark .ant-layout-sider-trigger) {
  background: #0c1f46;
}
.linkAbout {
  color: #333;
}
.linkAbout:hover {
  color: #2165d9;
}
.linkAbout:hover i {
  color: #2165d9;
}
