import { Component, Emit, Prop, Vue } from 'vue-property-decorator';
import styles from './organization-tree-table.module.less';
import { OrganizationDto } from './types';
import { organizationTreeTableService } from '@/PlatformStandard/components/OrganizationTreeTable/service';

@Component({})
export class OrganizationTreeTable extends Vue {
  @Prop() visible!: boolean;
  @Prop({ default: [] }) selectedRowKeys!: string[];
  @Prop({ default: [] }) selectedRows!: any;
  private fieldsSlotMap: any = {};
  private listOrganization: OrganizationDto[] = [];
  private columns = [
    { key: 'name', dataIndex: 'name', width: '40%', slots: { title: 'name' } },
    { key: 'code', dataIndex: 'code', width: '40%', slots: { title: 'code' } },
  ];

  private handleCancel() {
    this.closepop(false, [], true);
  }

  private handleOk() {
    this.closepop(false, this.rowSelection, false);
  }

  @Emit('closepop')
  private closepop(result: boolean, resultList: any, iscancel: boolean) {
  }

  private get rowSelection() {
    return {
      selectedRowKeys: this.selectedRowKeys,
      onChange: (selectedRowKeys: any, selectedRows: any) => {
        this.selectedRowKeys = selectedRowKeys;
        this.selectedRows = selectedRows;
      },
      onSelect: (record: any, selected: boolean, selectedRows: any[]) => {
        if (selected) {
          const rowKeys = this.selectedRowKeys;
          const reRows = selectedRows.filter(f => (f.path.indexOf(record.path) > -1 || record.path.indexOf(f.path) > -1)
            && f.path !== record.path);
          if (reRows.length > 0) {
            reRows.forEach(f => {
              if (rowKeys.length > 0 && rowKeys.includes(f.id)) {
                rowKeys.splice(rowKeys.indexOf(f.id), 1);
              }
              selectedRows.splice(selectedRows.indexOf(f), 1);
            });
            this.selectedRowKeys = rowKeys;
            this.selectedRows = selectedRows;
          }
        }
      }
    };
  }

  private load() {
    const params = { 'parent-id': '' };
    organizationTreeTableService.getOrganizations(params).subscribe(data => {
      this.listOrganization = data as OrganizationDto[];
    });
  }

  private onExpand(expanded: any, record: any) {
    if (!expanded) {
      return;
    }
    if (record.children && record.children.length > 0) {
      return;
    }
    const params = { 'parent-id': record.id };
    organizationTreeTableService.getOrganizations(params).subscribe(rs => {
      const children = rs;
      const dataMap = (items: any[]) => {
        items.map(item => {
          if (item.id === record.id) {
            item.children = children;
            return items;
          }
          if (item.children && item.children.length > 0) {
            dataMap(item.children);
          }
        });
      };
      dataMap(this.listOrganization);
    });
  }

  created() {
    this.load();
  }

  render() {
    return(<div>
      <a-modal
        width='900px'
        title={this.$t('platform.fields.organization')}
        visible={this.visible}
        on-cancel={this.handleCancel}
        on-ok={this.handleOk}>
        <a-card>
          <div style='height: 500px;overflow-y: scroll'>
            <a-table
              size='small'
              rowKey='id'
              columns={this.columns}
              dataSource={this.listOrganization}
              scopedSlots={this.fieldsSlotMap}
              row-selection={this.rowSelection}
              on-expand={this.onExpand}
              pagination={false}
            >
              <span slot='name'>{this.$t('platform.fields.name')}</span>
              <span slot='code'>{this.$t('platform.fields.code')}</span>
            </a-table>
          </div>
        </a-card>
      </a-modal>
    </div>);
  }
}
