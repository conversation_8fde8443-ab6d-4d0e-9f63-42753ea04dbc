
import { Organization } from '@/Bpm/views/PlatForm/OrganizationManagement/Organization';
import { User } from '@/Bpm/views/PlatForm/OrganizationManagement/User';
import { UserEdit } from '@/Bpm/views/PlatForm/OrganizationManagement/User/UserEdit';
import { UserView } from '@/Bpm/views/PlatForm/OrganizationManagement/User/UserView';
import { Homes } from '../../../../../src/PlatformStandard/views/PlatForm/HomeManagement';
import { RouteConfig } from 'vue-router';

export const BpmOrganizationManagementRoutes: RouteConfig[] = [
  {
    path: 'homes',
    component: Homes
  },
  {
    path: 'organizations',
    component: Organization
  },
  {
    path: 'users',
    component: User
  },
  {
    path: 'users/view',
    component: UserView
  },
  {
    path: 'users/add',
    component: UserEdit
  },
  {
    path: 'users/edit',
    component: UserEdit
  },
  // { path: '', redirect: 'organizations' }
  { path: '', redirect: 'homes' }
];
