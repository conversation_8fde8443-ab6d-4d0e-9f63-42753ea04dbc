import { dateHelper, formHelper, i18nHelper, notificationHelper } from '@/PlatformStandard/common/utils';
import { WrappedFormUtils } from 'ant-design-vue/types/form/form';
import { Component, Vue } from 'vue-property-decorator';
import { productRegistrationService } from './service';
import { LicenseDto } from './types';
import { CompCard } from '@/PlatformStandard/components';
import styles from '../product.module.less';

@Component({
  components: { CompCard } })
export class ProductRegistration extends Vue {
  private registedForm!: WrappedFormUtils;
  private licenseDto: LicenseDto = {};
  private registedShow = false;
  private productKey = '';
  private fileList: any = [];

  private cancelRegisted() {
    this.registedShow = false;
  }

  private registed() {
    const errMsg = formHelper.validateForm(this.registedForm);
    if (this.fileList.length === 0) {
      errMsg.push(i18nHelper.getLocale(['controls.select', 'platform.product.registration.licenseFile']));
    }
    if (errMsg.length === 0) {
      const formData = new FormData();
      formData.append('fileInfo', this.fileList[0]);
      formData.append('productKey', this.productKey);
      productRegistrationService.registeProduct(formData).subscribe(() => {
        this. getProductLicense();
        notificationHelper.success(i18nHelper.getLocale('messages.success'));
        this.registedShow = false;
      });
    } else {
      notificationHelper.error(errMsg);
    }
  }

  private fileRemove(file: any) {
    const index = this.fileList.indexOf(file);
    const newFileList = this.fileList.slice();
    newFileList.splice(index, 1);
    this.fileList = newFileList;
  }

  private beforeUpload(file: any) {
    this.fileList = [file];
    return false;
  }

  private getProductLicense() {
    productRegistrationService.getLicense().subscribe(data => {
      if (data) {
        this.licenseDto = data;
      }
    });
  }

  created() {
    this.registedForm = this.$form.createForm(this, { name: 'registedForm' });
    this. getProductLicense();
  }

  render() {
    return (
      <div>
        {/* <comp-card title={this.$t('platform.organization.info')}  */}
        <comp-card title={this.$t('platform.product.registration.productInfo')}>
          <div slot='extra'>
            <a-button on-click={() => { this.registedShow = true; }}>
              {this.$t('platform.product.registration.registe')}
            </a-button>
          </div>
          <div>
            <a-form labelCol={{ span: 4 }} wrapperCol={{ span: 17 }}>
              <a-row style='line-height:32px'>
                <a-col span='24'>
                  <a-form-item label={this.$t('platform.product.registration.productId')}>
                    {this.licenseDto.productId}
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col span='24'>
                  <a-form-item style='line-height:32px;' label={this.$t('platform.product.registration.productName')}>
                    {this.licenseDto.productName}
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col span='24'>
                  <a-form-item label={this.$t('platform.product.registration.productVersion')}>
                  {this.licenseDto.productVersion}
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col span='24'>
                  <a-form-item label={this.$t('platform.product.registration.customerName')}>
                    {this.licenseDto.customerName}
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col span='24'>
                  <a-form-item label={this.$t('platform.product.registration.licenseType')}>
                    {this.licenseDto.licenseType !== undefined ? this.licenseDto.licenseType === 0 ?
                      this.$t('platform.product.registration.trial') : this.$t('platform.product.registration.commercial') : ''}
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col span='24'>
                  <a-form-item label={this.$t('platform.product.registration.expirationDate')}>
                    {this.licenseDto.expirationDate ? dateHelper.formatDate(this.licenseDto.expirationDate, 'YYYY-MM-DD') : ''}
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col span='24'>
                  <a-form-item label={this.$t('platform.product.registration.licenseStatus')}>
                    {this.licenseDto.isActive !== undefined ? this.licenseDto.isActive ?
                      this.$t('platform.product.registration.active') : this.$t('platform.product.registration.expiration') : ''}
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col span='24'>
                  <a-form-item label={this.$t('platform.product.registration.manufacturer')}>
                    {this.licenseDto.manufacturer}
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </div>
        </comp-card>
        <a-modal
          width='800px'
          title={this.$t('platform.product.registration.registe')}
          visible={this.registedShow}
          destroyOnClose={true}
          maskClosable={false}
          on-cancel={() => this.cancelRegisted()}
          on-ok={() => this.registed()}
        >
          <div>
            <a-form form={this.registedForm} labelCol={{ span: 3 }} wrapperCol={{ span: 21 }}>
              <a-row>
                <a-col span='24'>
                  <a-form-item label={this.$t('platform.product.registration.productKey')} required>
                    <a-input on-change={(e: any) => { this.productKey = e.target.value; }}
                      placeholder={this.$l.getLocale(['controls.input', 'platform.product.registration.productKey'])}
                      v-decorator={['productKey', {
                        initialValue: this.productKey,
                        rules: [{
                          required: true,
                          message: this.$l.getLocale(['controls.input', 'platform.product.registration.productKey'])
                        }]
                      }]} />
                  </a-form-item>
                </a-col>
                <a-col span='24'>
                  <a-form-item label={this.$t('platform.product.registration.licenseFile')} required>
                    <a-upload
                      multiple={false}
                      file-list={this.fileList}
                      remove={(file: any) => this.fileRemove(file)}
                      before-upload={(file: any) => this.beforeUpload(file)}>
                      <a-button> <a-icon type='upload' />
                        {this.$l.getLocale(['controls.select', 'platform.product.registration.licenseFile'])}
                      </a-button>
                    </a-upload>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </div>
        </a-modal>
      </div >
    );
  }
}
