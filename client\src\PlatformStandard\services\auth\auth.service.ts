import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

import { httpHelper, toCasedStyleObject } from '@/PlatformStandard/common/utils';
import { menuService } from '../menu';
import { User } from './auth.types';
import { languageService } from '../language';

class AuthService {
  user: User = {};
  logoutUri = '';
  adminUri = '';

  private setAuthState(data: any) {
    this.logoutUri = data.logoutUri;
    delete data.logoutUri;
    this.adminUri = data.adminUri;
    delete data.adminUri;
    this.user = data;

    languageService.set(data.language);

    menuService.getAuthorizedMenus();
  }

  getAuthState() {
    return httpHelper.get('/auth/state', undefined, { loading: true }).pipe(
      tap(data => {
        this.setAuthState(data);
        return data;
      })
    );
  }

  login(account: string, password: string, nonce: string, code: string): Observable<void> {
    return httpHelper.post('/auth/state',
      { account: account, password: password, nonce: nonce, captcha: code },
      undefined,
      { loading: false });
  }

  logout(): Observable<void> {
    return httpHelper.delete('/auth/state');
  }

  impersonateout(): Observable<void> {
    return httpHelper.delete('/auth/impersonate');
  }
}

export const authService = new AuthService();
