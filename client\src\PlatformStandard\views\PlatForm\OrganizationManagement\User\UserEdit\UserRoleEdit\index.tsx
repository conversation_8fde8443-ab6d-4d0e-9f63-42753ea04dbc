import { Component, Vue } from 'vue-property-decorator';
import { CompCard, CompTableHeader } from '@/PlatformStandard/components';
import styles from './user-role-edit.module.less';
import { RoleDto, RoleQueryDto } from '../../types';
import { userService } from '../../service';
import { toCasedStyleObject } from '@/PlatformStandard/common/utils';

@Component({
  components: { CompCard, CompTableHeader },
})
export class UserRoleEdit extends Vue {
  private pagination = {
    total: 0,
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    size: 'default',
    showTotal: (total: string) =>
      this.$l
        .getLocale('paginations.total')
        .toString()
        .replace('{{value}}', total),
  };

  private query: RoleQueryDto = { status: 1 };
  private dataSource: RoleDto[] = [];
  private fieldsSlotMap: any = {};
  private selectedRowKeys: string[] = [];
  private selectedRows: any = [];

  private columns = [
    {
      dataIndex: 'roleCode',
      slots: { title: 'roleCode' }
    },
    {
      dataIndex: 'roleName',
      slots: { title: 'roleName' },
    },
    {
      dataIndex: 'description',
      slots: { title: 'description' },
      width: '50%'
    }
  ];

  private loadData(reset: boolean = false) {
    if (reset) {
      this.pagination.current = 1;
    }
    const params = { 'page-index': this.pagination.current, 'page-size': this.pagination.pageSize };
    userService.getRoles({ ...toCasedStyleObject(this.query), ...params }).subscribe(data => {
      this.dataSource = data.items;
      this.pagination.total = data.total;
      this.$forceUpdate();
    });
  }

  private reset() {
    this.query = { status: 1 };
  }

  private onRoleSelectChange(selectedRowKeys: any, selectedRows: any) {
    this.selectedRowKeys = selectedRowKeys;
    this.selectedRows = selectedRows;
  }

  save(): string[] {
    return this.selectedRowKeys;
  }

  created() {
    this.loadData(true);
  }

  render() {
    return (
      <div>
        <comp-card>
          <comp-table-header
            on-search={() => {
              this.loadData(true);
            }}
            on-reset={() => {
              this.reset();
            }}
          >
            <template slot='base'>
              <a-row>
                <a-col span='6' class='mr-1'>
                  <a-input v-model={this.query.roleCode} placeholder={this.$t('platform.fields.code')}></a-input>
                </a-col>
                <a-col span='6' class='mr-1'>
                  <a-input v-model={this.query.roleName} placeholder={this.$t('platform.fields.name')}></a-input>
                </a-col>
              </a-row>
            </template>
          </comp-table-header>
          <a-card class={styles.base_table} bordered={false}>
            <a-table
              rowKeyd='roleId'
              size='default'
              row-selection={{
                selectedRowKeys: this.selectedRowKeys,
                onChange: this.onRoleSelectChange
              }}
              columns={this.columns}
              data-source={this.dataSource}
              pagination={this.pagination}
              scopedSlots={this.fieldsSlotMap}
              on-change={(pagination: any) => {
                this.pagination = pagination;
                this.loadData(false);
              }}
            >
              <span slot='roleCode'>{this.$t('platform.fields.code')}</span>
              <span slot='roleName'>{this.$t('platform.fields.name')}</span>
              <span slot='description'>{this.$t('platform.fields.description')}</span>
            </a-table>
          </a-card>
        </comp-card>
      </div>
    );
  }
}
