import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import { getComponentFromProp } from '@/PlatformStandard/common/utils';
import { CompFormBox } from '../CompFormBox';
import { FormFieldDto } from '../CompEditForm/comp-edit-form.types';
import { Observable, of } from 'rxjs';

@Component({ components: { CompFormBox } })
export class CompModalForm extends Vue {
  private visible = false;
  private _textFields: FormFieldDto[] = [];
  @Prop() textFields!: FormFieldDto[];
  @Prop({ default: false }) value!: boolean;
  @Prop() title!: string;
  @Prop() width!: number;
  @Prop() footer!: any;

  @Emit('ok') onOk(e: any) {}
  @Emit('cancel') onCancel(e: any) {
    this.visible = false;
  }

  @Watch('textFields') onModeChange(v: FormFieldDto[]) {
    this._textFields = [...(v || [])];
    this.$forceUpdate();
  }

  @Watch('value') onValueChange(v: boolean) {
    this.visible = v;
    if (!!v && this.$refs.form) {
      (this.$refs.form as any).resetFormId();
    }
  }

  @Watch('visible') onVisibleChange(v: boolean) {
    this.$emit('input', v);
  }

  validate(): Observable<void> {
    if (!this.$refs.form) {
      return of();
    }

    return (this.$refs.form as any).validate();
  }

  created() {}

  render() {
    const footerDom = getComponentFromProp(this, 'footer');
    return (
      <a-modal
        title={this.title}
        width={this.width || 600}
        visible={this.visible}
        footer={this.footer}
        on-ok={this.onOk}
        on-cancel={this.onCancel}
      >
        {this._textFields && this._textFields.length > 0 ? (
          <a-descriptions>
            {this._textFields.map((field: FormFieldDto) => {
              return (
                <a-descriptions-item label={field.label} span={field.columnCount || 2}>
                  {field.value}
                </a-descriptions-item>
              );
            })}
          </a-descriptions>
        ) : (
          <comp-form-box ref='form'> {this.$slots.default}</comp-form-box>
        )}
        {footerDom ? <template slot='footer'>{footerDom}</template> : null}
      </a-modal>
    );
  }
}
