import { commonService } from '@/PlatformStandard/services/common';
import { Component, Vue } from 'vue-property-decorator';
import { CompCard } from '@/PlatformStandard/components';
import styles from './user-view.module.less';
import { UserDto, UserPositionDto } from '../types';
import { userService } from '../service';
import { i18nHelper } from '@/PlatformStandard/common/utils';
import { languageService } from '@/PlatformStandard/services/language';

@Component({
    components: { CompCard }
})
export class UserView extends Vue {
    private userId = '';
    private currentUser: UserDto = { roles: [], organizationPositions: [], userExtendInfo: {}, reportRelations: [] };
    private statusItems = i18nHelper.getLocaleObject('platform.user.statusDataset');
    private workingStatusItems = i18nHelper.getLocaleObject('platform.user.workingStatusDataset');

    private orgColumns = [
        {
            dataIndex: 'organizationCode',
            slots: { title: 'organizationCode' }
        },
        {
            dataIndex: 'organizationName',
            slots: { title: 'organizationName' },
            scopedSlots: { customRender: 'organizationName' },
        },
        {
            dataIndex: 'positionCode',
            slots: { title: 'positionCode' }
        },
        {
            dataIndex: 'positionName',
            slots: { title: 'positionName' },
        },
        {
            dataIndex: 'primaryPosition',
            slots: { title: 'primaryPosition' },
            scopedSlots: { customRender: 'primaryPosition' },
        },
    ];
    private orgFieldsSlotMap: any = {};

    private roleColumns = [
        {
            dataIndex: 'roleCode',
            slots: { title: 'roleCode' }
        },
        {
            dataIndex: 'roleName',
            slots: { title: 'roleName' },
        },
        {
            dataIndex: 'description',
            slots: { title: 'description' },
            width: '50%'
        }
    ];
    private roleFieldsSlotMap: any = {};

    private reportColumns = [
        {
            dataIndex: 'leaderName',
            slots: { title: 'leaderName' }
        },
        {
            dataIndex: 'jobGrade',
            slots: { title: 'jobGrade' },
        },
        {
            dataIndex: 'positionName',
            slots: { title: 'positionName' },
        },
        {
            dataIndex: 'businessLineName',
            slots: { title: 'businessLineName' },
        }
    ];

    created(): void {
        this.userId = this.$route.query['user-id'] as string;
        languageService.language$.subscribe(() => {
            this.statusItems = i18nHelper.getLocaleObject('platform.user.statusDataset');
            this.workingStatusItems = i18nHelper.getLocaleObject('platform.user.workingStatusDataset');
        });
        if (this.userId) {
            userService.getUserById(this.userId).subscribe(data => {
                this.currentUser = data;
            });

            this.orgFieldsSlotMap['primaryPosition'] = (text: UserPositionDto[], record: UserPositionDto, index: number) => {
                return (
                    <div>
                        {
                            record.primaryPosition ?
                                '是'
                                :
                                '否'
                        }
                    </div>
                );
            };

            this.orgFieldsSlotMap['organizationName'] = (text: UserPositionDto[], record: UserPositionDto, index: number) => {
                return (
                    <span class={styles.org_title}>
                        <a-tooltip>
                            <template slot='title'>
                                {record.organizationFullName}
                            </template>
                            {record.organizationName}
                            <a-icon type='info-circle' theme='filled' />
                        </a-tooltip>
                    </span>
                );
            };
        }
    }

    private goback() {
        commonService.backParentPage(this.$route, this.$router);
    }

    render() {
        return (
            <div>
                <a-card>
                    <div slot='extra'>
                        <a-button on-click={() => this.goback()}>{this.$t('buttons.back')}</a-button>
                    </div>
                    <a-form labelCol={{ span: 5 }} wrapperCol={{ span: 16 }}>
                        <comp-card title={this.$t('platform.user.baseInfo')}>
                            <div>
                                <a-row>
                                    <a-col span='12'>
                                        <a-form-item label={this.$t('platform.fields.account')}>
                                            {this.currentUser.account}
                                        </a-form-item>
                                    </a-col>
                                    <a-col span='12'>
                                        <a-form-item label={this.$t('platform.fields.name')}>
                                            {this.currentUser.name}
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                                <a-row>
                                    <a-col span='12'>
                                        <a-form-item label={this.$t('platform.user.phone')}>
                                            {this.currentUser.phoneNumber}
                                        </a-form-item>
                                    </a-col>
                                    <a-col span='12'>
                                        <a-form-item label={this.$t('platform.user.email')}>
                                            {this.currentUser.email}
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                                <a-row>
                                    <a-col span='12'>
                                        <a-form-item label={this.$t('platform.user.gender')}>
                                            {this.currentUser.gender === 'M' ? this.$t('platform.user.man') : this.$t('platform.user.girl')}
                                        </a-form-item>
                                    </a-col>
                                    <a-col span='12'>
                                        <a-form-item label={this.$t('platform.user.workNumber')}>
                                            {this.currentUser.number}
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                                <a-row>
                                    <a-col span='12'>
                                        <a-form-item label={this.$t('platform.user.upperUser')}>
                                            {this.currentUser.upperUserDisplayName}
                                        </a-form-item>
                                    </a-col>
                                    <a-col span='12'>
                                        <a-form-item label={this.$t('platform.fields.status')}>
                                            {this.statusItems && this.currentUser.status ?
                                                this.statusItems.find((a: any) => a.value === this.currentUser.status).label : ''}
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                                <a-row>
                                    <a-col span='12'>
                                        <a-form-item label={this.$t('platform.user.workingState')}>
                                            {this.workingStatusItems && this.currentUser.userExtendInfo.workingState ?
                                                this.workingStatusItems.find((a: any) =>
                                                    a.value === this.currentUser.userExtendInfo.workingState).label : ''}
                                        </a-form-item>
                                    </a-col>
                                    <a-col span='12'>
                                        <a-form-item label={this.$t('platform.fields.remark')}>
                                            {this.currentUser.remark}
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                            </div>
                        </comp-card>
                    </a-form>
                    <a-tabs default-active-key={1} class='mt-1'>
                        <a-tab-pane key={1} tab={this.$t('platform.user.extendInfo')}>
                            <a-form labelCol={{ span: 5 }} wrapperCol={{ span: 16 }}>
                                <comp-card title={this.$t('platform.user.extendInfo')}>
                                    <div>
                                        <a-row>
                                            <a-col span='12'>
                                                <a-form-item label={this.$t('platform.user.jobGrade')}>
                                                    {this.currentUser.userExtendInfo.jobGrade}
                                                </a-form-item>
                                            </a-col>
                                            <a-col span='12'>
                                                <a-form-item label={this.$t('platform.user.education')}>
                                                    {this.currentUser.userExtendInfo.education}
                                                </a-form-item>
                                            </a-col>
                                        </a-row>
                                        <a-row>
                                            <a-col span='12'>
                                                <a-form-item label={this.$t('platform.user.joinDate')}>
                                                    {this.currentUser.userExtendInfo.joinDate}
                                                </a-form-item>
                                            </a-col>
                                        </a-row>
                                    </div>
                                </comp-card>
                            </a-form>
                        </a-tab-pane>
                        <a-tab-pane key={2} tab={this.$t('platform.user.reportRelation')}>
                            <comp-card title={this.$t('platform.user.reportRelation')}>
                                <a-table
                                    rowKey='reportRelationId'
                                    columns={this.reportColumns}
                                    dataSource={this.currentUser.reportRelations}
                                    pagination={false}
                                >
                                    <span slot='leaderName'>{this.$t('platform.user.leaderName')}</span>
                                    <span slot='businessLineName'>{this.$t('platform.user.businessLineName')}</span>
                                    <span slot='positionName'>{this.$t('platform.organization.positionName')}</span>
                                    <span slot='jobGrade'>{this.$t('platform.user.jobGrade')}</span>
                                </a-table>
                            </comp-card>
                        </a-tab-pane>
                        <a-tab-pane key={3} tab={this.$t('platform.user.orgRelation')}>
                            <comp-card title={this.$t('platform.user.orgRelation')}>
                                <a-table
                                    rowKey='positionId'
                                    columns={this.orgColumns}
                                    dataSource={this.currentUser.organizationPositions}
                                    pagination={false}
                                    scopedSlots={this.orgFieldsSlotMap}
                                >
                                    <span slot='organizationCode'>{this.$t('platform.organization.organizationCode')}</span>
                                    <span slot='organizationName'>{this.$t('platform.organization.organizationName')}</span>
                                    <span slot='positionCode'>{this.$t('platform.organization.positionCode')}</span>
                                    <span slot='positionName'>{this.$t('platform.organization.positionName')}</span>
                                    <span slot='primaryPosition'>{this.$t('platform.organization.isPrimary')}</span>
                                </a-table>
                            </comp-card>
                        </a-tab-pane>
                        <a-tab-pane key={4} tab={this.$t('platform.user.roleRelation')}>
                            <comp-card title={this.$t('platform.user.roleRelation')}>
                                <a-table
                                    rowKey='roleId'
                                    columns={this.roleColumns}
                                    dataSource={this.currentUser.roles}
                                    pagination={false}
                                    scopedSlots={this.roleFieldsSlotMap}
                                >
                                    <span slot='roleCode'>{this.$t('platform.fields.code')}</span>
                                    <span slot='roleName'>{this.$t('platform.fields.name')}</span>
                                    <span slot='description'>{this.$t('platform.fields.description')}</span>
                                </a-table>
                            </comp-card>
                        </a-tab-pane>
                    </a-tabs>
                </a-card>
            </div >
        );
    }
}
