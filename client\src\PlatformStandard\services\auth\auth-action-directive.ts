import { Vue } from 'vue-property-decorator';
import { menuService } from '../menu';

class AuthActionDirective {
  register() {
    Vue.directive('permission', (el, binding, vnode) => {
      if (process.env.NODE_ENV !== 'development' && binding.value) {
        const $this = vnode.context;
        if ($this && $this.$route && $this.$route.path) {
          const actionIds = menuService.getPageAuthorizedActions($this.$route.path);
          if (!actionIds.includes(binding.value)) {
            const comment = document.createComment(' ');
            Object.defineProperty(comment, 'setAttribute', {
              value: () => undefined,
            });
            vnode.elm = comment;
            vnode.text = ' ';
            vnode.isComment = true;
            vnode.context = undefined;
            vnode.tag = undefined;
            if (vnode.data) {
              vnode.data.directives = undefined;
            }

            // if (vnode.componentInstance) {
            //   vnode.componentInstance.$el = comment;
            // }

            if (el.parentNode) {
              el.parentNode.replaceChild(comment, el);
            }
          }
        }
      }
    });
  }
}

export const authActionDirective = new AuthActionDirective();
