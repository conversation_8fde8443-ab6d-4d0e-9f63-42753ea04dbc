/*
变量参考 https://github.com/vueComponent/ant-design-vue/blob/master/components/style/themes/default.less
或 https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/components/style/themes/default.less
*/
/* 重写 AntDesign 的主题样式 */
/* 重写 AntDesign 结束 */
/* 自定义相关 */
/* 基础边距 */
.card_top {
  margin-bottom: 10px !important;
  padding: 0px 0px 0 0px !important;
}
.card_top :global(.ant-card-body) {
  padding: 0px 10px 0 10px !important;
}
.card_top :global(.ant-card-body) :global(.ant-layout) {
  padding-bottom: 0 !important;
}
.card_top :global(.ant-form-item-label) {
  line-height: 20px !important;
}
.button_top {
  text-align: right;
  float: right;
  padding-top: 22px;
}
.card {
  height: 100%;
  overflow: hidden;
  border-radius: 4px !important;
}
.card > :global(.ant-card-head) {
  border-bottom: 1px solid #ddd !important;
  min-height: 51px !important;
}
.card > :global(.ant-card-head) > :global(.ant-card-head-wrapper) {
  padding: 1px 8px 1px 15px ;
}
.card > :global(.ant-card-head) > :global(.ant-card-head-wrapper) > :global(.ant-card-head-title) {
  padding: 12px 10px !important;
  box-sizing: border-box;
  font-size: 16px;
}
.card > :global(.ant-card-head) > :global(.ant-card-head-wrapper::before) {
  content: '';
  position: absolute;
  height: 16px;
  width: 4px;
  background-color: #2165d9;
  top: 16px;
  left: 10px;
}
.card > :global(.ant-card-head-title) {
  padding: 15px !important;
  color: #666 !important;
}
.card :global(.ant-card-extra) {
  padding: 0px;
}
.card :global(.ant-card-body) {
  overflow: auto;
  height: 100%;
}
.card :global(.ant-card-body) :global(table) {
  border-top: 2px solid #2165d9 !important;
}
.card :global(.ant-card-body) :global(.ant-table-thead) {
  background: rgba(33, 101, 217, 0.07) !important;
}
.card :global(.ant-card-body) :global(.ant-table-thead th) {
  color: #666 !important;
  font-weight: bold !important;
}
.card :global(.ant-card-body) :global(.ant-table-tbody tr:nth-child(2n)) {
  background: #F7FAFE !important;
}
.card :global(.ant-card-head) {
  padding: 0px;
  font-weight: bold;
  font-size: 12px;
}
.card :global(.commonBtn) {
  padding: 0;
  margin: 0;
  width: 70px !important;
  height: 30px;
  text-align: center;
  line-height: 32px;
  border-radius: 5px !important;
  background-color: #fff !important;
  color: #333 !important;
  opacity: 1;
}
.card :global(.ant-table-thead > tr > th) {
  border-left: 1px solid #ddd;
  background: #f5f5f5;
}
.card :global(.ant-table-thead > tr > th:first-child) {
  border-left: 0px solid #ddd;
}
.card :global(.ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td) {
  background-color: #f5f5f5;
}
.card :global(.ant-btn-link) {
  color: #1677FF;
}
.card .no-result-tips {
  width: 445px;
  margin: 36px;
}
:global(.ant-card-bordered) {
  border: 0px solid #fff !important;
}
:global(.component-comp-card-module_card_2wiex .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td) {
  background: #EFF4FC !important;
}
.cardTitleBg {
  height: 100%;
  overflow: hidden;
  border-radius: 4px !important;
}
.cardTitleBg > :global(.ant-card-head) {
  min-height: 51px !important;
  background: #f4f7fa;
}
.cardTitleBg > :global(.ant-card-head) > :global(.ant-card-head-wrapper) {
  padding: 1px 8px 1px 15px ;
}
.cardTitleBg > :global(.ant-card-head) > :global(.ant-card-head-wrapper) > :global(.ant-card-head-title) {
  padding: 12px 10px !important;
  box-sizing: border-box;
  font-size: 16px;
}
.cardTitleBg > :global(.ant-card-head) > :global(.ant-card-head-wrapper::before) {
  content: '';
  position: absolute;
  height: 16px;
  width: 4px;
  background-color: #2165d9;
  top: 16px;
  left: 10px;
}
.cardTitleBg > :global(.ant-card-head-title) {
  padding: 15px !important;
  color: #666 !important;
}
.cardTitleBg :global(.ant-card-extra) {
  padding: 0px;
}
.cardTitleBg :global(.ant-card-body) {
  overflow: auto;
  height: 100%;
}
.cardTitleBg :global(.ant-card-body) :global(table) {
  border-top: 2px solid #2165d9 !important;
}
.cardTitleBg :global(.ant-card-body) :global(.ant-table-thead) {
  background: rgba(33, 101, 217, 0.07) !important;
}
.cardTitleBg :global(.ant-card-body) :global(.ant-table-thead th) {
  color: #666 !important;
  font-weight: bold !important;
}
.cardTitleBg :global(.ant-card-body) :global(.ant-table-tbody tr:nth-child(2n)) {
  background: #F7FAFE !important;
}
.cardTitleBg :global(.ant-card-head) {
  padding: 0px;
  font-weight: bold;
  font-size: 12px;
}
.cardTitleBg :global(.commonBtn) {
  padding: 0;
  margin: 0;
  width: 70px !important;
  height: 30px;
  text-align: center;
  line-height: 32px;
  border-radius: 5px !important;
  background-color: #fff !important;
  color: #333 !important;
  opacity: 1;
}
.cardTitleBg :global(.ant-table-thead > tr > th) {
  border-left: 1px solid #ddd;
  background: #f5f5f5;
}
.cardTitleBg :global(.ant-table-thead > tr > th:first-child) {
  border-left: 0px solid #ddd;
}
.cardTitleBg :global(.ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td) {
  background-color: #f5f5f5;
}
.cardTitleBg :global(.ant-btn-link) {
  color: #1677FF;
}
.cardTitleBg .no-result-tips {
  width: 445px;
  margin: 36px;
}
