import { Component, Vue } from 'vue-property-decorator';
import { CompCard, CompTableHeader } from '@/PlatformStandard/components';
import { PermissionTableDto, TableQueryDto } from './types';
import { dataPermissionTableService } from './service';
import { form<PERSON><PERSON>per, guid<PERSON>elper, i18n<PERSON>elper, notificationHelper, toCasedStyleObject } from '@/PlatformStandard/common/utils';
import { ActionEnum } from '@/PlatformStandard/services/menu';
import styles from './DataPermissionTable.module.less';
import { languageService } from '@/PlatformStandard/services/language';
import { WrappedFormUtils } from 'ant-design-vue/types/form/form';

@Component({ components: { CompCard, CompTableHeader } })
export class DataPermissionTable extends Vue {
  private pagination = {
    total: 0,
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    size: 'default',
    showTotal: (total: string) =>
      this.$l
        .getLocale('paginations.total')
        .toString()
        .replace('{{value}}', total),
  };
  private query: TableQueryDto = {};
  private dataSource: any;
  private fieldsSlotMap: any = {};
  private columns = [
    {
      dataIndex: 'tableName',
      slots: { title: 'tableName' },
      width: '400px'
    },
    {
      dataIndex: 'description',
      slots: { title: 'description' },
      scopedSlots: { customRender: 'description' },
    },
    {
      dataIndex: 'action',
      slots: { title: 'action' },
      scopedSlots: { customRender: 'action' },
      width: '200px'
    },
  ];

  private editShow = false;
  private actions = ActionEnum;
  private opration!: string;
  private form!: WrappedFormUtils;
  private currentTable: PermissionTableDto = {};
  private columnTypeItems = i18nHelper.getLocaleObject('platform.dataPermissionTable.columnTypes');

  private loadData(reset: boolean = false) {
    if (reset) {
      this.pagination.current = 1;
    }
    const params = { 'page-index': this.pagination.current, 'page-size': this.pagination.pageSize };
    dataPermissionTableService.getTableList({ ...toCasedStyleObject(this.query), ...params }).subscribe(data => {
      this.dataSource = data.items;
      this.pagination.total = data.total;
      this.$forceUpdate();
    });
  }

  private reset() {
    this.query = {};
  }

  private editTable(record: PermissionTableDto | null) {
    if (record) {
      dataPermissionTableService.getTable(record.tableId).subscribe(data => {
        this.currentTable = data;
        (this.currentTable.columns || []).forEach(column => {
          column.rowKey = guidHelper.generate().replace(/-/g, '').substring(0, 4);
        });
        this.opration = 'edit';
        this.editShow = true;
      });
    } else {
      this.currentTable = { columns: [{ rowKey: guidHelper.generate().replace(/-/g, '').substring(0, 4) }] };
      this.opration = 'add';
      this.editShow = true;
    }
  }

  private deleteTable(tableName: string | undefined) {
    if (tableName) {
      dataPermissionTableService.deleteTable(tableName).subscribe(() => {
        notificationHelper.success(i18nHelper.getLocale('messages.success'));
        this.loadData(true);
      });
    }
  }

  private save() {
    const errMsg = formHelper.validateForm(this.form);
    if (errMsg && errMsg.length > 0) {
      notificationHelper.error(errMsg);
      return;
    }
    dataPermissionTableService.saveTable(this.currentTable).subscribe(() => {
      notificationHelper.success(i18nHelper.getLocale('messages.success'));
      this.cancel();
      this.loadData(true);
    });
  }

  private cancel() {
    this.editShow = false;
    this.currentTable = {};
  }

  private addColumn() {
    (this.currentTable.columns || []).push({ rowKey: guidHelper.generate().replace(/-/g, '').substring(0, 4) });
  }

  private deleteColumn(rowKey: string | undefined) {
    if (rowKey) {
      this.currentTable.columns = (this.currentTable.columns || []).filter(column => column.rowKey !== rowKey);
    }
  }

  created() {
    this.form = this.$form.createForm(this, { name: 'tableForm' });
    this.fieldsSlotMap['action'] = (text: PermissionTableDto[], record: PermissionTableDto, index: number) => {
      return (
        <div>
          <span v-permission={this.actions.edit} class='mr-1'>
            <a-tooltip>
              <template slot='title'>
                {this.$l.getLocale('buttons.edit')}
              </template>
              {/* <a-icon
                type='edit'
                class='text-primary'
                on-click={() => this.editTable(record)} /> */}
              <span v-permission={this.actions.edit}>
                <a-button type='link' on-click={() => this.editTable(record)} size='small' class={styles.list_button}>
                  {this.$l.getLocale('buttons.edit')}
                </a-button>
              </span>
            </a-tooltip>
          </span>
          <span v-permission={this.actions.delete} class='mr-1'>
            <a-tooltip>
              <template slot='title'>
                {this.$l.getLocale('buttons.delete')}
              </template>
              <a-popconfirm title={this.$t('messages.delete')} on-confirm={() => this.deleteTable(record.tableName)}>
                {/* <a-icon
                  type='delete'
                  class='text-primary'
                  style='color: red; cursor: pointer;'
                /> */}
                <span v-permission={this.actions.delete} style='color: red; cursor: pointer;'>
                  {this.$l.getLocale('buttons.delete')}
                </span>
              </a-popconfirm>
            </a-tooltip>
          </span>
        </div>
      );
    };
    this.fieldsSlotMap['description'] = (text: PermissionTableDto[], record: PermissionTableDto, index: number) => {
      return (record.locales as { [key: string]: string })[languageService.current.code];
    };
    this.loadData(true);
  }

  render() {
    return (
      <div>
        <comp-card  class={styles.card_top}>
          <comp-table-header
            on-search={() => {
              this.loadData(true);
            }}
            on-reset={() => {
              this.reset();
            }}
          >
            <template slot='base'>
              <a-row>
                <a-col span='6' class='mr-1'>
                  <a-form-item label={this.$t('platform.dataPermissionTable.tableName')}>
                    <a-input v-model={this.query.tableName}></a-input>
                  </a-form-item>
                </a-col>
                <a-col span='6' class='mr-1'>
                <a-form-item label={this.$t('platform.fields.description')}>
                  <a-input v-model={this.query.desc}></a-input>
                </a-form-item>
                </a-col>
              </a-row>
            </template>
          </comp-table-header>
        </comp-card>
        <comp-card>
          <a-card class={styles.base_table} bordered={false}>
            <div slot='extra' style='line-height:0px;'>
              <a-button
                type='primary'
                v-permission={this.actions.add}
                on-click={() => this.editTable(null)}
                class={styles.common_btn}
              >
                {this.$t('buttons.add')}
              </a-button>
            </div>
            <a-table
              size='small'
              columns={this.columns}
              data-source={this.dataSource}
              pagination={this.pagination}
              scopedSlots={this.fieldsSlotMap}
              on-change={(pagination: any) => {
                this.pagination = pagination;
                this.loadData(false);
              }}
            >
              <span slot='tableName'>{this.$t('platform.dataPermissionTable.tableName')}</span>
              <span slot='description'>{this.$t('platform.fields.description')}</span>
              <span slot='action'>{this.$t('platform.fields.operation')}</span>
            </a-table>
          </a-card>
        </comp-card>
        <a-modal
          width='800px'
          title={this.opration === 'add' ?
            this.$l.getLocale(['buttons.add', 'platform.dataPermissionTable.table'])
            :
            this.$l.getLocale(['buttons.edit', 'platform.dataPermissionTable.table'])}
          visible={this.editShow}
          destroyOnClose={true}
          maskClosable={false}
          on-cancel={this.cancel}
          on-ok={this.save}
        >
          <a-form form={this.form}>
            <a-row>
              <a-col span='24'>
                <a-form-item label={this.$t('platform.dataPermissionTable.tableName')} labelCol={{ span: 3 }} wrapperCol={{ span: 21 }}>
                  <a-input
                    placeholder={this.$l.getLocale(['controls.input', 'platform.dataPermissionTable.tableName'])}
                    on-change={(e: any) => { this.currentTable.tableName = e.target.value; }}
                    v-decorator={[
                      'tableName',
                      {
                        initialValue: this.currentTable.tableName,
                        rules: [{
                          required: true,
                          message: 'data required'
                        }]
                      }]}
                  ></a-input>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col span='24'>
                <a-form-item label={this.$t('platform.dataPermissionTable.descCn')} labelCol={{ span: 3 }} wrapperCol={{ span: 21 }}>
                  <a-input
                    placeholder={this.$l.getLocale(['controls.input', 'platform.dataPermissionTable.descCn'])}
                    on-change={(e: any) => { this.currentTable.descCn = e.target.value; }}
                    v-decorator={[
                      'descCn',
                      {
                        initialValue: this.currentTable.descCn,
                        rules: [{
                          required: true,
                          message: 'data required'
                        }]
                      }]}
                  ></a-input>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col span='24'>
                <a-form-item label={this.$t('platform.dataPermissionTable.descEn')} labelCol={{ span: 3 }} wrapperCol={{ span: 21 }}>
                  <a-input
                    placeholder={this.$l.getLocale(['controls.input', 'platform.dataPermissionTable.descEn'])}
                    on-change={(e: any) => { this.currentTable.descEn = e.target.value; }}
                    v-decorator={[
                      'descEn',
                      {
                        initialValue: this.currentTable.descEn,
                        rules: [{
                          required: true,
                          message: 'data required'
                        }]
                      }]}
                  ></a-input>
                </a-form-item>
              </a-col>
            </a-row>
            <a-card title={this.$t('platform.dataPermissionTable.column')} class={styles.card} size='small'>
              <div class={styles.block} style='text-align:right;'>
                <a-button
                  type='link'
                  icon='plus-circle'
                  on-click={this.addColumn}>
                  {this.$t('platform.dataPermissionTable.addColumn')}
                </a-button>
              </div>
              {
                (this.currentTable.columns || []).map((column, index) => (
                  <div class={styles.block} style='display:flex;align-items:center;'>
                    <div class={styles.content} style='width:30px; margin-bottom: 8px; text-align: center;'>{index + 1}</div>
                    <div class={styles.content} style='width:200px;'>
                      <a-form-item>
                        <a-input
                          placeholder={this.$l.getLocale(['controls.input', 'platform.dataPermissionTable.columnName'])}
                          style='width:100%;'
                          on-change={(e: any) => { column.columnName = e.target.value; }}
                          v-decorator={[
                            'columnName_' + column.rowKey,
                            {
                              initialValue: column.columnName,
                              rules: [{
                                required: true,
                                message: this.$l.getLocale(['controls.input', 'platform.dataPermissionTable.columnName'])
                              }]
                            }]}
                        ></a-input>
                      </a-form-item>
                    </div>
                    <div class={styles.content} style='width:200px;'>
                      <a-form-item>
                        <a-input
                          placeholder={this.$l.getLocale(['controls.input', 'platform.dataPermissionTable.descCn'])}
                          style='width:100%;'
                          on-change={(e: any) => { column.descCn = e.target.value; }}
                          v-decorator={[
                            'descCn_' + column.rowKey,
                            {
                              initialValue: column.descCn,
                              rules: [{
                                required: true,
                                message: this.$l.getLocale(['controls.input', 'platform.dataPermissionTable.descCn'])
                              }]
                            }]}
                        ></a-input>
                      </a-form-item>
                    </div>
                    <div class={styles.content} style='width:200px;'>
                      <a-form-item>
                        <a-input
                          placeholder={this.$l.getLocale(['controls.input', 'platform.dataPermissionTable.descEn'])}
                          style='width:100%;'
                          on-change={(e: any) => { column.descEn = e.target.value; }}
                          v-decorator={[
                            'descEn_' + column.rowKey,
                            {
                              initialValue: column.descEn,
                              rules: [{
                                required: true,
                                message: this.$l.getLocale(['controls.input', 'platform.dataPermissionTable.descEn'])
                              }]
                            }]}
                        ></a-input>
                      </a-form-item>
                    </div>
                    <div class={styles.content} style='width:150px;'>
                      <a-form-item>
                        <a-select
                          options={this.columnTypeItems}
                          placeholder={this.$l.getLocale(['controls.select', 'platform.dataPermissionTable.columnType'])}
                          style='width:100%;'
                          on-change={(value: any) => { column.columnType = value; }}
                          v-decorator={[
                            'columnType_' + column.rowKey,
                            {
                              initialValue: column.columnType,
                              rules: [{
                                required: true,
                                message: this.$l.getLocale(['controls.input', 'platform.dataPermissionTable.columnType'])
                              }]
                            }]}
                        >
                        </a-select>
                      </a-form-item>
                    </div>
                    <div class={styles.content} style='width:10px; margin-bottom: 8px; text-align: left;'>
                      {
                        index > 0 ?
                          <a-popconfirm title={this.$t('messages.delete')} on-confirm={() => this.deleteColumn(column.rowKey)}>
                            <a-icon
                              type='delete'
                              style='color: red; cursor: pointer;'
                            />
                          </a-popconfirm>
                          :
                          null
                      }
                    </div>
                  </div>
                ))
              }
            </a-card>
          </a-form>
        </a-modal>
      </div>
    );
  }
}
