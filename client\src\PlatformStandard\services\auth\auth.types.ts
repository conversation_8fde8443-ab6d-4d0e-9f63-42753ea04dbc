export interface User {
  id?: string;
  name?: string;
  impersonatorId?: string;
  account?: string;
  workNumber?: string;
  menus?: any;
  positions?: UserPosition[];
  featrues?: Featrue[];
  language?: string;
  isAdmin?: boolean;
}

export interface UserPosition {
  label: string;
  value: string;
  organizationPath: string;
  organizationPathText: string;
}

export interface Featrue {
  menuPath: string;
  permissions: string[];
}
