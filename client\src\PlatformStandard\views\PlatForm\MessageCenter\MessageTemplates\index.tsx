import { Component, Vue } from 'vue-property-decorator';
import { CompCard, CompTableHeader } from '@/PlatformStandard/components';
import styles from './MessageTemplate.module.less';
import { MessageTemplateDto, MessageTemplateQueryDto, TemplateStatusChange } from './types';
import { formHelper, i18nHelper, notificationHelper, toCasedStyleObject } from '@/PlatformStandard/common/utils';
import { ActionEnum } from '@/PlatformStandard/services/menu';
import { WrappedFormUtils } from 'ant-design-vue/types/form/form';
import { messageTemplateService } from './service';
import { languageService } from '@/PlatformStandard/services/language';
import { commonService } from '@/PlatformStandard/services/common';

@Component({ components: { CompCard, CompTableHeader } })
export class MessageTemplate extends Vue {
  private pagination = {
    total: 0,
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    size: 'default',
    showTotal: (total: string) =>
      this.$l
        .getLocale('paginations.total')
        .toString()
        .replace('{{value}}', total),
  };
  private query: MessageTemplateQueryDto = {};
  private dataSource: any;
  private fieldsSlotMap: any = {};
  private statusDataset: any = [];
  private templateTypeDataset: any = [];

  private columns = [
    {
      dataIndex: 'applicationName',
      slots: { title: 'applicationName' },
      width: '10%'
    },
    {
      dataIndex: 'templateCode',
      slots: { title: 'templateCode' },
      width: '15%'
    },
    {
      dataIndex: 'templateName',
      slots: { title: 'templateName' },
      width: '20%'
    },
    {
      dataIndex: 'templateSubject',
      slots: { title: 'templateSubject' },
    },
    {
      dataIndex: 'templateType',
      slots: { title: 'templateType' },
      scopedSlots: { customRender: 'templateType' },
      width: '10%'
    },
    {
      dataIndex: 'status',
      slots: { title: 'status' },
      scopedSlots: { customRender: 'status' },
      width: '10%'
    },
    {
      dataIndex: 'action',
      slots: { title: 'action' },
      scopedSlots: { customRender: 'action' },
      width: '15%'
    },
  ];

  private editShow = false;
  private actions = ActionEnum;
  private opration!: string;
  private form!: WrappedFormUtils;
  private currentData: MessageTemplateDto = {};
  private applicationDataset: any = [];

  private loadData(reset: boolean = false) {
    if (reset) {
      this.pagination.current = 1;
    }
    const params = { 'page-index': this.pagination.current, 'page-size': this.pagination.pageSize };
    messageTemplateService.getMessageTemplateList({ ...toCasedStyleObject(this.query), ...params }).subscribe(data => {
      this.dataSource = data.items;
      this.pagination.total = data.total;
      this.$forceUpdate();
    });
  }

  private reset() {
    this.query = {};
  }

  private edit(record: MessageTemplateDto | null) {
    if (record) {
      // this.currentData = JSON.parse(JSON.stringify(record));
      messageTemplateService.getMessageTemplate(String(record.templateId)).subscribe(rs => {
        this.currentData = rs;
        this.opration = 'edit';
      });
    } else {
      this.currentData = {};
      this.opration = 'add';
    }
    this.editShow = true;
  }

  private changeStatus(record: MessageTemplateDto | null) {
    if (record) {
      const data: TemplateStatusChange = {
        templateId: record.templateId,
        status: record.status === 0 ? 1 : 0
      };
      messageTemplateService.changeTemplateStatus(data).subscribe(() => {
        notificationHelper.success(i18nHelper.getLocale('messages.success'));
        this.loadData(true);
      });
    }
  }

  private delete(templateId: string | undefined) {
    if (templateId !== undefined) {
      messageTemplateService.deleteMessageTemplate(templateId).subscribe(() => {
        notificationHelper.success(i18nHelper.getLocale('messages.success'));
        this.loadData(true);
      });
    }
  }

  private save() {
    const errMsg = formHelper.validateForm(this.form);
    if (errMsg && errMsg.length > 0) {
      notificationHelper.error(errMsg);
      return;
    }

    messageTemplateService.saveMessageTemplate(this.currentData).subscribe(() => {
      notificationHelper.success(i18nHelper.getLocale('messages.success'));
      this.cancel();
      this.loadData(true);
    });
  }

  private cancel() {
    this.editShow = false;
    this.currentData = {};
  }

  created() {
    if (this.$route.query['app-code']) {
      this.query.appCode = this.$route.query['app-code'] as string;
    }
    if (!this.query.appCode && sessionStorage.getItem('appCode')) {
      this.query.appCode = sessionStorage.getItem('appCode') as string;
    }
    this.form = this.$form.createForm(this, { name: 'tableForm' });
    languageService.langeAsync$.subscribe(() => {
      this.statusDataset = i18nHelper.getLocaleObject('platform.message.dataStatus');
      this.templateTypeDataset = i18nHelper.getLocaleObject('platform.message.templateTypeItems');
    });
    commonService.getApplications().subscribe(rs => {
      this.applicationDataset = rs;
    });
    this.fieldsSlotMap['action'] = (text: MessageTemplateDto[], record: MessageTemplateDto, index: number) => {
      return (
        <div>
          <span v-permission={this.actions.edit} class='mr-1'>
            <a-tooltip>
              <template slot='title'>
                {this.$l.getLocale('buttons.edit')}
              </template>
              <span>
                <a-button type='link' on-click={() => this.edit(record)} size='small' class={styles.list_button}>
                  {this.$l.getLocale('buttons.edit')}
                </a-button>
              </span>
            </a-tooltip>
          </span>
          <span v-permission={this.actions.edit} class='mr-1'>
            <a-tooltip>
              <template slot='title'>
                {record.status === 0 ? this.$l.getLocale('buttons.enable') : this.$l.getLocale('buttons.disable')}
              </template>
              <span>
                <a-button type='link' on-click={() => this.changeStatus(record)} size='small' class={styles.list_button}>
                  {record.status === 0 ? this.$l.getLocale('buttons.enable') : this.$l.getLocale('buttons.disable')}
                </a-button>
              </span>
            </a-tooltip>
          </span>
          <span v-permission={this.actions.delete} class='mr-1'>
            <a-tooltip>
              <template slot='title'>
                {this.$l.getLocale('buttons.delete')}
              </template>
              <a-popconfirm title={this.$t('messages.delete')} on-confirm={() => this.delete(record.templateId)}>
                <span style='color: red; cursor: pointer;'>
                  {this.$l.getLocale('buttons.delete')}
                </span>
              </a-popconfirm>
            </a-tooltip>
          </span>
        </div>
      );
    };
    this.fieldsSlotMap['templateType'] = (text: MessageTemplateDto[], record: MessageTemplateDto, index: number) => {
      return this.templateTypeDataset.find((d: any) => d.value === record.templateType).label;
    };
    this.fieldsSlotMap['status'] = (text: MessageTemplateDto[], record: MessageTemplateDto, index: number) => {
      return this.statusDataset.find((d: any) => d.value === record.status).label;
    };
    this.loadData(true);
  }

  render() {
    return (
      <div>
        <comp-card class={styles.card_top}>
          <comp-table-header
            on-search={() => {
              this.loadData(true);
            }}
            on-reset={() => {
              this.reset();
            }}
          >
            <template slot='base'>
              <a-row>
                <a-col span='4' class='mr-1'>
                  <a-form-item label={this.$t('platform.fields.application')}>
                    <a-select default-value={this.query.appCode}
                        on-change={(value: any) => this.query.appCode = value}
                        placeholder={this.$t('platform.fields.application')}
                        allowClear>
                      {this.applicationDataset.map((item: any) => (
                        <a-select-option value={item.code}>{item.label}</a-select-option>
                      ))}
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col span='5' class='mr-1'>
                  <a-form-item label={this.$t('platform.message.templateCode')}>
                    <a-input v-model={this.query.templateCode} placeholder={this.$t('platform.message.templateCode')}></a-input>
                  </a-form-item>
                </a-col>
                <a-col span='5' class='mr-1'>
                  <a-form-item label={this.$t('platform.message.templateName')}>
                    <a-input v-model={this.query.templateName} placeholder={this.$t('platform.message.templateName')}></a-input>
                  </a-form-item>
                </a-col>
                <a-col span='4' class='mr-1'>
                  <a-form-item label={this.$t('platform.message.templateType')}>
                    <a-select v-model={this.query.templateType} placeholder={this.$t('platform.message.templateType')} allowClear>
                      {this.templateTypeDataset.map((item: any) => (
                        <a-select-option value={item.value}>{item.label}</a-select-option>
                      ))}
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col span='4' class='mr-1'>
                  <a-form-item label={this.$t('platform.fields.status')}>
                    <a-select v-model={this.query.status} placeholder={this.$t('platform.fields.status')} allowClear>
                      {this.statusDataset.map((item: any) => (
                        <a-select-option value={item.value}>{item.label}</a-select-option>
                      ))}
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
            </template>
          </comp-table-header>
        </comp-card>
        <comp-card>
          <a-card class={styles.base_table} bordered={false}>
            <div slot='extra'>
              <a-button
                type='primary'
                v-permission={this.actions.add}
                on-click={() => this.edit(null)}
                class={styles.common_btn}
              >
                {this.$t('buttons.add')}
              </a-button>
            </div>
            <a-table
              size='small'
              columns={this.columns}
              data-source={this.dataSource}
              pagination={this.pagination}
              scopedSlots={this.fieldsSlotMap}
              on-change={(pagination: any) => {
                this.pagination = pagination;
                this.loadData(false);
              }}
            >
              <span slot='templateCode'>{this.$t('platform.message.templateCode')}</span>
              <span slot='templateName'>{this.$t('platform.message.templateName')}</span>
              <span slot='templateSubject'>{this.$t('platform.message.templateSubject')}</span>
              <span slot='templateType'>{this.$t('platform.message.templateType')}</span>
              <span slot='applicationName'>{this.$t('platform.fields.application')}</span>
              <span slot='status'>{this.$t('platform.fields.status')}</span>
              <span slot='action'>{this.$t('platform.fields.operation')}</span>
            </a-table>
          </a-card>
        </comp-card>
        <a-modal
          width='800px'
          title={this.opration === 'add' ?
            this.$l.getLocale(['buttons.add', 'platform.message.messageTemplate'])
            :
            this.$l.getLocale(['buttons.edit', 'platform.message.messageTemplate'])}
          visible={this.editShow}
          destroyOnClose={true}
          maskClosable={false}
          on-cancel={this.cancel}
          on-ok={this.save}
        >
          <a-form form={this.form} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
            <a-row>
              <a-col span='12'>
                <a-form-item label={this.$t('platform.message.templateCode')} required>
                  <a-input on-change={(e: any) => { this.currentData.templateCode = e.target.value; }}
                    placeholder={this.$l.getLocale(['controls.input', 'platform.message.templateCode'])}
                    v-decorator={['templateCode', {
                      initialValue: this.currentData.templateCode,
                      rules: [{ required: true, message: this.$l.getLocale(['controls.input', 'platform.message.templateCode']) }]
                    }]} />
                </a-form-item>
              </a-col>
              <a-col span='12'>
                <a-form-item label={this.$t('platform.message.templateName')} required>
                  <a-input on-change={(e: any) => { this.currentData.templateName = e.target.value; }}
                    placeholder={this.$l.getLocale(['controls.input', 'platform.message.templateName'])}
                    v-decorator={['templateName', {
                      initialValue: this.currentData.templateName,
                      rules: [{ required: true, message: this.$l.getLocale(['controls.input', 'platform.message.templateName']) }]
                    }]} />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col span='12'>
                <a-form-item label={this.$t('platform.message.templateSubject')}>
                  <a-input v-model={this.currentData.templateSubject}
                    placeholder={this.$l.getLocale(['controls.input', 'platform.message.templateSubject'])} />
                </a-form-item>
              </a-col>
              <a-col span='12'>
                <a-form-item label={this.$t('platform.message.templateType')} required>
                  <a-select
                    options={this.templateTypeDataset}
                    placeholder={this.$l.getLocale(['controls.select', 'platform.message.templateType'])}
                    style='width:100%;'
                    on-change={(value: any) => { this.currentData.templateType = value; }}
                    v-decorator={[
                      'templateType',
                      {
                        initialValue: this.currentData.templateType,
                        rules: [{
                          required: true,
                          message: this.$l.getLocale(['controls.select', 'platform.message.templateType'])
                        }]
                      }]}
                  >
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col span='12'>
                <a-form-item label={this.$t('platform.fields.application')}>
                  <a-select
                    allowClear
                    placeholder={this.$l.getLocale(['controls.select', 'platform.fields.application'])}
                    style='width:100%;'
                    v-model={this.currentData.applicationId}
                  >
                    {this.applicationDataset.map((item: any) => (
                      <a-select-option value={item.value}>{item.label}</a-select-option>
                    ))}
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col span='24'>
                <a-form-item label={this.$t('platform.message.templateContent')} labelCol={{ span: 2 }} wrapperCol={{ span: 22 }} required>
                  <a-textarea on-change={(e: any) => { this.currentData.templateContent = e.target.value; }}
                    placeholder={this.$l.getLocale(['controls.input', 'platform.message.templateContent'])}
                    auto-size={{ minRows: 10, maxRows: 15 }}
                    v-decorator={['templateContent', {
                      initialValue: this.currentData.templateContent,
                      rules: [{ required: true, message: this.$l.getLocale(['controls.input', 'platform.message.templateContent']) }]
                    }]} />
                </a-form-item>
              </a-col>
            </a-row>
            {
              this.opration === 'edit' ?
                <a-row>
                  <a-col span='12'>
                    <a-form-item label={this.$t('platform.fields.status')} required>
                      <a-select
                        options={this.statusDataset}
                        placeholder={this.$l.getLocale(['controls.select', 'platform.fields.status'])}
                        style='width:100%;'
                        on-change={(value: any) => { this.currentData.status = value; }}
                        v-decorator={[
                          'status',
                          {
                            initialValue: this.currentData.status,
                            rules: [{
                              required: true,
                              message: this.$l.getLocale(['controls.select', 'platform.fields.status'])
                            }]
                          }]}
                      >
                      </a-select>
                    </a-form-item>
                  </a-col>
                </a-row>
                :
                null
            }
          </a-form>
        </a-modal>
      </div>
    );
  }
}
