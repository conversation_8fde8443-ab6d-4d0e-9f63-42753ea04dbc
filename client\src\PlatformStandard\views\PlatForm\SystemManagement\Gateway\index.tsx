import { Component, Vue } from 'vue-property-decorator';
import { commGatewayService } from './service';
import styles from './Gateway.module.less';
import { CompCard } from '@/PlatformStandard/components';

@Component({ components: { CompCard } })
export class GatewayManagement extends Vue {
  private dataList = [];
  private fieldsSlotMap: any = {};
  private pagination: any;
  private addRowData = '';
  private isAdd = false;
  private columns = [
    {
      slots: { title: 'name' },
      dataIndex: 'name',
      scopedSlots: { customRender: 'name' },
      width: '300px'
    },
    {
      slots: { title: 'address' },
      dataIndex: 'value',
      scopedSlots: { customRender: 'value' }
    },
    {
      dataIndex: 'operation',
      slots: { title: 'operation' },
      scopedSlots: { customRender: 'operation' },
      width: '300px'
    }
  ];
  private getGateWayList(): void {
    commGatewayService.getGateways('').subscribe(data => {
      this.dataList = data;
      if (this.dataList) {
        this.pagination = data.length;
      }
      console.log(data);
    });
  }
  private addData(): void {
    const data = this.dataList as any;
    data.push({
      name: '',
      code: '',
      isEdit: true,
      isNew: true
  });
  }
  private edit(index: number): void {
    const newData = [...this.dataList] as any;
    const target = newData.filter((item: { name: any; }) => index === item.name)[0];
    if (target) {
        target.isEdit = true;
        this.dataList = newData;
    }
  }
  private save(e: any): void {
    const data = this.dataList as any;
    let param = {};
    if (data[e].value !== undefined ) {
      param = {
        name: data[e].name,
        value: data[e].value + ',' + this.addRowData
      };
    } else {
      param = {
        name: data[e].name,
        value:  this.addRowData
      };
    }
    // tslint:disable-next-line:no-shadowed-variable
    commGatewayService.saveGateways(param).subscribe(data => {
      this.getGateWayList();
    });
  }
  private cancel(index: any): void {
    const newData = [...this.dataList] as any;
    const target = newData.filter((item: { name: any; }) => index === item.name)[0];
    if (target) {
        if (target.isNew) {
            newData.splice(newData.indexOf(target), 1);
        } else {
            target.isEdit = false;
        }
        this.dataList = newData;
    }
  }
  private delete(e: any): void {
    commGatewayService.delGateways(e).subscribe(data => {
      this.getGateWayList();
    });
  }
  private addDataRow(): void {
    this.isAdd = true;
  }

  created(): void {
    this.getGateWayList();
    this.fieldsSlotMap['name'] = (text: any, record: any, index: number, cell: string, row: any) => {
      return (record.isEdit ? <a-input value={text} on-change={(v: any) => record.name = v.target.value} ></a-input> : { text });
    };
    this.fieldsSlotMap['value'] = (text: any, record: any, index: number, cell: string, row: any) => {
      let showBox: any;
      let testArry = [];
      if (text !== undefined && text.length > 0) {
        testArry = text.split(',');
      }

      const tagBox = [];
      for (const item of testArry) {
        tagBox.push(<a-tag>{item}</a-tag>);
      }
      if (record.isEdit && this.isAdd === false) {
        showBox = (<div>{tagBox}<a-button type='dashed' on-click={this.addDataRow}>+</a-button></div>);
      } else if (record.isEdit && this.isAdd) {
        showBox = (<div>{tagBox}<a-input style='width:150px' v-model={this.addRowData}></a-input></div>);
      } else {
        showBox = (tagBox);
      }
      return showBox;
    };
    this.fieldsSlotMap['operation'] = (text: any, record: any, index: number) => {
      return (record.isEdit ? [
        <a-button type='link' on-click={() => this.save(index)}>
            {this.$t('buttons.save')}
        </a-button>,
        <a-button type='link' on-click={() => this.cancel(record.name)}>
            {this.$t('buttons.cancel')}
        </a-button>
    ] : [
            <a-button type='link' on-click={() => this.edit(record.name)}>
                {this.$t('buttons.edit')}
            </a-button>,
            <a-button type='link' on-click={() => this.delete(record.name)}>
                {this.$t('buttons.delete')}
            </a-button>
        ]);
    };
  }
  render() {
    return <div>
      <comp-card>
        <a-card class={styles.base_table} bordered={false}>
          <div slot='extra'  style='line-height:0'>
            <a-button type='primary' class={styles.common_btn} on-click={this.addData}>{this.$l.getLocale(['buttons.add'])}</a-button>
          </div>
          <a-table
            size='small'
            bordered={false}
            columns={this.columns}
            dataSource={this.dataList}
            scopedSlots={this.fieldsSlotMap}
            pagination={false}
            rowKey={(_record: any, index: any) => index}
            >
            <span slot='name'>{this.$t('platform.gateway.name')}</span>
            <span slot='address'>{this.$t('platform.gateway.address')}</span>
            <span slot='operation'>{this.$t('platform.gateway.operation')}</span>
          </a-table>
          {/* <a-button style='width:100%;margin-top:50px;' type='dashed' on-click={this.addData}>
          +{this.$l.getLocale(['buttons.add'])}</a-button> */}
        </a-card>
      </comp-card>
    </div>;
  }
}
