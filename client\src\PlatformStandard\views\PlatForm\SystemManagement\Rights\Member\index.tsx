import { Component, Vue } from 'vue-property-decorator';
import { CompCard, CompTableHeader, OrganizationTreeTable, SelectUser } from '@/PlatformStandard/components';
import { UserTransfer } from '@/PlatformStandard/components';
import { i18nHelper, notificationHelper, toCasedStyleObject } from '@/PlatformStandard/common/utils';
import { RoleMemberQueryDto, RoleOrganizationRelationDto, RoleUserRelationDto } from '../types';
import { rightService } from '../service';
import { UserItemDto } from '@/PlatformStandard/components/OrganizationUser/types';
import styles from './Member.module.less';
import { commonService } from '@/PlatformStandard/services/common';

@Component({ components: { CompCard, CompTableHeader, UserTransfer, SelectUser, OrganizationTreeTable } })
export class RightsMember extends Vue {
  // private multiple: true;
  private query: RoleMemberQueryDto = {};
  private userFieldsSlotMap: any = {};
  private organizationFieldsSlotMap: any = {};
  private userDataSource: RoleUserRelationDto[] = [];
  private organizationDataSource: RoleOrganizationRelationDto[] = [];
  private roleId = '';
  private userAddVisible = false;
  private organizationVisible = false;
  private selectedOrganizationKeys: string[] = [];
  private selectedOrganizations: any = [];
  private userData: UserItemDto[] = [];
  private userPagination = {
    total: 0,
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    size: 'default',
    showTotal: (total: string) =>
      this.$l
        .getLocale('paginations.total')
        .toString()
        .replace('{{value}}', total),
  };
  private organizationPagination = {
    total: 0,
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    size: 'default',
    showTotal: (total: string) =>
      this.$l
        .getLocale('paginations.total')
        .toString()
        .replace('{{value}}', total),
  };

  private userColumns = [
    {
      dataIndex: 'userLoginId',
      slots: { title: 'userLoginId' }
    },
    {
      dataIndex: 'userName',
      slots: { title: 'userName' },
    },
    {
      dataIndex: 'operation',
      slots: { title: 'operation' },
      scopedSlots: { customRender: 'operation' },
      width: '200px'
    },
  ];

  private organizationColumns = [
    {
      dataIndex: 'organizationCode',
      slots: { title: 'organizationCode' }
    },
    {
      dataIndex: 'organizationName',
      slots: { title: 'organizationName' },
    },
    {
      dataIndex: 'organizationFullName',
      slots: { title: 'organizationFullName' },
    },
    {
      dataIndex: 'operation',
      slots: { title: 'operation' },
      scopedSlots: { customRender: 'operation' },
      width: '200px'
    },
  ];

  private loadUserData(reset: boolean = false) {
    if (reset) {
      this.userPagination.current = 1;
    }
    this.query.roleId = this.roleId;
    const params = { 'page-index': this.userPagination.current, 'page-size': this.userPagination.pageSize };
    rightService.getUsers({ ...toCasedStyleObject(this.query), ...params }).subscribe(data => {
      this.userDataSource = data.items;
      this.userPagination.total = data.total;
      this.$forceUpdate();
    });
  }

  private loadOrganizationData(reset: boolean = false) {
    if (reset) {
      this.organizationPagination.current = 1;
    }
    this.query.roleId = this.roleId;
    const params = {
      'page-index': this.organizationPagination.current,
      'page-size': this.organizationPagination.pageSize
    };
    rightService.getOrganizations({ ...toCasedStyleObject(this.query), ...params }).subscribe(data => {
      this.organizationDataSource = data.items;
      this.organizationPagination.total = data.total;
      if (data.items.length > 0) {
        this.selectedOrganizationKeys = [];
        this.selectedOrganizations = [];
        data.items.forEach((item: RoleOrganizationRelationDto) => {
            this.selectedOrganizationKeys = [...this.selectedOrganizationKeys, item.organizationId];
            this.selectedOrganizations.push({ id: item.organizationId, name: item.organizationName, path: item.organizationFullCode });
        });
      }
      this.$forceUpdate();
    });
  }

  private userReset() {
    this.query.userInfo = '';
  }

  private organizationReset() {
    this.query.organizationInfo = '';
  }

  private deleteUser(row: RoleUserRelationDto) {
    rightService.deleteUser(String(row.roleUserRelationId)).subscribe(() => {
      this.loadUserData(true);
      notificationHelper.success(i18nHelper.getLocale('messages.success'));
    });
  }

  private deleteOrganization(row: RoleOrganizationRelationDto) {
    rightService.deleteOrganization(String(row.roleOrganizationRelationId)).subscribe(() => {
      this.loadOrganizationData(true);
      notificationHelper.success(i18nHelper.getLocale('messages.success'));
    });
  }

  private addUser() {
    if ((this.userData || []).length === 0) {
      this.$message.warning(`${i18nHelper.getLocale('controls.select')}${i18nHelper.getLocale('fields.user')}`);
    }
    this.userAddVisible = false;
    const roleUsers: RoleUserRelationDto[] = this.userData.map(m => ({
      roleId: this.roleId,
      userId: m.userId + ''
    }));
    rightService.addUser(roleUsers)
      .subscribe(() => {
        this.loadUserData(true);
        notificationHelper.success(i18nHelper.getLocale('messages.success'));
      });
  }

  private onCloseUserPop(result: boolean, resultList: any, iscancel: boolean) {
    if (iscancel) {
      this.userAddVisible = false;
      return;
    }
    // debugger
    this.userData = resultList;
    // this.modalVisible = result;
    if ((this.userData || []).length === 0) {
      this.$message.error(`${i18nHelper.getLocale('controls.select')}${i18nHelper.getLocale('platform.fields.user')}`);
      return;
    }
    this.userAddVisible = false;
    const roleUsers: RoleUserRelationDto[] = this.userData.map(m => ({
      roleId: this.roleId,
      userId: m.value + ''
    }));
    rightService.addUser(roleUsers)
      .subscribe(() => {
        this.loadUserData(true);
        notificationHelper.success(i18nHelper.getLocale('messages.success'));
      });
    // this.inputValue = this.userData[0].label;
    // console.log(this.inputValue);
    // this.$emit('change', this.multiple ? this.userData : (this.userData || []).length > 0 ? this.userData[0] : {});
    // this.$emit('user-change', this.multiple ? this.userData : (this.userData || []).length > 0 ? [this.userData[0]] : []);
  }

  private onCloseOrganizationPop(result: boolean, resultList: any, iscancel: boolean) {
    if (iscancel) {
      this.organizationVisible = false;
      return;
    }
    // debugger
    this.selectedOrganizationKeys = resultList.selectedRowKeys;
    // this.modalVisible = result;
    if ((this.selectedOrganizationKeys || []).length === 0) {
      this.$message.error(`${i18nHelper.getLocale('controls.select')}${i18nHelper.getLocale('platform.fields.organization')}`);
      return;
    }
    this.organizationVisible = false;
    const roleOrganizations: RoleOrganizationRelationDto[] = this.selectedOrganizationKeys.map(m => ({
      roleId: this.roleId,
      organizationId: m + ''
    }));
    rightService.addOrganization(roleOrganizations)
      .subscribe(() => {
        this.loadOrganizationData(true);
        notificationHelper.success(i18nHelper.getLocale('messages.success'));
      });
  }

  private tabChange(key: string) {
    switch (key) {
      case 'user':
        this.loadUserData(true);
        break;
      case 'organization':
        this.loadOrganizationData(true);
        break;
    }
  }

  created() {
    this.roleId = this.$route.query['role-id'] as string;
    this.userFieldsSlotMap['operation'] = (text: RoleUserRelationDto[], record: RoleUserRelationDto, index: number) => {
      return (
        <div>
          <span class='mr-1'>
            <a-popconfirm title={this.$t('messages.delete')} on-confirm={() => this.deleteUser(record)}>
              <a-button type='link'>
                {this.$l.getLocale('buttons.delete')}
              </a-button>
            </a-popconfirm>
          </span>
        </div>
      );
    };
    this.organizationFieldsSlotMap['operation'] =
      (text: RoleOrganizationRelationDto[], record: RoleOrganizationRelationDto, index: number) => {
        return (
          <div>
            <span class='mr-1'>
              <a-popconfirm title={this.$t('messages.delete')} on-confirm={() => this.deleteOrganization(record)}>
                <a-button type='link'>
                  {this.$l.getLocale('buttons.delete')}
                </a-button>
              </a-popconfirm>
            </span>
          </div>
        );
      };
    this.loadUserData(true);
  }

  render() {
    return (
      <div>
        <a-tabs default-active-key='user' on-change={(key: string) => this.tabChange(key)}>
          <a-tab-pane key='user' tab={this.$t('platform.fields.user')}>
            <comp-card class={styles.card_top}>
              <comp-table-header class={styles.table_header}
                hideBack={true}
                on-search={() => {
                  this.loadUserData(true);
                }}
                on-reset={() => {
                  this.userReset();
                }}
                on-back={() => {
                  commonService.backParentPage(this.$route, this.$router);
                }}
              >
                <template slot='base'>
                  <a-row>
                    <a-col span='6'>
                      <a-form-item label={this.$t('platform.fields.user')}>
                        <a-input v-model={this.query.userInfo}></a-input>
                      </a-form-item>
                    </a-col>
                    {/* <a-col>
                  <a-button
                    on-click={() => {
                      commonService.backParentPage(this.$route, this.$router);
                    }}
                    class={ styles.common_btn }
                  >
                    {this.$t('buttons.back')}
                  </a-button>
                </a-col> */}
                  </a-row>
                </template>
              </comp-table-header>
            </comp-card>
            <comp-card>
              <a-card class={styles.base_table} bordered={false}>
                <div slot='extra'>
                  <a-button
                    type='primary'
                    on-click={() => {
                      this.userAddVisible = true;
                    }}
                    class={styles.common_btn}
                  >
                    {this.$t('buttons.add')}
                  </a-button>
                </div>

                <a-table
                  size='small'
                  columns={this.userColumns}
                  data-source={this.userDataSource}
                  pagination={this.userPagination}
                  scopedSlots={this.userFieldsSlotMap}
                  on-change={(pagination: any) => {
                    this.userPagination = pagination;
                    this.loadUserData(false);
                  }}
                >
                  <span slot='userLoginId'>{this.$t('platform.fields.code')}</span>
                  <span slot='userName'>{this.$t('platform.fields.name')}</span>
                  <span slot='operation'>{this.$t('platform.fields.operation')}</span>
                </a-table>
              </a-card>
            </comp-card>
            <select-user visible={this.userAddVisible} valueNew={this.userData}
              multiple={true}
              uStatus={1}
              wStatus={1}
              on-closepop=
              {(result: boolean, resultList: any, iscancel: boolean) =>
                this.onCloseUserPop(result, resultList, iscancel)}
            ></select-user>
          </a-tab-pane>
          <a-tab-pane key='organization' tab={this.$t('platform.fields.organization')}>
            <comp-card className={styles.card_top}>
              <comp-table-header className={styles.table_header}
                hideBack={true}
                on-search={() => {
                  this.loadOrganizationData(true);
                }}
                on-reset={() => {
                  this.organizationReset();
                }}
                on-back={() => {
                  commonService.backParentPage(this.$route, this.$router);
                }}
              >
                <template slot='base'>
                  <a-row>
                    <a-col span='6'>
                      <a-form-item label={this.$t('platform.fields.organization')}>
                        <a-input v-model={this.query.organizationInfo}></a-input>
                      </a-form-item>
                    </a-col>
                  </a-row>
                </template>
              </comp-table-header>
            </comp-card>
            <comp-card>
              <a-card className={styles.base_table} bordered={false}>
                <div slot='extra'>
                  <a-button
                    type='primary'
                    on-click={() => {
                      this.organizationVisible = true;
                    }}
                    className={styles.common_btn}
                  >
                    {this.$t('buttons.add')}
                  </a-button>
                </div>

                <a-table
                  size='small'
                  columns={this.organizationColumns}
                  data-source={this.organizationDataSource}
                  pagination={this.organizationPagination}
                  scopedSlots={this.organizationFieldsSlotMap}
                  on-change={(pagination: any) => {
                    this.organizationPagination = pagination;
                    this.loadOrganizationData(false);
                  }}
                >
                  <span slot='organizationCode'>{this.$t('platform.organization.organizationCode')}</span>
                  <span slot='organizationName'>{this.$t('platform.organization.organizationName')}</span>
                  <span slot='organizationFullName'>{this.$t('platform.organization.organizationFullPathText')}</span>
                  <span slot='operation'>{this.$t('platform.fields.operation')}</span>
                </a-table>
              </a-card>
            </comp-card>
            <organization-tree-table
              visible={this.organizationVisible}
              selectedRowKeys={this.selectedOrganizationKeys}
              selectedRows={this.selectedOrganizations}
              on-closepop=
              {(result: boolean, resultList: any, iscancel: boolean) =>
                this.onCloseOrganizationPop(result, resultList, iscancel)}
            ></organization-tree-table>
          </a-tab-pane>
        </a-tabs>
      </div>
    );
  }
}
