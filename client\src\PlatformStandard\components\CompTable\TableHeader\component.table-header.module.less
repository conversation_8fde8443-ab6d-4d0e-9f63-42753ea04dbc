@import '../../../../themes/default/variables.less';
.condition {
  padding: @base-size * 2 @base-size;
  // margin-bottom: @base-size;
}
.card_top2{
  .condition {
    margin-bottom: 0px !important;
  }
}
.float_button {
  float: right !important;
  text-align: right;
}
.searchBtn{
  margin-right: 10px;
  width: 72px;
  height: 32px;
  opacity: 1;
  background: #2165d9;
  text-align: center;
  line-height: 32px;
  border-radius: 5px !important;
}
.commonBtn{
  padding:0;
  margin: 0;
  width: 70px !important;
  height: 30px;
  text-align: center;
  line-height: 32px;
  border-radius: 5px !important;
  border: 1px solid #ddd  !important;
}