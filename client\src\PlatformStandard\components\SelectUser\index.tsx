import { Component, Vue, Prop, Emit, Watch } from 'vue-property-decorator';
import { ValueLabelPair } from '@/PlatformStandard/common/defines';
import styles from './select-user.module.less';
import { departmentService } from './service';
import { OrganizationDto, OrganizationTree } from './types';
// import { organizationService } from '@/PlatformStandard/services/organization';
// import { toJSONString } from 'xe-utils';
import { getComponentFromProp } from '@/PlatformStandard/common/utils';
import debounce from 'lodash/debounce';
import internal from 'stream';

@Component({
  // mixins: [InstanceParamMixin],
})
export class SelectUser extends Vue {
  @Prop() test!: boolean;
  @Prop() title!: string;
  @Prop() visible!: boolean;
  @Prop({ default: true }) multiple!: boolean;
  @Prop() value!: any;
  @Prop() valueNew!: any;
  @Prop() uStatus!: number;
  @Prop() wStatus!: number;
  // private visible = false;
  private keyword = '';
  private treeData = [{}];
  private lstOrgLevel: ValueLabelPair[] = [];
  private selectDepart: OrganizationDto = {};
  private userArry: any = [];
  private searchuserArry: any = [];
  private checkedList: any = [];
  private checkedListChecked: any = [];
  private titleDom: any;
  private columns = [
    {
      dataIndex: 'label',
      width: '60px',
      slots: { title: 'label' },
      scopedSlots: { customRender: 'label' },
    },
    {
      dataIndex: 'account',
      width: '140px',
      slots: { title: 'account' },
    }
  ];
  private fieldsSlotMap: any = {};
  private selectedRowKeys: any[] = [];
  private selectedRows: any[] = [];
  private fetchUser = debounce((value: any) => this.onSearch(value), 500);
  private invalidstate: any[] = [{ lable: '有效', value: 1 }, { lable: '无效', value: 0 }, { lable: '所有', value: 9 }];
  private workingState: any[] = [{ lable: '在职', value: 1 }, { lable: '离职', value: 0 }, { lable: '所有', value: 9 }];

  private iInvalid = this.uStatus ? this.uStatus : this.invalidstate.find(x => x.lable === '所有').value;
  private iworkState = this.wStatus ? this.wStatus : this.workingState.find(x => x.lable === '所有').value;
  // private fetchUser = debounce((value: any) => this.userSearch(value), 500);
  private userFetching = false;
  private getValue = '';

  @Watch('visible')
  @Watch('value')
  @Watch('valueNew')
  visibleChange(newVal: any, oldVal: any) {
    if (newVal) {
      if (this.value && this.value !== null && this.value.length > 0) {
        this.selectedRows = this.value.map((item: any, index: any) => {
          return JSON.parse(JSON.stringify(item).replace('userName', 'label').replace('userId', 'value'));
        });
      } else {
        this.selectedRows = [];
      }
      if (this.valueNew && this.valueNew !== null && this.valueNew.length > 0) {
        this.selectedRows = this.valueNew.map((item: any, index: any) => {
          return JSON.parse(JSON.stringify(item).replace('userName', 'label').replace('userId', 'value'));
        });
      } else {
        this.selectedRows = [];
      }

      // this.getValue = JSON.stringify(this.value);
      // this.getValue = this.getValue.replaceAll('userName', 'label').replaceAll('userId', 'value');

      // this.selectedRows = JSON.parse(this.getValue);
      console.log(this.selectedRows);
      this.keyword = '';
      this.searchuserArry = [];
    }
  }
  @Emit('closepop')

  private closepop(result: boolean, resultList: any, iscancel: boolean) {
    // alert(JSON.stringify(result));
  }
  private onSearch(value: string) {
    // console.log(value);
    if (value) {
      departmentService.getUserList(value, this.iInvalid, this.iworkState).subscribe(data => {
        this.searchuserArry = data;
        // console.log(this.searchuserArry);
        this.userFetching = false;
        // this.$forceUpdate();
      });
    }
  }
  private handleChange(value: any) {
    const that = this;
    console.log((value.label));
    const obj = {
      value: value.key,
      loginId: value.label.split('(')[1].split(')')[0],
      label: value.label.split(':')[0].split('(')[0],
      state: value.label.indexOf('[') !== -1 ? value.label.split('[')[1].split(']')[0] : '', // 无效和离职时，只显示无效；只有离职正常显示；正常没有值
    };
    if (!this.multiple) {
      that.selectedRows = [];
    }
    that.selectedRows.push(obj);
    // console.log(that.selectedRows);
  }
  private onExpand(expandedKeys: string, e: any) {
    const params = { 'parent-id': e.node.dataRef.key };
    if (e.node.dataRef.children) {
      return;
    }
    departmentService.getDepartmentTree(params).subscribe(rs => {
      e.node.dataRef.children = this.transformTreeData(rs);
      if (rs.length === 0) {
        e.node.dataRef.isLeaf = true;
      }
      this.treeData = [...this.treeData];
    });
  }
  private onSelect(selectedKeys: string, e: any) {
    if (selectedKeys && selectedKeys.length > 0) {
      departmentService.getOrganization(selectedKeys[0]).subscribe(rs => {
        this.selectDepart = rs;
        // const orgLevel = this.lstOrgLevel.find(d => d.value === this.selectDepart.tagId);
        // if (orgLevel) {
        //   this.selectDepart.tagName = orgLevel.label;
        // }
      });
      departmentService.getUserListByOrg(selectedKeys[0], this.iInvalid, this.iworkState).subscribe(rs => {
        this.userArry = rs;
        // console.log(this.userArry);
      });
    } else {
      this.selectDepart = {};
    }
  }
  private transformTreeData(data: OrganizationTree[]) {
    return data.map(o => ({
      title: o.label,
      key: o.value,
      datum: o.childCount,
      isLeaf: o.childCount === 0,
      slots: { icon: o.childCount === 0 ? 'file' : 'folder' },
    }));
  }
  private onChange(checkedValues: any) {
    console.log('checked = ', checkedValues);
    this.checkedList = checkedValues;
  }
  private handleClose(e: any) {
    this.checkedList.splice(this.checkedList.findIndex((d: any) => d.value === e.value), 1);
    this.checkedList = this.checkedList;
  }
  private handleCancel() {
    this.checkedList = [];
    this.closepop(false, this.checkedList, true);
  }
  private handleOk() {
    this.closepop(false, this.selectedRows, false);
  }

  // private onSelectChange(selectedRowKeys: any, selectedRows: any) {
  //   this.selectedRowKeys = selectedRowKeys;
  //   this.selectedRows = selectedRows;

  //   // console.log(selectedRowKeys);
  //   // this.$forceUpdate();
  //   // console.log(this.selectedRows);
  // }
  private onTableSelect(record: any, selected: any, selectedRows: any, nativeEvent: any) {
    const rowIndex = this.selectedRows.findIndex((f: any) => f.value === record.value);
    if (rowIndex > -1 && !selected) {
      this.selectedRows.splice(rowIndex, 1);
    } else if (rowIndex === -1 && selected) {
      if (!this.multiple) {
        this.selectedRows = [];
      }
      record.state = (record.status === 0 ? this.$l.getLocale('platform.fields.inValid')
        :
        (record.workingState === 0 ? this.$l.getLocale('platform.user.leave') : '')
      );
      record.loginId = record.number;
      this.selectedRows.push(record);
    }
  }
  private onTableSelectAll(selected: any, selectedRows: any, changeRows: any) {
    changeRows.map((m: any) => {
      this.onTableSelect(m, selected, null, null);
    });
  }
  private closelog(e: any, index: number) {
    this.selectedRows.splice(this.selectedRows.findIndex((item: any) => item.value === e.value), 1);
    this.selectedRowKeys.splice(this.selectedRowKeys.findIndex((item: any) => item === index), 1);
    this.selectedRows = this.selectedRows;
    this.$forceUpdate();
  }
  created() {
    const params = { 'parent-id': '' };
    departmentService.getDepartmentTree(params).subscribe(rs => {
      this.treeData = this.transformTreeData(rs);
    });
    // organizationService.getOrgLevelTags().subscribe(data => {
    //   this.lstOrgLevel = data;
    // });

    const titleDom = getComponentFromProp(this, 'title');
    this.titleDom = titleDom;
    const visible = getComponentFromProp(this, 'visible');
    this.visible = visible;
    // console.log(this.visible);
    // alert(this.visible);
    this.fieldsSlotMap['label'] = (cell: string, row: any) => {
      return (
        row.label +
        (row.status === 0 ? '[' + this.$l.getLocale('platform.fields.inValid') + ']'
          :
          (row.workingState === 0 ? '[' + this.$l.getLocale('platform.user.leave') + ']' : '')
        )
      );
    };
  }
  render() {
    return (
      <div>
        <a-modal
          width='700px'
          title={this.$t('bpm.form.user')}
          visible={this.visible}
          on-cancel={this.handleCancel}
          on-ok={this.handleOk}>
          <div class={styles.searchBox}>
            <div>
              <div>
                {
                  this.selectedRows && this.selectedRows.length > 0 && this.selectedRows[0].label !== ''
                   ? this.selectedRows.map((item: any, index) => (
                    <a-tag closable key={item.value} on-close={() => this.closelog(item, index)}>
                      {item.label}
                    </a-tag>
                  )) : ''
                }
                <a-select
                  show-search
                  label-in-value={true}
                  v-model={this.keyword}
                  filter-option={false}
                  on-search={this.fetchUser}
                  on-change={(value: any) => this.handleChange(value)}
                  placeholder={this.$l.getLocale('controls.input')}
                  style='width:300px'
                >
                  {
                    this.searchuserArry.map((item: any) => (
                      <a-select-option value={item.value}>{item.label}({item.account})
                        {
                          item.status === 0 ? '[' + this.$l.getLocale('platform.fields.inValid') + ']'
                            :
                            (item.workingState === 0 ? '[' + this.$l.getLocale('platform.user.leave') + ']' : '')
                        }
                        :{item.fullDivision}
                      </a-select-option>
                    ))
                  }
                </a-select>
                {/* <a-input style='width:50px' placeholder={this.$l.getLocale('controls.input')} v-model={this.keyword}  />
                   on-click={this.onSearch}*/}
              </div>
            </div>
            <a-button class={styles.searchBtn} type='primary' icon='search' html-type='submit' >
              {/* {this.$l.getLocale('buttons.search')} */}
            </a-button>
          </div>
          <div class={styles.main}>
            <div class={styles.left}>
              <a-tree on-expand={this.onExpand} on-select={this.onSelect} tree-data={this.treeData} show-icon>
                <a-icon slot='switcherIcon' type='down' />
                <a-icon slot='company' type='contacts' />
                <a-icon slot='department' type='team' />
              </a-tree>
            </div>
            <div class={styles.right}>
              <div class={styles.rightTop}>
                <a-table
                  rowKey={(userArry: any) => userArry.value}
                  columns={this.columns}
                  data-source={this.userArry}
                  bordered
                  size='middle'
                  // pagination={true}
                  pagination={{ pageSize: 7 }}
                  type='checkbox'
                  scopedSlots={this.fieldsSlotMap}
                  row-selection={{
                    type: this.multiple ? 'checkbox' : 'radio',
                    onSelect: this.onTableSelect,
                    onSelectAll: this.onTableSelectAll
                  }}
                >
                  <span slot='label'>姓名</span>
                  <span slot='account'>用户编码</span>
                </a-table>
              </div>
              <div class={styles.rightBottom} style='display:none'>
                {
                  this.checkedList.map((item: { label: any; value: any }) => (
                    [console.log(item),
                    <a-tag closable
                      key={item.value}
                      on-close={() => this.handleClose(item)}
                    >{item.label}</a-tag>]
                  ))
                }
              </div>
            </div>
          </div>
        </a-modal>
      </div>
    );
  }
}
