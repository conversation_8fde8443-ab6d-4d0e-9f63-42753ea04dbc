import { Observable, of } from 'rxjs';
import { httpHelper } from '@/PlatformStandard/common/utils';
import { map } from 'rxjs/operators';
import {RoleDto, RoleOrganizationRelationDto, RoleUserRelationDto} from './types';

class RightService {
  /**
   * 获取角色列表
   * @param params 查询参数
   */
  getRoles(params: any): Observable<any> {
    const _url = `/api/platform/v1/manage/roles`;
    return httpHelper.get(_url, { params: params }).pipe(
      map(data => ({
        total: data.total,
        items: data.items.map((m: any) => ({
          ...m,
          key: m.roleId
        })),
      }))
    );
  }

  /**
   * 保存角色
   * @param roleDto 角色实体
   */
  saveRole(roleDto: RoleDto): Observable<void> {
    const _url = `/api/platform/v1/manage/role`;
    return httpHelper.post(_url, roleDto);
  }

  /**
   * 删除角色
   * @param roleId 角色Id
   */
  deleteRole(roleId: string): Observable<void> {
    const _url = `/api/platform/v1/manage/role/${roleId}`;
    return httpHelper.delete(_url);
  }

  /**
   * 获取角色下的人员
   * @param params 人员查询参数
   */
  getUsers(params: any): Observable<any> {
    const _url = `/api/platform/v1/manage/role-members`;
    return httpHelper.get(_url, { params: params }).pipe(
      map(data => ({
        total: data.total,
        items: data.items.map((m: any) => ({
          ...m,
          key: m.roleUserRelationId
        })),
      }))
    );
  }

  /**
   * 获取角色下的组织
   * @param 组织查询参数
   */
  getOrganizations(params: any): Observable<any> {
    const _url = `/api/platform/v1/manage/role-organizations`;
    return httpHelper.get(_url, { params: params }).pipe(
      map(data => ({
        total: data.total,
        items: data.items.map((m: any) => ({
          ...m,
          key: m.roleOrganizationRelationId
        })),
      }))
    );
  }

  /**
   * 添加角色人员关系
   * @param member 角色人员关系对象
   */
  addUser(users: RoleUserRelationDto[]): Observable<void> {
    const path = `/api/platform/v1/manage/role-members`;
    return httpHelper.post(path, users);
  }

  /**
   * 添加角色组织关系
   * @param member 角色组织关系对象
   */
  addOrganization(organizations: RoleOrganizationRelationDto[]): Observable<void> {
    const path = `/api/platform/v1/manage/role-organizations`;
    return httpHelper.post(path, organizations);
  }

  /**
   * 删除角色人员关系
   * @param type 角色人员关系Id
   */
  deleteUser(relationId: string): Observable<void> {
    const path = `/api/platform/v1/manage/role-members/${relationId}`;
    return httpHelper.delete(path);
  }

  /**
   * 删除角色组织关系
   * @param type 角色组织关系Id
   */
  deleteOrganization(relationId: string): Observable<void> {
    const path = `/api/platform/v1/manage/role-organizations/${relationId}`;
    return httpHelper.delete(path);
  }

  /**
   * 获取角色所属的应用集合
   * @param roleId 角色Id
   * @returns 应用集合
   */
  getRoleApplications(roleId: string): Observable<any> {
    const path = `/api/platform/v1/manage/role-applications/${roleId}`;
    return httpHelper.get(path);
  }

}

export const rightService = new RightService();
