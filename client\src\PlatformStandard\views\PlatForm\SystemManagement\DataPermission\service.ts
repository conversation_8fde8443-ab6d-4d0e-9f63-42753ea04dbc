import { httpHelper, i18nHelper } from '@/PlatformStandard/common/utils';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { DataPermissionDto, PermissionTable } from './types';

class DataPermissionService {
  tables: PermissionTable[] = [];

  getDataPermissions(params: any): Observable<any> {
    const url = '/api/platform/v1/manage/data-permissions';
    return httpHelper.get(url, { params: params }).pipe(
      map(data => ({
        total: data.total,
        items: data.items.map((m: any) => ({
          ...m,
          key: m.permissionId
        })),
      }))
    );
  }

  getAllDataPermissions(): Observable<any> {
    const params = {isAll: 'true'};
    const url = '/api/platform/v1/manage/data-permissions';
    return httpHelper.get(url, { params: params });
  }

  getDataPermission(permissionId: string): Observable<any> {
    const url = '/api/platform/v1/manage/data-permission';
    return httpHelper.get(url, { params: { permissionId: permissionId } });
  }

  getTables() {
    const url = '/api/platform/v1/manage/permission-tables';
    httpHelper.get(url)
      .pipe(map((m: any[]) => m.map(v => ({ ...v, locales: { ['zh']: v.descCn, ['en']: v.descEn } }))))
      .subscribe(data => {
        this.tables = data;
      });
  }

  getColumns(tableName: string): Observable<any> {
    const url = '/api/platform/v1/manage/permission-columns';
    return httpHelper.get(url, { params: { tableName: tableName } })
      .pipe(map((m: any[]) => m.map(v => ({ ...v, locales: { ['zh']: v.descCn, ['en']: v.descEn } }))));
  }

  saveDataPermission(dataPermission: DataPermissionDto) {
    const url = '/api/platform/v1/manage/data-permission';
    return httpHelper.post(url, dataPermission);
  }

  deleteDataPermission(permissionId: string) {
    const url = '/api/platform/v1/manage/data-permission';
    return httpHelper.delete(url, { params: { permissionId: permissionId } });
  }
}
export const dataPermissionService = new DataPermissionService();
