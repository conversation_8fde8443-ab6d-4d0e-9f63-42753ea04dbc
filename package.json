{"name": "mt-enterprise-web", "version": "0.0.1", "description": "mt-enterprise-web server", "main": "index.js", "scripts": {"lint": "tslint -c ./tslint.json ./src/**/*.ts", "tsc": "tsc", "rimraf": "<PERSON><PERSON><PERSON>", "npm-run-all": "npm-run-all", "concurrently": "concurrently", "clean:koa": "rimraf node_modules dist package-lock.json yarn.lock", "clean:vue": "rimraf client/node_modules public client/package-lock.json client/yarn.lock", "start:dev:koa": "nodemon", "start:dev:koa:debug": "nodemon --inspect=0.0.0.0:9229", "start:dev:vue": "cd client && set NODE_OPTIONS=--openssl-legacy-provider && npm-run-all serve", "start:dev": "concurrently \"npm-run-all start:dev:koa\" \"npm-run-all start:dev:vue\"", "start:dev:debug": "concurrently \"npm-run-all start:dev:koa:debug\" \"npm-run-all start:dev:vue\"", "build": "rimraf dist && tsc", "build:ncc": "rimraf dist && ncc build src/app.ts"}, "dependencies": {"@antv/layout": "^0.2.5", "amqplib": "~0.5.3", "clipboard-copy": "^4.0.1", "crypto-js": "~3.1.9-1", "koa": "~2.5.2", "koa-better-http-proxy": "~0.2.4", "koa-body": "~4.0.4", "koa-generic-session": "~2.0.1", "koa-logger": "~3.2.0", "koa-redis": "~3.1.3", "koa-router": "~7.4.0", "koa-static": "~5.0.0", "lodash": "~4.17.11", "nacos": "^1.1.0", "nacos-naming": "^1.1.0", "socket.io": "~2.1.1", "statuses": "~1.5.0", "superagent": "~3.8.3"}, "devDependencies": {"@types/amqplib": "~0.5.9", "@types/koa": "~2.0.47", "@types/koa-generic-session": "~1.0.2", "@types/koa-logger": "~3.1.1", "@types/koa-redis": "~3.0.2", "@types/koa-router": "~7.0.35", "@types/koa-static": "~4.0.0", "@types/lodash": "~4.14.119", "@types/node": "~11.11.3", "@types/socket.io": "~1.4.39", "@types/statuses": "~1.5.0", "@types/superagent": "~3.8.4", "@vercel/ncc": "^0.24.1", "bpmn-js": "^8.2.1", "concurrently": "~3.6.1", "nodemon": "~1.18.7", "npm-run-all": "~4.1.5", "rimraf": "~2.6.3", "ts-node": "~7.0.1", "tslint": "~5.12.1", "typescript": "~3.3.3"}, "license": "MIT"}