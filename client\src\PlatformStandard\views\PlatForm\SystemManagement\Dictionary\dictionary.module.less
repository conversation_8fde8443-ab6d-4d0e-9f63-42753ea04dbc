@import '../../../../../themes/default/variables.less';

.box {
  display: flex;
  .box_left {
    width: 330px;
    height: 500px;
    margin-right: @base-size * 2;
  }

  .box_right {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;

    .dictionary_table {
      margin-top: @base-size * 2;
    }

    .dictionary_table button {
      margin-bottom: @base-size;
    }
    .common_btn {
      padding:0;
      margin: 0  0 0 10px;
      width: 70px !important;
      height: 30px !important;
      text-align: center;
      line-height: 25px;
      border-radius: 5px !important;
      border: 1px solid #ddd !important;
      background-color: #fff !important;
      color: #333 !important;
      opacity: 1;
      text-shadow: none !important;
      box-shadow: none !important;
    }
  }
}
