import Vue from 'vue';
import router from './router';
import App from './App';
import i18n from './i18n';
import '@babel/polyfill';
import '@/plugins/ant-design-vue';
import './styles.less';
import { i18nService } from './PlatformStandard/services/i18n';
import { authActionDirective } from './PlatformStandard/services/auth/auth-action-directive';
import { httpHelper } from './PlatformStandard/common/utils';
import { Settings } from './PlatformStandard/common/defines';
import { preventReClickDirective } from './PlatformStandard/common/utils/prevent-reclick-directive';

refreshLanguage();
Vue.config.productionTip = false;
i18nService.init(i18n);
authActionDirective.register();
preventReClickDirective.register();

new Vue({
  router,
  i18n,
  render: h => h(App),
}).$mount('#app').$loadLanguageAsync();

// 刷新多语言缓存
function refreshLanguage() {
  httpHelper.get('/auth/i18Languages', undefined, { loading: false }).subscribe(msgs => {
    localStorage.setItem(Settings.I18nLanguageCacheKey, JSON.stringify(msgs));
    Object.keys(msgs).forEach((key: string) => {
      i18n.setLocaleMessage(key, msgs[key]);
    });
  });
}
