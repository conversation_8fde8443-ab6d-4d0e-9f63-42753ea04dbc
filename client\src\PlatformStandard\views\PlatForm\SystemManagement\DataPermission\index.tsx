import { Component, Vue } from 'vue-property-decorator';
import { CompCard, CompTableHeader } from '@/PlatformStandard/components';
import { DataPermissionDto, PermissionQueryDto } from './types';
import { i18nHelper, notificationHelper, toCasedStyleObject } from '@/PlatformStandard/common/utils';
import { dataPermissionService } from './service';
import styles from './DataPermission.module.less';
import { DataPermissionCondition } from './Conditions';
import { ActionEnum } from '@/PlatformStandard/services/menu';
import { languageService } from '@/PlatformStandard/services/language';

@Component({ components: { CompCard, CompTableHeader, DataPermissionCondition } })
export class DataPermission extends Vue {
  private pagination = {
    total: 0,
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    size: 'default',
    showTotal: (total: string) =>
      this.$l
        .getLocale('paginations.total')
        .toString()
        .replace('{{value}}', total),
  };
  private query: PermissionQueryDto = {};
  private dataSource: any;
  private fieldsSlotMap: any = {};
  private columns = [
    {
      dataIndex: 'code',
      key: 'code',
      slots: { title: 'code' }
    },
    {
      dataIndex: 'description',
      key: 'description',
      slots: { title: 'description' }
    },
    {
      dataIndex: 'status',
      key: 'status',
      slots: { title: 'status' },
      scopedSlots: { customRender: 'status' },
      width: '200px'
    },
    {
      dataIndex: 'action',
      slots: { title: 'operation' },
      scopedSlots: { customRender: 'action' },
      width: '200px'
    },
  ];
  private opration!: string;
  private selectPermissionId!: string;
  private editShow = false;
  private actions = ActionEnum;
  private statusDataset = i18nHelper.getLocaleObject('platform.dataPermission.statusDataset');

  private loadData(reset: boolean = false) {
    if (reset) {
      this.pagination.current = 1;
    }
    const params = { 'page-index': this.pagination.current, 'page-size': this.pagination.pageSize };
    dataPermissionService.getDataPermissions({ ...toCasedStyleObject(this.query), ...params }).subscribe(data => {
      this.dataSource = data.items;
      this.pagination.total = data.total;
      this.$forceUpdate();
    });
  }

  private reset() {
    this.query = {};
  }

  private editPermission(permissionId?: string) {
    if (permissionId) {
      this.selectPermissionId = permissionId;
      this.opration = 'edit';
    } else {
      this.selectPermissionId = '';
      this.opration = 'add';
    }
    this.editShow = true;
  }

  private deletePermission(permissionId?: string) {
    dataPermissionService.deleteDataPermission(permissionId as string).subscribe(rs => {
      notificationHelper.success(i18nHelper.getLocale('messages.success'));
      this.loadData(true);
    });
  }

  private save() {
    const errorMsgs = (this.$refs.editDataPermision as DataPermissionCondition).validateForm();
    if (errorMsgs && errorMsgs.length > 0) {
      notificationHelper.error(i18nHelper.getLocale('platform.dataPermission.dataRequied'));
      return false;
    }
    const dataPermission = (this.$refs.editDataPermision as DataPermissionCondition).save();
    if (!dataPermission.expression || dataPermission.expression === '') {
      notificationHelper.error(i18nHelper.getLocale('platform.dataPermission.dataRequied'));
      return false;
    }

    dataPermissionService.saveDataPermission(dataPermission).subscribe(rs => {
      this.editShow = false;
      notificationHelper.success(i18nHelper.getLocale('messages.success'));
      this.loadData(true);
    });
  }

  private cancel() {
    this.editShow = false;
  }

  created() {
    dataPermissionService.getTables();
    this.fieldsSlotMap['action'] = (text: DataPermissionDto[], record: DataPermissionDto, index: number) => {
      return (
        <div>
          <span v-permission={this.actions.edit} class='mr-1'>
            <a-tooltip>
              <template slot='title'>
                {this.$l.getLocale('buttons.edit')}
              </template>
              {/* <a-icon
                type='edit'
                class='text-primary'
                on-click={() => this.editPermission(record.permissionId)} /> */}
              <span v-permission={this.actions.edit}>
                <a-button type='link' on-click={() => this.editPermission(record.permissionId)} size='small' class={styles.list_button}>
                  {this.$l.getLocale('buttons.edit')}
                </a-button>
              </span>
            </a-tooltip>
          </span>
          <span v-permission={this.actions.delete} class='mr-1'>
            <a-tooltip>
              <template slot='title'>
                {this.$l.getLocale('buttons.delete')}
              </template>
              <a-popconfirm title={this.$t('messages.delete')} on-confirm={() => this.deletePermission(record.permissionId)}>
                {/* <a-icon
                  type='delete'
                  class='text-primary'
                  style='color: red; cursor: pointer;'
                /> */}
                <span v-permission={this.actions.delete}>
                  <a-button type='link' on-click={() => this.editPermission(record.permissionId)} size='small' class={styles.list_button}>
                    {this.$l.getLocale('buttons.delete')}
                  </a-button>
                </span>
              </a-popconfirm>
            </a-tooltip>
          </span>
        </div>
      );
    };
    this.fieldsSlotMap['status'] = (text: DataPermissionDto[], record: DataPermissionDto, index: number) => {
      // return record.status === 1 ? this.$l.getLocale('platform.commonRole.valid') : this.$l.getLocale('platform.commonRole.invalid');
      return (
        <div>
          {record.status === 1 ? <a-tag color='blue'>
            {this.$l.getLocale('platform.commonRole.valid')} </a-tag>
            :
            <a-tag color='red'> {this.$l.getLocale('platform.commonRole.invalid')} </a-tag>}
        </div>
      );
    };
    languageService.language$.subscribe(() => {
      this.statusDataset = i18nHelper.getLocaleObject('platform.dataPermission.statusDataset');
    });
    this.loadData(true);
  }

  render() {
    return (
      <div>
        <comp-card  class={styles.card_top}>
          <comp-table-header
            on-search={() => {
              this.loadData(true);
            }}
            on-reset={() => {
              this.reset();
            }}
          >
            <template slot='base'>
              <a-row>
                <a-col span='8' class='mr-1'>
                  <a-form-item label={this.$t('platform.fields.description')}>
                    <a-input v-model={this.query.description}></a-input>
                  </a-form-item>
                </a-col>
                <a-col span='8' class='mr-1'>
                  <a-form-item label={this.$t('platform.fields.status')}>
                    <a-select v-model={this.query.status}>
                      {this.statusDataset.map((item: any) => (
                        <a-select-option value={item.value}>{item.label}</a-select-option>
                      ))}
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
            </template>
          </comp-table-header>
        </comp-card>
        <comp-card>
          <a-card class={styles.base_table} bordered={false}>
            <div slot='extra' style='line-height: 0'>
              <a-button type='primary' class={styles.common_btn}
                on-click={() => this.editPermission('')}>{this.$t('buttons.add')}</a-button>
            </div>
            <a-table
              size='small'
              columns={this.columns}
              data-source={this.dataSource}
              pagination={this.pagination}
              scopedSlots={this.fieldsSlotMap}
              on-change={(pagination: any) => {
                this.pagination = pagination;
                this.loadData(false);
              }}
            >
              <span slot='code'>{this.$t('platform.fields.code')}</span>
              <span slot='description'>{this.$t('platform.fields.description')}</span>
              <span slot='status'>{this.$t('platform.fields.status')}</span>
              <span slot='operation'>{this.$t('platform.fields.operation')}</span>
            </a-table>
          </a-card>
        </comp-card>
        <a-modal
          width='800px'
          title={this.opration === 'add' ?
            this.$l.getLocale(['buttons.add', 'platform.dataPermission.permission'])
            :
            this.$l.getLocale(['buttons.edit', 'platform.dataPermission.permission'])}
          visible={this.editShow}
          destroyOnClose={true}
          maskClosable={false}
          on-cancel={this.cancel}
          on-ok={this.save}
        >
          <data-permission-condition ref='editDataPermision' selectPermissionId={this.selectPermissionId} />
        </a-modal>
      </div>
    );
  }
}
