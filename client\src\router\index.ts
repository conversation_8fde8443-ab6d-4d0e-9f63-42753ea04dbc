import Vue from 'vue';
import Router from 'vue-router';
import { platformRoutes } from '@/PlatformStandard/router/platform.routes';
import { bpmRoutes } from '@/Bpm/router/bpm.routes';

Vue.use(Router);

// 获取原型对象上的push函数
const originalPush = Router.prototype.push;
// 修改原型对象中的push方法
Router.prototype.push = function push(location: any) {
  return (originalPush.call(this, location) as any).catch((err: any) => err);
};

export default new Router({
  mode: 'history',
  base: process.env.BASE_URL,
  routes: [...bpmRoutes.concat(platformRoutes)],
});
