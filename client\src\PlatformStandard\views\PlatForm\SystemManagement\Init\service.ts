import { Apis } from '@/PlatformStandard/views/PlatForm/SystemManagement/Apis';
import { Observable, of } from 'rxjs';
import { httpHelper, downloadHelper } from '@/PlatformStandard/common/utils';
import { map } from 'rxjs/operators';

class InitService {
  /**
   * 上传图片
   * @param data 图片文件
   */
  uploadPicture(data: FormData): Observable<any> {
    const url = `/api/file-centre/v1/document-services/upload/local`;
    return httpHelper.post(url, data);
  }

  /**
   * 下载图片
   * @param filePath 图片路径
   */
  downloadPicture(filePath: string): Observable<any> {
    const url = `/api/file-centre/v1/document-services/download/local`;
    return httpHelper.get(url, { params: { FilePath: filePath }, responseType: 'blob' as any });
  }

  /**
   * 获取设定
   * @param group 设定分组
   */
  getSettings(group: string): Observable<any> {
    const url = `/api/platform/v1/manage/init-settings/${group}`;
    return httpHelper.get(url);
  }

  /**
   * 提交设定
   * @param group 设定分组
   * @param data 设定值
   */
  submitSettings(group: string, data: any): Observable<any> {
    const url = `/api/platform/v1/manage/init-settings/${group}`;
    return httpHelper.put(url, data);
  }
}
export const initService = new InitService();
