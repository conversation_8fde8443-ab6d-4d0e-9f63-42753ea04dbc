import { Component, Vue, Prop, Emit, Watch } from 'vue-property-decorator';
import styles from './select-user.module.less';
import { departmentService } from './service';
import { OrganizationTree } from './types';

@Component({
})
export class SelectDepartment extends Vue {
  @Prop() test!: boolean;
  @Prop() title!: string;
  @Prop() visible!: boolean;
  @Prop({ default: true }) multiple!: boolean;
  @Prop() value!: any;
  private treeData = [{}];
  private selectDepart: any = [];
  private copyTreeData = [];

  @Watch('visible')
  visibleChange(newVal: any, oldVal: any) {
    if (newVal) {
      this.selectDepart = JSON.parse(JSON.stringify(this.value));
    }
  }
  @Emit('closepop')
  private closepop(result: boolean, resultList: any) {
  }
  private onExpand(expandedKeys: string, e: any) {
    const params = { 'parent-id': e.node.dataRef.key + '', 'parent-type': e.node.dataRef.datum };
    if (e.node.dataRef.children) {
      return;
    }
    departmentService.getDepartmentList(e.node.dataRef.key).subscribe(rs => {
      e.node.dataRef.children = this.transformTreeData(rs);
      if (rs.length === 0) {
        e.node.dataRef.isLeaf = true;
      }
      this.treeData = [...this.treeData];
    });
  }
  private onSelect(selectedKeys: string, e: any) {
    const orgIndex = this.selectDepart.findIndex((f: any) => f.key === e.node.dataRef.key);
    if (orgIndex > -1 && !e.selected) {
      this.selectDepart.splice(orgIndex, 1);
    } else if (orgIndex === -1 && e.selected) {
      if (!this.multiple) {
        this.selectDepart = [];
      }
      this.selectDepart.push(e.node.dataRef);
    }
  }
  private transformTreeData(data: OrganizationTree[]) {
    return data.map(o => ({
      title: o.label,
      key: o.value,
      code: o.code,
      path: o.fullPathCode,
      datum: o.childCount,
      isLeaf: o.childCount === 0,
      slots: { icon: o.childCount === 0 ? 'file' : 'folder' }
    }));
  }
  private handleCancel() {
    this.closepop(false, this.selectDepart);
  }
  private handleOk() {
    this.closepop(true, this.selectDepart);
  }
  private onSearch(e: any) {
    if (!e) {
      this.treeData = this.copyTreeData.length > 0 ?
        JSON.parse(JSON.stringify(this.copyTreeData)) : this.treeData;
    } else {
      this.copyTreeData = JSON.parse(JSON.stringify(this.treeData));
      departmentService.searchOrganizations({ 'key-word': e }).subscribe(data => {
        this.treeData = data.map((o: any) => ({
          title: o.label,
          key: o.value,
          code: o.code,
          path: o.fullPathCode,
          isLeaf: true
        }));
      });
    }
  }
  private onClose(i: number) {
    this.selectDepart.splice(i, 1);
  }
  created() {
    const params = { 'parent-id': '', 'parent-type': 'company' };
    departmentService.getDepartmentTree(params).subscribe(rs => {
      this.treeData = this.transformTreeData(rs);
    });
  }
  render() {
    return (
      <div>
        <a-modal
          width='680px'
          title={this.$t('platform.columns.organization')}
          visible={this.visible}
          on-cancel={this.handleCancel}
          on-ok={this.handleOk}>
          <div class={styles.main}>
            <div class={styles.left}>
              <a-input-search placeholder={this.$t('platform.organization.addCheck')} allowClear on-search={this.onSearch} />
              <a-tree on-expand={this.onExpand} on-select={this.onSelect} tree-data={this.treeData} show-icon>
                <a-icon slot='switcherIcon' type='down' />
                <a-icon slot='company' type='contacts' />
                <a-icon slot='department' type='team' />
              </a-tree>
            </div>
            <div class={styles.right}>
              <div>{this.$t('platform.organization.selectedOrganization')}</div>
              {
                this.selectDepart.map((m: any, i: number) => {
                  return <div class={styles.checked}>
                    <span>{m.title}</span>
                    <a-icon type='close' on-click={() => this.onClose(i)}></a-icon>
                  </div>;
                })
              }
            </div>
          </div>
        </a-modal>
      </div>
    );
  }
}
