import { Component, Prop, Vue } from 'vue-property-decorator';
import { BaseControl } from '../base-control';

@Component({
})
export class MmtCheckBox extends BaseControl {
    get defaultLable() {
        if (this.value && this.value.length > 0) {
            const arrs: any = [];
            this.value.map((m: any) => {
                const item = this.controlConfig.config.options.find((f: any) => f.value === m);
                if (item) {
                    arrs.push(item.label);
                }
            });
            return arrs.join(';');
        }
        return '';
    }
    created(): void {
    }
    render() {
        return (
            this.pageModel === 'view' ? (
                <div>{this.defaultLable}</div>
            ) : (
                <a-checkbox-group
                    value={this.value}
                    disabled={!this.controlConfig.config.disabled}
                    options={this.controlConfig.config.options}
                    on-change={(e: any) => this.onChange(e)}
                >
                </a-checkbox-group>
            ));
    }
}
