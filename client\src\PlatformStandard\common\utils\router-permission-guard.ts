import { Route } from 'vue-router';

import { authService } from '@/PlatformStandard/services/auth';
import { notificationHelper } from './notification-helper';
import { i18nHelper } from './i18n-helper';
import { menuService } from '@/PlatformStandard/services/menu';

export function routerPermissionGuard(to: Route, from: Route, next: (option?: any) => void) {
  // 全局接收app-code参数并记录sessionStorage
  if (to.query['app-code'] && to.query['app-code'] !== '') {
    sessionStorage.setItem('appCode', to.query['app-code'] as string);
  }
  authService.getAuthState().subscribe(
    () => {
      /* console.log('menuService.authorizedPages', menuService.authorizedPages);
      console.log('to', to); */
      if (menuService.authorizedPages.findIndex(page => ('/' + page.route).match(to.path) !== null) >= 0) {
        next();
      } else {
        // notificationHelper.error(i18nHelper.getLocale('framework.nonpermission'), '401');
        next({ path: menuService.getDefaultPage(), replace: true });
      }
    },
    error => {
      if (error && error.response) {
        if (error.response.status === 401) {
          next(error.response.data);
        } else if (error.response.status === 308) {
          window.location.href = error.response.data;
        }
      }
    }
  );
}
