/*
变量参考 https://github.com/vueComponent/ant-design-vue/blob/master/components/style/themes/default.less
或 https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/components/style/themes/default.less
*/
/* 重写 AntDesign 的主题样式 */
/* 重写 AntDesign 结束 */
/* 自定义相关 */
/* 基础边距 */
.page {
  display: flex;
}
.page .tree_list {
  width: 330px;
  height: 510px;
}
.page .tree_list .tree_card {
  width: 100%;
  display: flex !important;
  flex-direction: column;
  height: 100%;
}
.page .content_info {
  width: 100%;
}
.page .content_info .edit_info {
  width: 100%;
  margin-left: 12px;
}
.page .content_info .edit_info .info_card {
  display: flex !important;
  width: 100%;
  flex-direction: column;
  height: auto;
}
.page .content_info .edit_info .info_card .buttons {
  height: 22px;
}
.page .content_info .edit_info2 {
  width: 100%;
  margin-top: 8px;
  margin-left: 12px;
}
.page .content_info .edit_info2 .info_card {
  display: flex !important;
  width: 100%;
  flex-direction: column;
  height: auto;
}
.page .content_info .edit_info2 .info_card .buttons {
  height: 22px;
}
.page .content_info .edit_info2 .info_card .row_outer {
  background-color: #f8f8f8;
  margin: 8px 0px !important;
}
.page .content_info .edit_info2 .info_card .row {
  padding: 16px;
}
.page .content_info .edit_info2 .info_card .operation {
  text-align: right;
  cursor: pointer;
}
.page .content_info .list_button {
  padding: 0 3px !important;
}
:global(.ant-tree li .ant-tree-node-content-wrapper) {
  margin-left: -10px !important;
}
