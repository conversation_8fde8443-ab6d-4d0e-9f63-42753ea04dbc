/* 提供的边距类 */
.margin-loop(@n, @i: 0) when(@i <=@n) {
  .mt-@{i} {
    margin-top: @i* @base-size !important;
  }

  .mb-@{i} {
    margin-bottom: @i* @base-size !important;
  }

  .ml-@{i} {
    margin-left: @i* @base-size !important;
  }

  .mr-@{i} {
    margin-right: @i* @base-size !important;
  }

  .mx-@{i} {
    margin-left: @i* @base-size !important;
    margin-right: @i* @base-size !important;
  }

  .my-@{i} {
    margin-top: @i* @base-size !important;
    margin-bottom: @i* @base-size !important;
  }

  .m-@{i} {
    margin: @i* @base-size !important;
  }

  .margin-loop(@n, (@i + 1));
}

.margin-loop(3);

.mt-auto {
  margin-top: auto !important;
}

.mb-auto {
  margin-bottom: auto !important;
}

.ml-auto {
  margin-left: auto !important;
}

.mr-auto {
  margin-right: auto !important;
}

.mx-auto {
  margin-left: auto !important;
  margin-right: auto !important;
}

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}

.padding-loop(@n, @i: 0) when(@i <=@n) {
  .pt-@{i} {
    padding-top: @i* @base-size !important;
  }

  .pb-@{i} {
    padding-bottom: @i* @base-size !important;
  }

  .pl-@{i} {
    padding-left: @i* @base-size !important;
  }

  .pr-@{i} {
    padding-right: @i* @base-size !important;
  }

  .px-@{i} {
    padding-left: @i* @base-size !important;
    padding-right: @i* @base-size !important;
  }

  .py-@{i} {
    padding-top: @i* @base-size !important;
    padding-bottom: @i* @base-size !important;
  }

  .p-@{i} {
    padding: @i* @base-size !important;
  }

  .padding-loop(@n, (@i + 1));
}

.padding-loop(3);

.pt-auto {
  padding-top: auto !important;
}

.pb-auto {
  padding-bottom: auto !important;
}

.pl-auto {
  padding-left: auto !important;
}

.pr-auto {
  padding-right: auto !important;
}

.px-auto {
  padding-left: auto !important;
  padding-right: auto !important;
}

.py-auto {
  padding-top: auto !important;
  padding-bottom: auto !important;
}
