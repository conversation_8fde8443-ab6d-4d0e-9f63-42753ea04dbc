import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { httpHelper } from '../../common/utils';
import { PageModelingDto } from './types';

class ModelingViewService {
    uploadFilesUrl = '/api/modeling/v1/page-modelings/upload-files';
    getDynamicData(params: any, databaseId: string): Observable<any> {
        const _url = `/api/dynamic/get/${databaseId}`;
        return httpHelper.post(_url, params);
    }
    addDynamicData(params: any, databaseId: string): Observable<any> {
        const _url = `/api/dynamic/add/${databaseId}`;
        return httpHelper.post(_url, params);
    }
    editDynamicData(params: any, databaseId: string): Observable<any> {
        const _url = `/api/dynamic/edit/${databaseId}`;
        return httpHelper.post(_url, params);
    }
    getPageModeling(id: string): Observable<PageModelingDto> {
        const _url = `/api/modeling/v1/page-modelings/${id}`;
        return httpHelper.get(_url);
    }
}

export const modelingViewService = new ModelingViewService();
