import { Observable, of } from 'rxjs';
import { httpHelper } from '@/PlatformStandard/common/utils';
import { map } from 'rxjs/operators';

class JobsService {
  /**
   * 获取Job分页列表
   * @param params 查询条件
   * @returns Job集合
   */
  getJobs(params: any): Observable<any> {
    const _url = `/api/platform/v1/manage/jobs`;
    return httpHelper.get(_url, { params: params }).pipe(
      map(data => ({
        total: data.total,
        items: data.items.map((m: any) => ({
          ...m,
          key: m.jobId
        })),
      }))
    );
  }

  /**
   * 获取Job明细
   * @param jobId Job主键Id
   * @returns Job对象
   */
  getJob(jobId: number): Observable<any> {
    const _url = `/api/platform/v1/manage/job/${jobId}`;
    return httpHelper.get(_url);
  }

  /**
   * Job 重试
   * @param queueName 队列名称
   * @param shortKeys Job 短Key
   */
  retryJobs(jobIds: string[]): Observable<any> {
    const url = `/api/platform/v1/manage/jobs-retry`;
    return httpHelper.post(url, jobIds);
  }
}
export const jobsService = new JobsService();
