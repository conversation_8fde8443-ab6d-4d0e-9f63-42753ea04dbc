import { Component, Vue, Prop, Emit } from 'vue-property-decorator';
import styles from './form-list.module.less';
import { guid<PERSON><PERSON>per, i18n<PERSON>elper, notification<PERSON>elper, removeNullValueProperty } from '../common/utils';
import { WrappedFormUtils } from 'ant-design-vue/types/form/form';
import dynamicApi from '../dynamic-api-json';
import * as com from '../component/Controls';
import { modelingViewService } from '../service';

@Component({
    components: {
        ...com
    }
})
export class FormList extends Vue {
    @Prop() pageDesginJson!: string;
    @Prop({ default: 'add' }) pageModel!: string;
    @Prop({ default: 'newTab' }) openModel!: string;
    @Prop() parameter!: any;
    private form!: WrappedFormUtils;
    private currentPageDsegin: any = {};
    private currentChangePageData: any = {}; // 最终保存数据
    private currentDefaultPageData: any = []; // 默认显示数据/修改后数据
    private tableColumns: any = {}; // 表列
    private fieldsSlotMap: any = {}; // 表slot
    private scroll: any = {}; // 表滚动条出现方式
    private columnsWidth: any = {}; // 表列宽
    private tableEditRowData: any = {}; // 当前编辑行数据
    private isHasEditRow: any = {}; // 是否存在编辑行
    private detailTableErrorTips: any = []; //  明细行必填提示
    @Emit('success')
    onSuccess(isChange: boolean) { }
    check() {
        this.detailTableErrorTips = [];
        this.getPageData(this.currentPageDsegin.info);
        if (this.pageModel === 'add') {
            if (this.checkForm()) {
                const apiJson = dynamicApi.addJson(this.currentPageDsegin.dataSource,
                    this.currentChangePageData);
                modelingViewService.addDynamicData(removeNullValueProperty(apiJson),
                    this.currentPageDsegin.dataSource.dataBaseId).subscribe(data =>
                        this.onSuccess(true));
            }
        } else if (this.pageModel === 'edit') {
            if (this.checkForm()) {
                const apiJson = dynamicApi.editJson(this.currentPageDsegin.dataSource,
                    this.currentChangePageData, this.currentDefaultPageData[0]);
                modelingViewService.editDynamicData(removeNullValueProperty(apiJson),
                    this.currentPageDsegin.dataSource.dataBaseId).subscribe(data =>
                        this.onSuccess(true));
            }
        } else {
            this.onSuccess(false);
        }
    }
    private checkForm() {
        let isSuccess = true;
        const {
            form: { validateFields },
        } = this;
        validateFields((errors, values) => {
            if (errors) {
                isSuccess = false;
            }
        });
        Object.keys(this.isHasEditRow).map((m: any) => {
            if (this.isHasEditRow[m]) {
                notificationHelper.error('明细表存在行编辑状态数据，请先保存');
                isSuccess = false;
            }
        });
        this.detailTableErrorTips.map((m: any) => {
            notificationHelper.error(m);
            isSuccess = false;
        });
        return isSuccess;
    }
    private getPageData(items: any) {
        items.forEach((e: any) => {
            if (e.info.type === 'form-item' && e.config.dataKey) {
                if (!this.currentChangePageData.hasOwnProperty(e.config.dataKey.split('.')[0])) {
                    this.currentChangePageData[e.config.dataKey.split('.')[0]] = {};
                }
                this.currentChangePageData[e.config.dataKey.split('.')[0]][e.config.dataKey] = e.cdata;
            } else if (e.info.type === 'layout') {
                e.info.columns.forEach((f: any) => this.getPageData(f.component));
            } else if (e.info.type === 'collapse-panel') {
                this.getPageData(e.info.columns);
            } else if (e.info.type === 'tab-panel') {
                e.config.options.forEach((f: any) => this.getPageData(f.columns));
            } else if (e.info.type === 'table') {
                if (e.config.required && e.cdata.length === 0) {
                    this.detailTableErrorTips.push(`${e.config.label}至少需要一条数据`);
                }
                this.currentChangePageData[e.config.dataKey.split('.')[0]] = [];
                e.cdata.map((m: any) => {
                    const temp: any = {};
                    Object.keys(m).map((f: any) => {
                        if (f !== 'rowAction' && f !== 'rowStatus' && f !== 'defaultData') {
                            if (e.config.dataKey.split('.')[0] === m[f].config.dataKey.split('.')[0]) {
                                temp[m[f].config.dataKey] = m[f].cdata;
                            }
                        } else {
                            temp[f] = m[f];
                        }
                    });
                    this.currentChangePageData[e.config.dataKey.split('.')[0]].push(temp);
                });
            }
        });
    }
    // 设置默认数据
    private setDefaultData(items: any) {
        items.forEach((e: any) => {
            if (e.info.type === 'form-item' && e.config.dataKey) {
                e.cdata = this.currentDefaultPageData[0][e.config.dataKey.replace('.', '_')];
            } else if (e.info.type === 'layout') {
                e.info.columns.map((m: any, i: number) => {
                    this.setDefaultData(m.component);
                });
            } else if (e.info.type === 'collapse-panel') {
                this.setDefaultData(e.info.columns);
            } else if (e.info.type === 'tab-panel') {
                e.config.options.map((m: any, i: number) => {
                    this.setDefaultData(m.columns);
                });
            } else if (e.info.type === 'table' && e.config.dataKey) {
                // 判断主键再数据源中是否为null
                const table = this.currentPageDsegin.dataSource.tables.find((f: any) => f.key === e.config.dataKey.split('.')[0]);
                const filterData = this.currentDefaultPageData.filter((f: any) => {
                    let isSuccess = false;
                    table.primaryKey.forEach((t: any) => {
                        if (f[`${table.key}_${t}`]) {
                            isSuccess = true;
                        }
                    });
                    return isSuccess;
                });
                filterData.map((f: any, i: number) => {
                    const instanceConfig: any = {};
                    e.info.columns.map((m: any) => {
                        const copy = JSON.parse(JSON.stringify(m));
                        copy.cdata = f[copy.config.dataKey.replace('.', '_')];
                        instanceConfig[copy.info.id] = copy;
                        instanceConfig[copy.info.id].info.id = `${copy.info.id}_${i}`;
                    });
                    instanceConfig['rowAction'] = 'view';
                    instanceConfig['rowStatus'] = 'default';
                    const defaultData: any = {};
                    Object.keys(f).map((m: any) => {
                        defaultData[m] = f[m];
                    });
                    instanceConfig['defaultData'] = defaultData;
                    e.cdata.push(instanceConfig);
                });
            }
        });
    }
    // formItem
    private getFormItem(item: any, pageMdel = this.pageModel) {
        if (!item) {
            return '';
        }
        const customCom = item.info.key;
        return <a-form-item
            class={styles.form_item + ' ' + styles.comp}
            label={item.config.vshow ? item.config.label : ''}
        >
            {
                <customCom
                    controlConfig={item}
                    pageModel={pageMdel}
                    v-decorator={
                        [
                            item.info.id, {
                                initialValue: item.cdata,
                                rules: [{
                                    required: pageMdel === 'view' ? false : item.config.required,
                                    message: item.config.placeholder ? item.config.placeholder :
                                        `请输入${item.config.label}`
                                }]
                            }
                        ]
                    }
                    on-change={(value: any) => item.cdata = value}
                />
            }
        </a-form-item>;
    }
    // 折叠面板
    private getCollapsePanel(item: any) {
        return <a-collapse
            class={styles.comp}
            default-active-key={item.info.id}
        >
            <a-collapse-panel
                key={item.info.id}
                header={item.config.label}
                show-arrow={false}
            >
                {
                    item.info.columns.map((m: any) => {
                        return [
                            m.info.type === 'form-item' ? this.getFormItem(m) : '',
                            m.info.type === 'tab-panel' ? this.getTabPanel(m) : '',
                            m.info.type === 'collapse-panel' ? this.getCollapsePanel(m) : '',
                            m.info.type === 'layout' ? this.getLayout(m) : '',
                            m.info.type === 'table' ? this.getTable(m) : '',
                        ];
                    })
                }
            </a-collapse-panel>
        </a-collapse>;
    }
    // tab面板
    private getTabPanel(item: any) {
        return <a-tabs
            class={styles.comp}
            default-active-key={item.config.options[0].value}
            tab-position={item.config.tabPosition}
        >
            {
                item.config.options.map((m: any, i: number) => (
                    <a-tab-pane
                        key={m.value}
                        tab={m.label}>
                        {
                            m.columns.map((c: any) => {
                                return [
                                    c.info.type === 'form-item' ? this.getFormItem(c) : '',
                                    c.info.type === 'tab-panel' ? this.getTabPanel(c) : '',
                                    c.info.type === 'collapse-panel' ? this.getCollapsePanel(c) : '',
                                    c.info.type === 'layout' ? this.getLayout(c) : '',
                                    c.info.type === 'table' ? this.getTable(c) : '',
                                ];
                            })
                        }
                    </a-tab-pane>
                ))
            }
        </a-tabs>;
    }
    // 栅格
    private getLayout(item: any) {
        return <a-row
            class={styles.comp}
            gutter={8}
        >
            {
                item.info.columns.map((m: any, i: number) => (
                    <a-col
                        span={m.span}
                    >
                        {
                            this.getFormItem(m.component[0])
                        }
                    </a-col>
                ))
            }
        </a-row>;
    }
    // 表格
    private getTable(item: any) {
        this.tableColumns[item.info.id] = [];
        this.fieldsSlotMap[item.info.id] = {};
        this.columnsWidth[item.info.id] = 0;
        if (item.config.hasSerial) {
            this.tableColumns[item.info.id].push({
                title: i18nHelper.getLocale('modeling.common.serial-number'),
                width: 80,
                align: 'center',
                customRender: (text: any, record: any, index: number) => {
                    return index + 1;
                }
            });
            this.columnsWidth[item.info.id] += 80;
        }
        item.info.columns.forEach((f: any) => {
            this.tableColumns[item.info.id].push(
                {
                    title: f.config.label,
                    align: f.config.columnAlign,
                    dataIndex: f.config.dataKey.replace('.', '_'),
                    width: f.config.columnWidth ? f.config.columnWidth : undefined,
                    scopedSlots: { customRender: f.info.id }
                }
            );
            this.columnsWidth[item.info.id] += f.config.columnWidth ? f.config.columnWidth : 100;
            this.fieldsSlotMap[item.info.id][f.info.id] = (text: any, record: any, index: number) => {
                return this.getFormItem(record[f.info.id],
                    this.pageModel === 'view' ? this.pageModel : record.rowAction);
            };
        });
        if ((this.pageModel === 'add' || this.pageModel === 'edit') && item.config.disabled) {
            this.tableColumns[item.info.id].push(
                {
                    title: i18nHelper.getLocale('modeling.common.operation'),
                    align: 'center',
                    width: 140,
                    scopedSlots: { customRender: 'operation' }
                }
            );
            this.columnsWidth[item.info.id] += 140;
            this.fieldsSlotMap[item.info.id]['operation'] = (text: any, record: any, index: number) => {
                return (
                    record.rowAction === 'view' ? [
                        <a-button
                            type='link'
                            on-click={() => {
                                if (this.isHasEditRow[item.info.id]) {
                                    notificationHelper.error('当前明细表存在行编辑状态数据，请先保存');
                                    return;
                                }
                                record['rowAction'] = 'edit';
                                this.tableEditRowData[item.info.id] = JSON.parse(JSON.stringify(record));
                                this.isHasEditRow[item.info.id] = true;
                            }}>
                            编辑
                        </a-button>,
                        <a-button
                            type='link'
                            on-click={() => {
                                if (this.isHasEditRow[item.info.id]) {
                                    notificationHelper.error('当前明细表存在行编辑状态数据，请先保存');
                                    return;
                                }
                                if (record['rowStatus'] === 'add') {
                                    item.cdata.splice(index, 1);
                                } else {
                                    record['rowStatus'] = 'delete';
                                }
                                this.isHasEditRow[item.info.id] = false;
                            }}>
                            删除
                        </a-button>
                    ] : [
                        <a-button
                            type='link'
                            on-click={() => {
                                const arrs: any = [];
                                Object.keys(record).forEach((f: any) => {
                                    if (f !== 'rowAction' && f !== 'rowStatus' && f !== 'defaultData') {
                                        arrs.push(record[f].info.id);
                                    }
                                });
                                const {
                                    form: { validateFields },
                                } = this;
                                validateFields(arrs, (errors, values) => {
                                    if (!errors || errors.length === 0) {
                                        record['rowAction'] = 'view';
                                        if (record['rowStatus'] !== 'add') {
                                            record['rowStatus'] = 'edit';
                                        }
                                        this.isHasEditRow[item.info.id] = false;
                                    }
                                });
                            }}>
                            保存
                        </a-button>,
                        <a-button
                            type='link'
                            on-click={() => {
                                if (record['rowStatus'] === 'add') {
                                    item.cdata.splice(index, 1);
                                } else {
                                    const data = JSON.parse(JSON.stringify(this.tableEditRowData[item.info.id]));
                                    Object.keys(item.cdata[index]).forEach((f: any) => {
                                        if (f !== 'rowAction' && f !== 'rowStatus' && f !== 'defaultData') {
                                            record[f].cdata = data[f].cdata;
                                            item.cdata[index][f].cdata = data[f].cdata;
                                            const setValue: any = {};
                                            setValue[record[f].info.id] = data[f].cdata;
                                            this.form.setFieldsValue(setValue);
                                        }
                                    });
                                    record['rowAction'] = 'view';
                                    item.cdata[index]['rowAction'] = 'view';
                                }
                                this.isHasEditRow[item.info.id] = false;
                            }}>
                            取消
                        </a-button>
                    ]
                );
            };
            this.fieldsSlotMap[item.info.id]['title'] = (currentPageData: any) => {
                return (<a-button on-click={() => {
                    if (this.isHasEditRow[item.info.id]) {
                        notificationHelper.error('当前明细表存在行编辑状态数据，请先保存');
                        return;
                    }
                    const items: any = {};
                    item.info.columns.map((m: any) => {
                        items[m.info.id] = JSON.parse(JSON.stringify(m));
                        items[m.info.id].info.id = `${m.info.id}_${item.cdata.length}`;
                    });
                    items['rowAction'] = 'add';
                    items['rowStatus'] = 'add';
                    this.tableEditRowData[item.info.id] = JSON.parse(JSON.stringify(items));
                    item.cdata.push(items);
                    this.isHasEditRow[item.info.id] = true;
                }}
                >新增</a-button>);
            };
        }
        return <a-table
            rowKey={(record: any, index: number) => index}
            ref={item.info.id}
            bordered
            columns={this.tableColumns[item.info.id]}
            dataSource={item.cdata.filter((f: any) => f['rowStatus'] !== 'delete')}
            pagination={false}
            scopedSlots={this.fieldsSlotMap[item.info.id]}
            scroll={{ x: this.scroll[item.info.id] ? 'max-content' : false }}
            class={styles.comp}
        />;
    }
    created(): void {
        this.currentPageDsegin = this.pageDesginJson ? JSON.parse(this.pageDesginJson) : {};
        this.form = this.$form.createForm(this, { name: guidHelper.generate() });
        const {
            form: { resetFields },
        } = this;
        resetFields();
        if (this.pageModel === 'edit' || this.pageModel === 'view') {
            const mainTable = this.currentPageDsegin.dataSource.tables.find(
                (f: any) => f.isMain
            );
            const params = dynamicApi.selectJson(this.currentPageDsegin.dataSource,
                {}, this.$route.query, false,
                0, 0, 'eject', this.parameter);
            modelingViewService.getDynamicData(params, this.currentPageDsegin.dataSource.dataBaseId)
                .subscribe((data: any) => {
                    if (data.msg === 'success') {
                        this.currentDefaultPageData = [];
                        data['[]'].forEach((e: any) => {
                            this.currentDefaultPageData.push(e[mainTable.key]);
                        });
                        console.log(this.currentDefaultPageData);
                        this.setDefaultData(this.currentPageDsegin.info);
                    }
                });
        }
        this.$nextTick(() => {
            Object.keys(this.columnsWidth).map((m: any) => {
                const clientWidth = (this.$refs[m] as any).$el.clientWidth;
                const coW = this.columnsWidth[m];
                this.$set(this.scroll, m, this.columnsWidth[m] > clientWidth ? 'max-content' : false);
            });
        });
    }
    render() {
        return (
            <div class={styles.form}>
                <a-form
                    form={this.form}
                    label-align={this.currentPageDsegin.config.alignMode}
                    label-col={{ style: `flex:0 0 ${this.currentPageDsegin.config.labelWidth}px` }}
                    wrapper-col={{ style: 'flex:1 1 auto' }}
                >
                    {
                        this.currentPageDsegin.info.map((m: any) => {
                            return [
                                m.info.type === 'form-item' ? this.getFormItem(m) : '',
                                m.info.type === 'tab-panel' ? this.getTabPanel(m) : '',
                                m.info.type === 'collapse-panel' ? this.getCollapsePanel(m) : '',
                                m.info.type === 'layout' ? this.getLayout(m) : '',
                                m.info.type === 'table' ? this.getTable(m) : '',
                            ];
                        })
                    }
                </a-form>
            </div>
        );
    }
}
