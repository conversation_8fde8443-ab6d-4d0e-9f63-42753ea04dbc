import { httpHelper } from '@/PlatformStandard/common/utils';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { TableRelationDto } from './types';

class TableRelationService {
  getTableRelationList(params: any): Observable<any> {
    const url = '/api/platform/v1/manage/table-relations';
    return httpHelper.get(url, { params: params }).pipe(
      map(data => ({
        total: data.total,
        items: data.items.map((m: any) => ({
          ...m,
          key: m.relationId
        })),
      }))
    );
  }

  getTableRelation(relationId: number | undefined): Observable<any> {
    const url = '/api/platform/v1/manage/table-relation';
    return httpHelper.get(url, { params: { relationId: String(relationId) } });
  }

  saveTableRelation(dto: TableRelationDto): Observable<any> {
    const url = '/api/platform/v1/manage/table-relation';
    return httpHelper.post(url, dto);
  }

  deleteTableRelation(relationId: number | undefined): Observable<any> {
    const url = `/api/platform/v1/manage/table-relation/${relationId}`;
    return httpHelper.delete(url);
  }
}
export const tableRelationService = new TableRelationService();
