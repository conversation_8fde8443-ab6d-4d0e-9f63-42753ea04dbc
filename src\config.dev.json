{"nacos": {"serverAddr": "*************:8848", "namespace": "shui<PERSON>", "languagegroupid": "i18n", "languageids": ["en.json", "zh.json"]}, "urlKey": "platform", "port": 32002, "uploadLimit": "200mb", "apiGateway": {"uri": "http://*************:32000", "appKey": "799e6e124ad95e09b055ae8c8cc53d7f", "appSecret": "a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0"}, "localServices": [{"enable": false, "name": "process", "uri": "http://localhost:5052"}, {"enable": false, "name": "platform", "uri": "http://localhost:5051"}], "adminUri": "", "sso": {"type": "movitech", "movitech": {"pc": {"redirectUri": "http://*************:2005/identity", "loginUri": "/shuiwuaccount/login", "logoutUri": "/shuiwuaccount/logout", "userStateUri": "/connect/userinfo", "callbackUri": "http://*************:2005/platform/auth/sso-callback", "localNetwork": "http://*************:32014"}}}, "impersonatePath": "/auth/impersonate", "noNeedAuthPaths": {"state": "/platform/v1/userExts", "captcha": "/platform/v1/captcha", "process-json": "/process/v1/process-json"}, "session": {"maxAge": 3600, "redis": {"host": "*************", "port": 6379, "db": 0, "password": "7Qx0YFWyUDsbjzBv"}}, "rabbitMQ": {"UserName": "bpm", "Password": "1nsc72AZBxNFLtw4", "host": "*************", "exchange": "boards.refresh"}, "idocv": {"view": "http://************/view/url?url="}, "laboratoryFunction": true, "documentEdit": {"fileEditUrl": "https://localhost:44307/document-edit/documentedit", "fileCentreUrl": "http://*************:10057", "fileControlUrl": "officecontrol/exeindex.html"}, "ExcelProcess": {"CustomFlag": "&"}, "formDesginConfig": {"processClientViewUrl": "http://*************:2005/customer/done/{procinstNo}", "fileUploadType": "local", "baiduak": "QRqq3vxdZq4G1D6s9cncMIvRkKL0Uxfz"}}