import { httpHelper } from '@/PlatformStandard/common/utils';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { PermissionTableDto } from './types';

class DataPermissionTableService {
  getTableList(params: any): Observable<any> {
    const url = '/api/platform/v1/manage/permission-table-list';
    return httpHelper.get(url, { params: params }).pipe(
      map(data => ({
        total: data.total,
        items: data.items.map((m: any) => ({
          ...m,
          key: m.tableId,
          locales: { ['zh']: m.descCn, ['en']: m.descEn }
        })),
      }))
    );
  }

  getTable(tableId: number | undefined): Observable<any> {
    const url = '/api/platform/v1/manage/permission-table';
    return httpHelper.get(url, { params: { tableId: String(tableId) } });
  }

  saveTable(dto: PermissionTableDto): Observable<any> {
    const url = '/api/platform/v1/manage/permission-table';
    return httpHelper.post(url, dto);
  }

  deleteTable(tableName: string): Observable<any> {
    const url = `/api/platform/v1/manage/permission-table/${tableName}`;
    return httpHelper.delete(url);
  }
}
export const dataPermissionTableService = new DataPermissionTableService();
