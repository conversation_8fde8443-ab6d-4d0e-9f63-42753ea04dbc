
  .main{
    padding-top: 10px;
    height: 340px;
    overflow: hidden;
    .left{
      float: left;
      width: 240px;
      height: 330px;
      border:1px solid #ddd;
      overflow: auto;
      // margin-right: 20px;
      // padding: 10px;
    }
    .right{
      float: right;
      width: 405px;
      .rightTop{
        width: 404px;
        height: 330px;
        // border:1px solid #ddd;
        // padding: 10px;
        // overflow: auto;
      }
      .rightBottom{
        margin-top: 15px;
        width: 298px;
        height: 100px;
        border:1px solid #ddd;
        padding: 10px;
        overflow: auto;
      }
    }
  }
  .searchBox{
    border:1px solid #ddd;
    padding: 5px;
    box-sizing: border-box;
    overflow: hidden;
    width: 100%;
    position: relative;
    div{
      width: 95%;
    }
    .searchBtn{
      width:5%;
      position: absolute;
      float: right;
      right: 5px;
      top: 5px;
    }
    :global(.ant-select-selection){
      border: 0px solid #e8e8e8;
      border-top-width: 0;
      border:none;
    }
    :global(.ant-select-arrow){
      opacity: 0;
    }
    :global(.ant-select.ant-select-focused .ant-select-selection){
      border: 0px solid #e8e8e8;
    }
  }
  .ant-table-selection-column{
    width: 15% !important;
  }



