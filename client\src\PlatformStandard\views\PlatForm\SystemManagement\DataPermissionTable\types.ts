export interface TableQueryDto {
  tableName?: string;
  desc?:      string;
}

export interface PermissionTableDto {
  tableId?:   number;
  tableName?: string;
  descCn?:    string;
  descEn?:    string;
  columns?:   PermissionColumn[];
  locales?: { [key: string]: string };
}

export interface PermissionColumn {
  columnId?:   number;
  tableName?:  string;
  columnName?: string;
  descCn?:     string;
  descEn?:     string;
  columnType?: string;
  rowKey?:     string;
}
