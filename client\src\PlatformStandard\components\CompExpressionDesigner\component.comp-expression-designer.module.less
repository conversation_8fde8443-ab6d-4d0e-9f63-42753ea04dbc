.operation {
  display: none;
}

.list{
  div {
    border: 1px dashed #ffffff;
    padding: 0px 5px;
    span {
      line-height: 26px;
      height: 26px;
      vertical-align: middle;
      margin-right: 8px;
    }
    span.operator {
      color: #4882cc;
    }
    span.logicalOperator {
      color: red;
    }
    span.operatorL:hover,
    span.operator:hover,
    span.operatorR:hover,
    span.logicalOperator:hover {
      cursor: pointer;
      text-decoration: underline;
      color: #4882cc;
    }
  }
  div:hover {
    border: 1px dashed #4882cc;
  }
  div:hover .operation {
    display: inline;
  }
}

.operatorLR ul,
.operatorTemp ul,
.logicalOperatorTemp ul {
  padding: 0px;
  margin: 5px 0px;
}

.operatorLR ul {
  max-height: 118px;
  overflow-y: scroll;
}

.operatorTemp ul {
  max-height: 208px;
  overflow-y: scroll;
}

.ul{
  li {
    list-style-type: none;
    height: 20px;
    line-height: 20px;
    vertical-align: middle;
    cursor: pointer;
    padding: 0 7px;
    margin: 2px 0px;
  }
  li:hover {
    color: #ffffff;
    background-color: #4882cc;
    border-radius: 2px;
  }
}

.h4 {
  margin: 7px 0px 0px 0px;
}

.customInput {
  font-size: 13px;
}

.explain {
  margin-top: 20px;
}