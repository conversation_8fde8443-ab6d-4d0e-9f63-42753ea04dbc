/**
 * API 转发规则：
 * {localServices}设置了值之后，指定的服务会转发至本地启动的接口；
 * 开发环境将遵守以上规则，生产环境所有 api 均会转发至网关；
 */

import config from './config.json';
import devConfig from './config.dev.json';
import { NacosConfigClient } from 'nacos';

const isDevelopment = process.env.NODE_ENV === 'development';

export let environment: { [key: string]: any | any[] } = {};
export const configJson: { [key: string]: any | any[] } = {
  isDevelopment: isDevelopment,
  ...(isDevelopment ? devConfig : config),
};

const configClient = new NacosConfigClient({
  serverAddr: configJson.nacos.serverAddr,
  namespace: configJson.nacos.namespace,
});

export async function nacos() {
  if (isDevelopment) {
    environment = configJson;
    /* await configClient.getConfig('en.management.json', configJson.nacos.group)
    .then((data: any) => {
      console.log('data====>', data);
    }); */
  } else {
    await configClient.getConfig(configJson.nacos.dataid, configJson.nacos.group).then((data: any) => {
      environment = { isDevelopment: isDevelopment, ...JSON.parse(data) };
    });
  }
}

const languageJson: { [key: string]: any | any[] } = {};

export async function i18Languages() {
  const ids = configJson.nacos.languageids as [];
  for (const languageid of ids) {
    await getlanguage(languageid);
  }
  return languageJson;
}

async function getlanguage(languageid: string) {
  const matched = languageid.match(/([A-Za-z0-9-_]+)\./i);
  const locale = matched[1];
  await configClient.getConfig(languageid, configJson.nacos.languagegroupid)
    .then((data: any) => {
      languageJson[locale] = JSON.parse(data);
    });
}
