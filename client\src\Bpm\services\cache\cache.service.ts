import { httpHelper } from '@/PlatformStandard/common/utils';
import { Observable } from 'rxjs';

class CacheService {
  /**
   * 刷新个人缓存
   * @param userId 用户Id
   */
  refreshUserCache(userId: string): Observable<any> {
    const path = `/api/platform/v1/manage/refresh-user-cache/${userId}`;
    return httpHelper.post(path);
  }

  /**
   * 获取缓存列表
   * @returns 缓存列表
   */
  getCacheList(): Observable<any> {
    const path = `/api/platform/v1/manage/cache-list`;
    return httpHelper.get(path);
  }

  /**
   * 初始化系统缓存
   */
  initSystemCache(): Observable<any> {
    const path = `/api/platform/v1/manage/init-cache`;
    return httpHelper.post(path);
  }

  /**
   * 根据缓存key刷新缓存
   * @param cacheKey 缓存Key
   */
  refreshCacheItem(cacheKey: string): Observable<any> {
    const path = `/api/platform/v1/manage/refresh-cache/${cacheKey}`;
    return httpHelper.post(path);
  }

  refreshDbProcessMap(): Observable<any> {
    const path = `/api/process/v1/process-map/setProcessMap_Cache`;
    return httpHelper.post(path, []);
  }
}
export const cacheService = new CacheService();
