import { Homes } from '@/PlatformStandard/views/PlatForm/HomeManagement';
import { Organization } from '@/PlatformStandard/views/PlatForm/OrganizationManagement/Organization';
import { User } from '@/PlatformStandard/views/PlatForm/OrganizationManagement/User';
import { UserEdit } from '@/PlatformStandard/views/PlatForm/OrganizationManagement/User/UserEdit';
import { UserView } from '@/PlatformStandard/views/PlatForm/OrganizationManagement/User/UserView';
import { RouteConfig } from 'vue-router';
import { component } from 'vue/types/umd';

export const OrganizationManagementRoutes: RouteConfig[] = [
  {
    path: 'organizations',
    component: Organization
  },
  {
    path: 'users',
    component: User
  },
  {
    path: 'users/view',
    component: UserView
  },
  {
    path: 'users/add',
    component: UserEdit
  },
  {
    path: 'users/edit',
    component: UserEdit
  }
];
