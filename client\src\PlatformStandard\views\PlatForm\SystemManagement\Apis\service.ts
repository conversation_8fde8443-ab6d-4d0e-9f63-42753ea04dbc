import { httpHelper } from '@/PlatformStandard/common/utils';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ApiDto } from './types';

class ApiService {
  getApiList(params: any): Observable<any> {
    const url = `/api/platform/v1/manage/applicationInterfaces`;
    return httpHelper.get(url, { params: params }).pipe(
      map(data => ({
        total: data.total,
        items: data.items.map((m: any) => ({
          ...m,
          key: m.apiId,
          permissionId: m.permissionIds && m.permissionIds.length > 0 ? m.permissionIds[0] : undefined
        })),
      }))
    );
  }

  saveApiDataPermission(apiDto: ApiDto): Observable<any> {
    const url = `/api/platform/v1/manage/api-data-permission`;
    return httpHelper.put(url, apiDto);
  }

  importApis(moduleCode: string, apiDtoes: any): Observable<any> {
    const url = `/api/platform/v1/manage/api-import`;
    return httpHelper.post(url, apiDtoes, { params: { moduleCode: moduleCode } });
  }

  updateApiTables(): Observable<any> {
    const url = `/api/platform/v1/manage/api-tables`;
    return httpHelper.get(url);
  }
}
export const apiService = new ApiService();
