import { Component, Vue } from 'vue-property-decorator';
import { Organization } from './Organization';
@Component({
  components: { Organization }
})

export class Configuration extends Vue {
  private ConfigPage: any[] = [
    { label: 'organization', value: 0 }
  ];
  private authorityId = '';
  private authorityName = '';
  private pageValue = '';
  private defaultTagsKey = '0';
  created() {
    const page = this.$route.query['page'] as string;
    if (page) {
      this.defaultTagsKey = this.ConfigPage.find(item => item.label === page).value.toString();
    }
    this.authorityId = this.$route.query['id'] as string;
    this.authorityName = this.$route.query['name'] as string;
  }
  onTabChange(pageIndex: number): void {
    this.defaultTagsKey = pageIndex.toString();
    this.pageValue = this.ConfigPage.find(item => item.value.toString() === pageIndex).label;
    this.$router.push('/authority-management/data-authority/config?id=' + this.authorityId + '&name='
      + this.authorityName + '&page=' + this.pageValue);
  }
  back() {
    this.$router.push('/authority-management/data-authority');
  }
  render() {
    return <div>
      <a-card title={this.$t('bpm.data-authority.name') + '：' + this.authorityName}
        bodyStyle={{ overflow: 'auto', height: '100%' }}>
        <div slot='extra'>
          <a-button on-click={this.back}>
            {this.$t('buttons.back')}
          </a-button>
        </div>
        <a-tabs default-active-key={this.defaultTagsKey} on-change={this.onTabChange}>
          <a-tab-pane key='0' tab={this.$t('bpm.data-authority.organization')}>
            <organization ref='Organization'></organization>
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </div>;
  }
}
