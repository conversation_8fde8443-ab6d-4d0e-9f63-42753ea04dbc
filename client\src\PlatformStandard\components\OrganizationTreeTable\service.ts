import { Observable } from 'rxjs';
import { OrganizationDto } from './types';
import { httpHelper } from '@/PlatformStandard/common/utils';
import { map } from 'rxjs/operators';

class OrganizationTreeTableService {
  getOrganizations(params: any): Observable<OrganizationDto> {
    const _url = '/api/platform/v1/manage/tree-organizations';
    return httpHelper
      .get(_url, { params })
      .pipe(
        map(data =>
          data.map((m: { label: any; code: any; fullPathCode: any; value: string; childCount: number }) => ({
            name: m.label,
            code: m.code,
            id: m.value.toLocaleLowerCase(),
            path: m.fullPathCode,
            children: m.childCount > 0 ? [] : undefined,
          }))
        )
      );
  }
}

export const organizationTreeTableService = new OrganizationTreeTableService();
