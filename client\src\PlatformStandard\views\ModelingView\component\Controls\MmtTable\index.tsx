import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator';
import styles from './table.module.less';
import Draggable from 'vuedraggable';
import * as con from '../../Controls';
import { BaseControl } from '../base-control';
import { guidHelper } from '../../../common/utils';
import { TableColmun } from '../../../common/defines';
@Component({
    components: {
        Draggable,
        ...con
    }
})
export class MmtTable extends BaseControl {
    @Prop() currentCheckedItemId!: string;
    @Prop({ default: true }) isMove!: boolean;
    @Prop({ default: true }) isEdit!: boolean;
    @Prop({ default: true }) isDeleted!: boolean;
    private fieldsSlotMap: any = {};
    private columns: TableColmun[] = [];
    private isHasScroll = false;
    @Emit('checked')
    checked(item: any, isFormTable: boolean) {
    }
    @Emit('deleted')
    deleted(item: any, parentItem: any, index: number) {
    }
    private add(newIndex: number, items: any) {
        const newItem = JSON.parse(JSON.stringify(items[newIndex]));
        newItem.info.id = guidHelper.generate();
        newItem.config.vshow = false;
        items.splice(newIndex, 1, newItem);
        this.checked(items[newIndex], true);
    }
    private customTag(m: any, i: number) {
        const CustomTag = m.info.key;
        return (
            <CustomTag
                controlConfig={m}
                value={m.cdata}
                key={m.info.id}
                on-change={(value: any) => m.cdata = value}
            ></CustomTag>
        );
    }
    get getCustomWidth() {
        let width = 100;
        this.controlConfig.info.columns.map((m: any) => {
            width += m.config.columnWidth;
        });
        return width;
    }
    created(): void {
    }
    render() {
        return (
            this.controlConfig ? (
                <div class={styles.div
                    + ` ${this.currentCheckedItemId === this.controlConfig.info.id
                        ? styles.active : null}`}
                >
                    <div class={styles.content}>
                        {
                            this.controlConfig.config.hasSerial ? (
                                <div class={styles.system_column}>
                                    <div class={styles.column_header}>
                                        #
                            </div>
                                    <div class={styles.column_content}>
                                        1
                            </div>
                                </div>
                            ) : ''
                        }
                        <draggable
                            v-model={this.controlConfig.info.columns}
                            group={{
                                name: 'people', put: (a: any, b: any, c: any, d: any) => {
                                    const cs = c.id.split('_')[0];
                                    return cs === 'form-item' ? true : false;
                                }
                            }}
                            animation={200}
                            ghostClass={styles.ghost}
                            class={styles.draggable}
                            style={{
                                'margin-left': `${this.controlConfig.config.hasSerial ? 50 : 0}px`,
                                'min-width': `${this.getCustomWidth}px`
                            }}
                            on-add={(event: any) => this.add(event.newIndex, this.controlConfig.info.columns)}
                        >
                            {
                                this.controlConfig.info.columns.map((c: any, i: number) => (
                                    <div class={styles.custom_column
                                        + ` ${this.currentCheckedItemId === c.info.id
                                            ? styles.active : null}`}
                                        style={{ width: `${c.config.columnWidth ? c.config.columnWidth : 100}px` }}
                                        on-click={(e: any) => {
                                            this.checked(c, true);
                                            e.stopPropagation();
                                        }}
                                    >
                                        <div class={styles.column_header}
                                            style={{ 'text-align': c.config.columnAlign }}
                                        >
                                            {c.config.label}
                                        </div>
                                        <div class={styles.column_content}>
                                            {
                                                this.customTag(c, i)
                                            }
                                        </div>
                                        {
                                            <div class={styles.column_item_action}>
                                                {
                                                    this.isDeleted && this.currentCheckedItemId === c.info.id ? (
                                                        <a-icon type='delete' title='删除'
                                                            nativeOnClick={(e: any) => {
                                                                this.deleted(c, this.controlConfig.info.columns, i);
                                                                e.stopPropagation();
                                                            }}
                                                        />
                                                    ) : null
                                                }
                                            </div>
                                        }
                                        {
                                            this.isMove && this.currentCheckedItemId === c.info.id ? (
                                                <div class={styles.form_item_drag}>
                                                    <a-icon type='drag' class='drag-widget' />
                                                </div>
                                            ) : null
                                        }
                                    </div>
                                ))
                            }</draggable>
                    </div>
                    {
                        this.isEdit || this.isDeleted ? (
                            <div class={styles.form_item_action}>
                                {
                                    this.isEdit ? (
                                        <a-icon type='edit' title='属性设置'
                                            nativeOnClick={(e: any) => {
                                                this.checked(this.controlConfig, false);
                                                e.stopPropagation();
                                            }}
                                        />
                                    ) : null
                                }
                                {
                                    this.isDeleted ? (
                                        <a-icon type='delete' title='删除'
                                            nativeOnClick={(e: any) => {
                                                this.deleted(this.controlConfig, null, -1);
                                                e.stopPropagation();
                                            }}
                                        />
                                    ) : null
                                }
                            </div>
                        ) : null
                    }
                    {
                        this.isMove &&
                            this.currentCheckedItemId === this.controlConfig.info.id ? (
                            <div class={styles.form_item_drag}>
                                <a-icon type='drag' class='drag-widget' />
                            </div>
                        ) : null
                    }
                </div>
            ) : null
        );
    }
}
