import { saiwuSettingService } from '@/Bpm/services/saiwu-setting/saiwu-setting.service';
import { Component, Vue } from 'vue-property-decorator';

@Component({ components: {} })
export class SaiwuSetting extends Vue {
  RefreshProduct() {
    saiwuSettingService.refreshProduct().subscribe(() => {
      this.$message.success('同步成功');
    });
  }
  StructuredStorage() {
    saiwuSettingService.structuredStorage().subscribe(() => {
      this.$message.success('执行完成');
    });
  }

  created() {

  }

  render() {
    return (
      <a-card>
        <a-button on-click={this.RefreshProduct}>同步产品数据</a-button>
        <a-button style='margin-left: 10px;' on-click={this.StructuredStorage}>结构化存储执行</a-button>
      </a-card>
    );
  }
}
