@import '../../../themes/default/variables.less';

.tagColor(@tag-color) {
  color: @tag-color;
  background-color: tint(@tag-color, 80%);
}

.tagResetColor(@tag-color) {
  :global(.ant-badge-status-dot) {
    background-color: @tag-color;
  }
}

.tag {
  font-size: @font-size-sm;
  line-height: 1.5;
  display: inline-block;
  padding: 0 7px;
  white-space: nowrap;
  border-radius: @border-radius-base;

  &.start {
    .tagColor(@process-start);
  }

  &.ready {
    .tagColor(@process-ready);
  }

  &.processing {
    .tagColor(@process-processing);
  }

  &.approved {
    .tagColor(@process-approved);
  }

  &.refused {
    .tagColor(@process-refused);
  }

  &.canceled {
    .tagColor(@process-canceled);
  }

  &.rejected {
    .tagColor(@process-rejected);
  }

  &.todo {
    .tagColor(@process-todo);
  }
}

:global(.ant-badge) {
  &.start {
    .tagResetColor(@process-start);
  }

  &.ready {
    .tagResetColor(@process-ready);
  }

  &.processing {
    .tagResetColor(@process-processing);
  }

  &.approved {
    .tagResetColor(@process-approved);
  }

  &.refused {
    .tagResetColor(@process-refused);
  }

  &.canceled {
    .tagResetColor(@process-canceled);
  }

  &.rejected {
    .tagResetColor(@process-rejected);
  }

  &.todo {
    .tagResetColor(@process-todo);
  }
}
