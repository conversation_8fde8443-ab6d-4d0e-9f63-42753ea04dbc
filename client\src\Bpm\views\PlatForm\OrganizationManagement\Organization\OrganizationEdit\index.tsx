import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import { UserTransferInput } from '@/PlatformStandard/components';
import { SelectUser } from '@/PlatformStandard/components/SelectUser';
import { OrganizationDto, OrganizationExt, DomainLevelDto } from '../types';
import { UserItemDto } from '@/PlatformStandard/components/OrganizationUser/types';
import { WrappedFormUtils } from 'ant-design-vue/types/form/form';
import { formHelper, i18nHelper } from '@/PlatformStandard/common/utils';
import { ValueLabelPair } from '@/PlatformStandard/common/defines';
import { organizationService } from '../service';
import { getComponentFromProp } from '@/PlatformStandard/common/utils';

@Component(
  { components: { UserTransferInput, SelectUser } }
)
export class OrganizationEdit extends Vue {
  @Prop() selectOrganization!: OrganizationDto;
  @Prop() option!: number; // 0:添加顶级组织 1：添加子组织 2：编辑组织
  // @Prop() value: UserItemDto[] = [];
  // @Prop() valueNew: UserItemDto[] = [];
  // @Prop() resultList!: any;
  private currentOrganization: OrganizationDto = { extendColumns: {} };
  private currentOrganizationExt: OrganizationExt = { organizationId: this.selectOrganization.organizationId };
  private selectLeader: UserItemDto[] = [];
  private selectFGFZer: UserItemDto[] = [];
  private form!: WrappedFormUtils;
  private domainItems: ValueLabelPair[] = [];
  private domainLevelOptions: ValueLabelPair[] = [];
  private domainLevelItems: DomainLevelDto[] = [];
  private cLevelItems: ValueLabelPair[] = [];
  private userVisible = false;
  private userVisibleSecond = false;
  private resultList: any;
  private inputValue: any;

  // @Watch('value') onValueChange(v: UserItemDto[]) {
  //   this.inputValue = this.selectedStringNew(v);
  // }
  // @Watch('valueNew') onValueChangeNew(v: UserItemDto[]) {
  //   this.inputValue = this.selectedStringNew(v);
  // }
  save(): OrganizationDto {
    this.currentOrganization.extendColumns = this.currentOrganizationExt;
    // this.currentOrganization.manager = this.
    return this.currentOrganization;
  }

  validateForm(): string[] {
    return formHelper.validateForm(this.form);
  }

  created() {
    // console.log(JSON.stringify(this.selectOrganization));
    const selectedRows = getComponentFromProp(this, 'selectedRows');
    // console.log(JSON.stringify(selectedRows));
    this.form = this.$form.createForm(this, { name: 'organizationForm' });
    organizationService.getDomains().subscribe(data => {
      this.domainItems = data;
      this.$forceUpdate();
    });

    switch (this.option) {
      case 0:
        this.currentOrganization = { level: 0, sortCode: 0, extendColumns: { organizationId: this.selectOrganization.organizationId } };
        this.selectLeader = [{
          userId: '',
          label: ''
        }];
        this.selectFGFZer = [{
          userId: '',
          userName: '',
          label: ''
        }];
        break;
      case 1:
        this.currentOrganization = {
          level: Number(this.selectOrganization.level) + 1,
          upperId: this.selectOrganization.organizationId,
          upperName: this.selectOrganization.name,
          extendColumns: { organizationId: this.selectOrganization.organizationId }
        };
        this.selectLeader = [{
          userId: '',
          label: ''
        }];
        this.selectFGFZer = [{
          userId: '',
          userName: '',
          label: ''
        }];
        break;
      case 2:
      default:
        this.currentOrganization = JSON.parse(JSON.stringify(this.selectOrganization));
        this.currentOrganizationExt = this.currentOrganization.extendColumns;

        if (this.currentOrganizationExt.domainId) {
          this.refreshDomainLevel(this.currentOrganizationExt.domainId, true);
        }
        if (this.currentOrganizationExt.domainLevelId) {
          this.refreshCLevel(this.currentOrganizationExt.domainLevelId, true);
        }

        if (this.currentOrganization.manager && this.currentOrganization.extendColumns.domainName !== null) {
          this.selectLeader = [{
            userId: this.currentOrganization.extendColumns.managerIds,
            userName: this.currentOrganization.extendColumns.managerNames,
            label: this.currentOrganization.extendColumns.managerNames
          }];
        } else {
          this.selectLeader = [{
            userId: '',
            label: ''
          }];
        }
        console.log(this.currentOrganization.extendColumns.fgfzId);
        console.log(JSON.stringify(this.selectLeader));
        // alert(this.currentOrganization.extendColumns.fgfzId);
        if (this.currentOrganization.extendColumns.fgfzIds && this.currentOrganization.extendColumns.fgfzIds !== null) {
          this.selectFGFZer = [{
            userId: this.currentOrganization.extendColumns.fgfzIds,
            userName: this.currentOrganization.extendColumns.fgfzNames,
            label: this.currentOrganization.extendColumns.fgfzNames
          }];
        } else {
          this.selectFGFZer = [{
            userId: '',
            userName: '',
            label: ''
          }];
        }
        break;
    }

    // this.onValueChange(this.value || []);
    // this.onValueChangeNew(this.valueNew || []);
  }

  private selectedStringNew(data: UserItemDto[]): string {
    return (data || [])
      .map(m => {
        return m.label;
      })
      .join('; ');
  }

  private domainChange(domainId: string) {
    if (domainId) {
      this.currentOrganizationExt = { organizationId: this.selectOrganization.organizationId };
      this.currentOrganizationExt.domainId = domainId;
      this.currentOrganizationExt.domainName = (this.domainItems.find((d: any) => d.value === domainId) as any).label;
      this.refreshDomainLevel(domainId, false);
    } else {
      this.currentOrganizationExt = { organizationId: this.selectOrganization.organizationId };
    }
  }

  private domainLevelChange(domainLevelId: string) {
    if (domainLevelId) {
      const item = this.domainLevelItems.find((d: DomainLevelDto) => d.id === domainLevelId) as any;
      this.currentOrganizationExt.domainLevelName = item.levelName;
      this.currentOrganizationExt.domainLevelId = domainLevelId;
      this.currentOrganizationExt.domainLevelCode = item.levelCode;
      this.currentOrganizationExt.weight = item.weight;
      this.refreshCLevel(domainLevelId, false);
    }
  }

  private domainCLevelChange(domainCLevelId: string) {
    this.currentOrganizationExt.cLevelId = domainCLevelId;
    this.currentOrganizationExt.cLevelName = (this.cLevelItems.find((d: any) => d.value === domainCLevelId) as any).label;
  }
  private refreshDomainLevel(domainId: string, isCreated: boolean) {
    this.domainLevelOptions = [];
    organizationService.getDomainLevels(domainId).subscribe(data => {
      this.domainLevelItems = data;
      data.map(x => {
        this.domainLevelOptions.push({ value: x.id || '', label: x.levelName || '' });
      });

      this.$forceUpdate();
      if (isCreated) {
        if (this.domainLevelOptions.length > 0) {
          if (this.domainLevelOptions.filter(x => x.value === this.currentOrganizationExt.domainLevelId).length > 0) {
            this.currentOrganization.extendColumns.domainLevelName = (this.domainLevelOptions.find((d: any) =>
              d.value === this.currentOrganizationExt.domainLevelId) as any).label;
          }
        }
      }
    });
  }

  private refreshCLevel(domainLevelId: string, isCreated: boolean) {
    organizationService.getCLevels(domainLevelId).subscribe(data => {
      this.cLevelItems = data;
      this.$forceUpdate();
      if (isCreated) {
        // 防止页面加载前，显示数据Label 已经在基础数据中被修改；
        if (this.cLevelItems.length > 0) {
          if (this.cLevelItems.filter(x => x.value === this.currentOrganizationExt.cLevelId).length > 0) {
            this.currentOrganization.extendColumns.cLevelName = (this.cLevelItems.find((d: any) =>
              d.value === this.currentOrganizationExt.cLevelId) as any).label;
          }
        }
      }
    });
  }
  private userClosepop(result: any, resultList: any, iscancel: any) {  // 获取选人过来的数据
    console.log(JSON.stringify(resultList));
    this.userVisible = result;
    if (!iscancel) {
      resultList = resultList.filter((x: { value: string; }) => x.value !== '');
      this.selectLeader = resultList.map((item: any) => {
        return {
          userId: item.value,
          userName: item.label,
          label: item.label
        };
      });
      this.currentOrganizationExt.managerIds = '';
      resultList.map((item: any) => {
        this.currentOrganizationExt.managerIds += item.value + ';';
      });
      this.currentOrganizationExt.managerNames = '';
      resultList.map((item: any) => {
        this.currentOrganizationExt.managerNames += item.label + ';';
      });

      // this.selectLeader[0].userId = resultList.length > 0 ? resultList[0].value : '';
      // this.selectLeader[0].userName = resultList.length > 0 ? resultList[0].label : '';
      // this.selectLeader[0].label = resultList.length > 0 ? resultList[0].label : '';
      // this.currentOrganizationExt.managerName = resultList.length > 0 ? resultList[0].label : '';
      // this.currentOrganization.manager = resultList.length > 0 ? resultList[0].value : '';
      this.$emit('change', this.selectLeader);
      this.$emit('on-change', this.selectLeader);
    }
  }
  private userClosepopSecond(result: any, resultList: any, iscancel: any) {
    // console.log(JSON.stringify(resultList));
    this.userVisibleSecond = result;
    // alert(this.userVisibleSecond);
    if (!iscancel) {
      resultList = resultList.filter((x: { value: string; }) => x.value !== '');
      this.selectFGFZer = resultList.map((item: any) => {
        return {
          userId: item.value,
          userName: item.label,
          label: item.label
        };
      });
      this.currentOrganizationExt.fgfzIds = '';
      resultList.map((item: any) => {
        this.currentOrganizationExt.fgfzIds += item.value + ';';
      });
      this.currentOrganizationExt.fgfzNames = '';
      resultList.map((item: any) => {
        this.currentOrganizationExt.fgfzNames += item.label + ';';
      });
      // this.currentOrganization.extendColumns.fgfzId = resultList.length > 0 ? resultList[0].value : '';
      // this.currentOrganization.extendColumns.fgfzName = resultList.length > 0 ? resultList[0].label : '';
      // this.selectFGFZer =  [resultList[0]] ;
      // this.selectFGFZer = resultList.length > 0 ? resultList : [{ userId: '', userName: '', label: '' }];
      console.log(this.selectFGFZer);
      this.$emit('change', this.selectFGFZer);
      this.$emit('on-change', this.selectFGFZer);
    }
  }
  render() {
    return (
      <div>
        <a-form form={this.form} labelCol={{ span: 5 }} wrapperCol={{ span: 16 }}>
          <a-row>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.organization.superiorOrganization')}>
                {this.currentOrganization.upperName}
              </a-form-item>
            </a-col>
            <a-col span='12'>

            </a-col>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.fields.level')}>
                {this.currentOrganization.level}
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.fields.name')} required>
                <a-input on-change={(e: any) => { this.currentOrganization.name = e.target.value; }}
                  placeholder={this.$l.getLocale(['controls.input', 'platform.fields.name'])}
                  v-decorator={['name', {
                    initialValue: this.currentOrganization.name,
                    rules: [{ required: true, message: this.$l.getLocale(['controls.input', 'platform.fields.name']) }]
                  }]} />
              </a-form-item>
            </a-col>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.fields.code')} required>
                <a-input on-change={(e: any) => { this.currentOrganization.organizationCode = e.target.value; }}
                  placeholder={this.$l.getLocale(['controls.input', 'platform.fields.code'])}
                  v-decorator={['code', {
                    initialValue: this.currentOrganization.organizationCode,
                    rules: [{ required: true, message: this.$l.getLocale(['controls.input', 'platform.fields.code']) }]
                  }]} />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.fields.leader')}>
                {/* <user-transfer-input
                  multiple={false}
                  value={this.selectLeader}
                  placeholder={this.$l.getLocale(['controls.input', 'platform.fields.leader'])}
                  on-change={(data: UserItemDto) => {
                    this.currentOrganization.manager = data.userId;
                    this.currentOrganization.extendColumns.managerName = data.userName;
                    this.selectLeader = [data];
                  }}
                /> */}
                <a-input
                  v-model={this.currentOrganizationExt.managerNames}
                  on-click={() => {
                    if (!this.userVisible) {
                      this.userVisible = true;
                    }
                  }}
                >
                  <a-icon slot='prefix' type='user' />
                </a-input>
              </a-form-item>
            </a-col>
            <a-col span='12'>
              <a-form-item label={i18nHelper.getLocaleObject('platform.organization.positionTypeDataset')[1].label}>
                {/* <user-transfer-input
                  multiple={false}
                  value={this.selectFGFZer}
                  placeholder={this.$l.getLocale(['controls.input',
                    i18nHelper.getLocaleObject('platform.organization.positionTypeDataset')[1].label])}
                  on-change={(data: UserItemDto) => {
                    this.currentOrganization.extendColumns.fgfzId = data.userId;
                    this.currentOrganization.extendColumns.fgfzName = data.userName;
                    this.selectFGFZer = [data];
                  }}
                /> */}
                {/* {JSON.stringify(this.selectFGFZer[0])} */}
                <a-input
                  v-model={this.currentOrganizationExt.fgfzNames}
                  on-click={() => {
                    if (!this.userVisibleSecond) {
                      this.userVisibleSecond = true;
                    }
                  }}
                >
                  <a-icon slot='prefix' type='user' />
                </a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.organization.businessType')} required>
                <a-select
                  options={this.domainItems}
                  placeholder={this.$l.getLocale(['controls.select', 'platform.organization.businessType'])}
                  style='width:100%;'
                  on-change={(value: any) => this.domainChange(value)}
                  v-decorator={['domain', {
                    initialValue: this.currentOrganizationExt.domainId,
                    rules: [{ required: true, message: this.$l.getLocale(['controls.select', 'platform.organization.businessType']) }]
                  }]}
                >
                </a-select>
              </a-form-item>
            </a-col>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.organization.businessTypeLevel')} required>
                <a-select
                  options={this.domainLevelOptions}
                  placeholder={this.$l.getLocale(['controls.select', 'platform.organization.businessTypeLevel'])}
                  style='width:100%;'
                  on-change={(value: any) => this.domainLevelChange(value)}
                  v-decorator={['domainLevelId', {
                    initialValue: this.currentOrganizationExt.domainLevelId,
                    rules: [{ required: true, message: this.$l.getLocale(['controls.select', 'platform.organization.businessTypeLevel']) }]
                  }]}
                >
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.fields.domainLevelCName')}>
                <a-select
                  options={this.cLevelItems}
                  placeholder={this.$l.getLocale(['controls.select', 'platform.fields.domainLevelCName'])}
                  style='width:100%;'
                  v-model={this.currentOrganizationExt.cLevelName}
                  on-change={(value: any) => this.domainCLevelChange(value)}
                >
                </a-select>
              </a-form-item>
            </a-col>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.fields.order')}>
                <a-input-number v-model={this.currentOrganization.sortCode} min={0}
                  placeholder={this.$l.getLocale(['controls.input', 'platform.fields.order'])} style='width:100%' />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.fields.remark')}>
                <a-textarea v-model={this.currentOrganization.remark}
                  placeholder={this.$l.getLocale(['controls.input', 'platform.fields.remark'])} rows='4' />
              </a-form-item>
            </a-col>
          </a-row>
          <select-user visible={this.userVisible} valueNew={this.selectLeader}
            multiple={true}
            on-closepop={this.userClosepop}
          ></select-user>
          <select-user visible={this.userVisibleSecond} valueNew={this.selectFGFZer}
            multiple={true}
            on-closepop={this.userClosepopSecond}
          ></select-user>
        </a-form>
      </div>
    );
  }
}
