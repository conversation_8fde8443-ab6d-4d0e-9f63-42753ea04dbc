import { Observable } from 'rxjs';
import { httpHelper } from '@/PlatformStandard/common/utils';
import { DataAuthorityRoleDto, RoleDto, RoleListDto } from './types';
import { map } from 'rxjs/internal/operators/map';
class RoleService {
    getRoles(params: any): Observable<RoleListDto> {
        const _url = `/api/platform/v1/manage/roles`;
        return httpHelper.get(_url, { params: params });
    }
    getDataAuthoritysRole(authorityId: string): Observable<DataAuthorityRoleDto[]> {
        const _url = `/api/platform/v1/manage/data-authoritys-role/${authorityId}`;
        return httpHelper.get(_url);
    }
    saveDataAuthoritysRole(authorityId: string, data: RoleDto[]): Observable<string> {
        const url = `/api/platform/v1/manage/data-authoritys/${authorityId}/role`;
        return httpHelper.post(url, data);
    }
}
export const roleService = new RoleService();
