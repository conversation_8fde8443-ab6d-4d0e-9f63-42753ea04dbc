import { Observable, BehaviorSubject, of } from 'rxjs';

import { httpHelper, i18nHelper } from '@/PlatformStandard/common/utils';
import { Settings, ValueLabelPair } from '@/PlatformStandard/common/defines';
import { authService, Featrue } from '../auth';
import { MenuGroup, MenuViewState, PageActionDataDto, PageMenu } from './menu.types';

class MenuService {
  modules: MenuGroup[] = [];
  authorizedMenus: MenuGroup[] = [];
  authorizedPages: PageMenu[] = [];
  private defaultPage = '';
  private menuViewStateSource: BehaviorSubject<MenuViewState>;
  private menuActionCache: any = {};

  menuViewState$: Observable<MenuViewState>;

  /**
   * 菜单状态
   */
  get menuViewState(): MenuViewState {
    const cachedState = localStorage.getItem(Settings.UserMenuViewStateCacheKey);
    return cachedState === 'collapsed' || (!cachedState && document.body.clientWidth <= Settings.ScreenBoundary)
      ? MenuViewState.Collapsed
      : MenuViewState.Expanded;
  }

  constructor() {
    // 初始化菜单显示状态
    this.menuViewStateSource = new BehaviorSubject<MenuViewState>(this.menuViewState);
    this.menuViewState$ = this.menuViewStateSource.asObservable();
  }

  /**
   * 切换菜单伸缩状态
   * @param menuViewState 菜单伸缩状态
   */
  switchMenuViewState(menuViewState: MenuViewState) {
    localStorage.setItem(Settings.UserMenuViewStateCacheKey, menuViewState);
    this.menuViewStateSource.next(menuViewState);
  }

  updateAuthorization(features: Featrue[] | undefined) {
    /* if (features) {
      if (this.authorizedMenus.length > 0) {
        this.defaultPage = this.authorizedMenus[0].children[0].route;
      }
    } else {
      this.authorizedMenus = [];
      this.defaultPage = '';
    } */
    this.defaultPage = '/home-management/homes';
  }

  getAuthorizedMenus(): MenuGroup[] {
    this.modules = [];
    this.authorizedMenus = [];
    this.authorizedPages = [];
    const menus: MenuGroup[] = [];
    if (authService.user && authService.user.menus && (authService.user.menus as []).length > 0) {
      (authService.user.menus as []).forEach((first: any) => {
        this.modules.push({
          key: `${first.path}`,
          locales: { zh: first['name'], en: first['englishName'] },
          icon: first.icon,
          children: [],
          hidden: first.hidden
        });
        if ((first.children || []).length > 0) {
          first.children.forEach((second: any) => {
            const menuItem: MenuGroup = {
              key: `${first.path}/${second.path}`,
              locales: { zh: second['name'], en: second['englishName'] },
              icon: second.icon ? second.icon : 'book',
              children: [],
              hidden: second.hidden
            };
            if ((second.children || []).length > 0) {
              second.children.forEach((third: any) => {
                const pageItem: PageMenu = {
                  key: `${first.path}/${third.path}`,
                  locales: { zh: third['name'], en: third['englishName'] },
                  route: `${third.path}`,
                  hidden: third.hidden,
                  actionIds: third.actionIds,
                  permissionId: third.permissionId,
                };
                menuItem.children.push(pageItem);
                this.authorizedPages.push(pageItem);
              });
            }
            menus.push(menuItem);
          });
        }
      });
    }
    this.authorizedMenus = menus;
    /* const currentModulePages = this.authorizedPages.filter(page => page.key.startsWith(Settings.AppKey));
    if ( currentModulePages.length > 0 ) {
      this.defaultPage = currentModulePages[0].route;
    } */
    return menus;
  }

  getDefaultPage(): string {
    if (this.defaultPage) {
      return this.defaultPage;
    }

    return '/401';
  }

  getCurrentPageKeys(path: string): string[] {
    const menuGroup = this.authorizedMenus.find(m => m.children.some(c => c.route === path));
    if (menuGroup) {
      const page = menuGroup.children.find(c => c.route === path);
      if (page) {
        return [menuGroup.key, page.key];
      }
    }

    return ['', ''];
  }

  getPageAuthorizedActions(path: string): string[] {
    if (this.menuActionCache[path]) {
      return this.menuActionCache[path];
    }

    this.authorizedMenus.forEach(m => {
      const query = m.children.filter(c => `/${c.route}` === path);
      if (query.length > 0) {
        this.menuActionCache[path] = query[0].actionIds || [];
      }
    });

    return this.menuActionCache[path];
  }

  getPageActions(routes: any[], paths: string[]): PageActionDataDto {
    const result: PageActionDataDto = {};
    paths.forEach(p => {
      const currentVue = routes.find(f => f.path === p);
      if (currentVue && currentVue.instances) {
        const actions = (currentVue.instances.default as any).pageActions || [];
        actions.forEach((a: string) => {
          if (!result[p]) {
            result[p] = [];
          }

          result[p].push({ value: a, label: i18nHelper.getLocale(`buttons.${a}`) });
        });
      }
    });

    return result;
  }

  getBreadcrumbs(path: string, languageCode: string): string[] {
    const breadcrumbs: string[] = [];
    // 筛选页面
    const menuGroup = this.authorizedMenus.find(m => m.children.some(c => '/' + c.route === path));
    if (menuGroup) {
      const page = menuGroup.children.find(c => '/' + c.route === path);
      if (page) {
        breadcrumbs.push(menuGroup.locales[languageCode]);
        breadcrumbs.push(page.locales[languageCode]);
      }
    }
    return breadcrumbs;
  }
}

export const menuService = new MenuService();
