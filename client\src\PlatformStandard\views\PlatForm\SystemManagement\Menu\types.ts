export interface MenuEntity {
  actionIds?: string[];
  appId?: string;
  englishName?: string;
  filePath?: string;
  hidden?: boolean;
  icon?: string;
  id?: string;
  inUse?: boolean;
  name?: string;
  order?: number;
  parentId?: string;
  path?: string;
  permissionIds?: string;
  type?: string;
  children?: MenuEntity[];
}

export interface AppMenuLevelDto {
  appCode:   string;
  menuLevel: number;
}
