import { Observable } from 'rxjs';

import logo from '@/assets/images/logo+R_white.svg';
import logo2 from '@/assets/images/logo+R_blue.svg';
import logoProject from '@/assets/images/mt_boost.png';
import logoM from '@/assets/images/logo_M.png';
import logo_head from '@/assets/images/Avatar_default.png';
import { Settings } from '@/PlatformStandard/common/defines/settings';
import { ValueLabelPair } from '@/PlatformStandard/common/defines';
import { httpHelper } from '@/PlatformStandard/common/utils';
import VueRouter, { Route } from 'vue-router';
import { map } from 'rxjs/operators';

class CommonService {
  get logo() {
    return Settings.EnableMovitechLogo ? logo : logoProject;
  }

  get logo2() {
    return logo2;
  }
  get logoM() {
    return logoM;
  }
  get logo_head() {
    return logo_head;
  }
  getDictionaries(params: string[]): Observable<{ [key: string]: ValueLabelPair[] } | ValueLabelPair[]> {
    let paramsString = '';
    params.forEach((item: string, index: number) => {
      paramsString += `${index === 0 ? '?' : '&'}group=${item}`;
    });
    const _url = `/api/platform/v1/select-dictionaries${paramsString}`;
    return httpHelper.get(_url, undefined, { loading: false });
  }

  getEnums(params: string[]): Observable<{ [key: string]: ValueLabelPair[] } | ValueLabelPair[]> {
    let paramsString = '';
    params.forEach((item: string, index: number) => {
      paramsString += `${index === 0 ? '?' : '&'}name=${item}`;
    });
    const _url = `/api/platform/v1/commons/enum-items${paramsString}`;
    return httpHelper.get(_url, undefined, { loading: false });
  }

  /**
   * 返回父级页面
   */
  backParentPage(route: Route, router: VueRouter) {
    const len = route.matched.length;
    const currentPath = route.matched[len - 1].path;
    const currentPathIndex = currentPath.lastIndexOf('/');
    if (len > 1 && currentPathIndex !== -1) {
      router.push(currentPath.substring(0, currentPathIndex));
    }
  }

  /**
   * 获取设定
   * @param group 设定分组
   */
  getSettings(group: string): Observable<any> {
    const url = `/api/platform/v1/manage/init-settings/${group}`;
    return httpHelper.get(url);
  }

 /**
  * 获取应用集合
  * @returns 应用集合
  */
  getApplications(): Observable<any> {
    const path = `/api/platform/v1/manage/applications`;
    return httpHelper.get(path)
      .pipe(map(data => data.map((m: any) => ({ label: m.name, value: m.id, code: m.code }))));
  }
}

export const commonService = new CommonService();
