import { form<PERSON><PERSON><PERSON>, guid<PERSON><PERSON><PERSON>, i18n<PERSON>elper } from '@/PlatformStandard/common/utils';
import { languageService } from '@/PlatformStandard/services/language';
import { WrappedFormUtils } from 'ant-design-vue/types/form/form';
import { Component, Prop, Vue } from 'vue-property-decorator';
import { dataPermissionService } from '../service';
import { ConditionRelation, DataPermissionDto, Detail, PermissionColumn, PermissionTable } from '../types';
import styles from './Conditions.module.less';
@Component
export class DataPermissionCondition extends Vue {
  @Prop() selectPermissionId!: string;
  private form!: WrappedFormUtils;
  private currentPermission: DataPermissionDto = {};
  private conditions: Detail[] = [];
  private currentLanguageCode = languageService.current.code;
  private columns: PermissionColumn[] = [];
  private statusDataset = i18nHelper.getLocaleObject('platform.dataPermission.statusDataset');

  private operators: any = [];
  private conditionTypes: any = [];
  private systemVariables: any = [];

  private conditonRelations: ConditionRelation = { logicType: 'group', logicRelation: 'and', childRelations: [] };

  created() {
    this.form = this.$form.createForm(this, { name: 'conditionForm' });
    this.operators = i18nHelper.getLocaleObject('platform.dataPermission.operators');
    this.conditionTypes = i18nHelper.getLocaleObject('platform.dataPermission.conditionType');
    this.systemVariables = i18nHelper.getLocaleObject('platform.dataPermission.systemVariables');
    if (this.selectPermissionId) {
      dataPermissionService.getDataPermission(this.selectPermissionId).subscribe(data => {
        this.currentPermission = data;
        this.conditions = this.currentPermission.details as Detail[];
        this.conditions.forEach(condition => {
          condition.rowKey = guidHelper.generate().replace(/-/g, '').substring(0, 4);
          (condition.columnOptions as PermissionColumn[]).map(m => (
            Object.assign(m, { locales: { ['zh']: m.descCn, ['en']: m.descEn } })
          ));
        });
        this.conditonRelations = JSON.parse(this.currentPermission.expressionJson as string);
      });
    } else {
      this.addConditions();
    }
  }

  validateForm(): string[] {
    return formHelper.validateForm(this.form);
  }

  save(): DataPermissionDto {
    this.conditions.forEach((data, index) => {
      data.orderNum = index + 1;
    });
    this.currentPermission.details = this.conditions;
    this.currentPermission.expression = this.logicText;
    this.currentPermission.expressionJson = JSON.stringify(this.conditonRelations);
    return JSON.parse(JSON.stringify(this.currentPermission));
  }

  /* private rowText(condition: Detail): string {
    let text = '';
    switch (condition.optionType) {
      case 1:
        text += ` [${condition.tableName}].${condition.columnName} = `;
        break;
      case 2:
        text += ' =';
        break;
    }
    return text;
  } */

  private recursionRelation(item: ConditionRelation, index: number, relation: string) {
    let text = '';
    if (item.logicType === 'group' && (item.childRelations as []).length > 0) {
      text += (index > 0 ? `${relation} ` : '') + '( ';
      (item.childRelations as ConditionRelation[]).forEach((e: ConditionRelation, i: number) => {
        text += this.recursionRelation(e, i, item.logicRelation as string);
      });
      text += ') ';
    } else if (item.logicType === 'condition') {
      if (index === 0) {
        text += `${item.conditionCode} `;
      } else {
        text += `${relation} ${item.conditionCode} `;
      }
    }
    return text;
  }

  private get logicText(): string {
    return this.recursionRelation(
      this.conditonRelations,
      0,
      this.conditonRelations.logicRelation as string
    );
  }

  private addConditions() {
    this.conditions.push({
      tableName: undefined,
      columnName: undefined,
      optionType: 1,
      conditionType: 1,
      conditionValue: undefined,
      columnOptions: [],
      rowKey: guidHelper.generate().replace(/-/g, '').substring(0, 4)
    });
  }

  private deleteCondition(rowKey?: string) {
    this.conditions = this.conditions.filter(d => d.rowKey !== rowKey);
  }

  private tableChaneg(condition: Detail, tableName: string) {
    condition.columnName = undefined;
    dataPermissionService.getColumns(tableName).subscribe(data => {
      condition.columnOptions = data;
    });
  }

  private changeLogic(item: ConditionRelation) {
    item.logicRelation = item.logicRelation === 'and' ? 'or' : 'and';
    this.$forceUpdate();
  }

  private delCondition(item: ConditionRelation, index: number) {
    (item.childRelations as []).splice(index, 1);
    this.$forceUpdate();
  }

  private addCondition(items: ConditionRelation[], index: number) {
    items.push({
      logicType: 'condition',
      conditionCode: index
    });
    this.$forceUpdate();
  }

  private addGroup(items: ConditionRelation[]) {
    items.push({
      logicType: 'group',
      logicRelation: 'and',
      childRelations: []
    });
    this.$forceUpdate();
  }

  private delGroup(items: any, index: number) {
    items.splice(index, 1);
    this.$forceUpdate();
  }

  private logic(
    conditions: Detail[],
    conditonRelation: ConditionRelation,
    chiliren: ConditionRelation[],
    parentRelationIndex: number) {
    return (
      <div class={styles.logic_group}>
        <div class={styles.logic_relation}>
          <a-button type='link' on-click={() => this.changeLogic(conditonRelation)}>
            {conditonRelation.logicRelation === 'and' ? '且' : '或'}
          </a-button>
        </div>
        <div class={styles.logic_content}>
          {
            (conditonRelation.childRelations as ConditionRelation[]).map((m: ConditionRelation, i: number) => {
              return m.logicType === 'condition' ? (
                <a-tag
                  color='cyan'
                  closable
                  style='margin-bottom:5px;'
                  on-close={() => this.delCondition(conditonRelation, i)}
                >
                  {m.conditionCode}
                </a-tag>
              ) : this.logic(conditions, m, conditonRelation.childRelations as [], i);
            }
            )
          }
        </div>
        <div class={styles.logic_option}>
          {
            conditions.length > 0 ? (
              <a-dropdown>
                <a-icon type='plus' style='color:#1890ff'
                  on-click={(e: any) => e.preventDefault()}></a-icon>
                <a-menu slot='overlay'>
                  {
                    conditions.map((m: Detail, i: number) => (
                      <a-menu-item
                        on-click={() => this.addCondition(conditonRelation.childRelations as [], i + 1)}
                      >
                        {
                          `过滤条件序号:${i + 1}`
                        }
                      </a-menu-item>
                    ))
                  }
                </a-menu>
              </a-dropdown>
            ) : null
          }
          <div>
            <a-icon type='plus-circle' style='color:#1890ff'
              on-click={() => this.addGroup(conditonRelation.childRelations as [])}></a-icon>
          </div>
          {
            parentRelationIndex > -1 ? (
              <div>
                <a-icon type='delete' style='color:#1890ff'
                  on-click={() => this.delGroup(chiliren, parentRelationIndex)}></a-icon>
              </div>
            ) : null
          }
        </div>
      </div>
    );
  }

  render() {
    return (
      <div>
        <a-form form={this.form}>
          <a-row>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.fields.code')} labelCol={{ span: 6 }} wrapperCol={{ span: 15 }}>
                <a-input
                  placeholder={this.$l.getLocale(['controls.input', 'platform.fields.code'])}
                  on-change={(e: any) => { this.currentPermission.code = e.target.value; }}
                  v-decorator={[
                    'code',
                    {
                      initialValue: this.currentPermission.code,
                      rules: [{
                        required: true,
                        message: 'data required'
                      }]
                    }]}
                ></a-input>
              </a-form-item>
            </a-col>
            {
              this.currentPermission.permissionId ?
                <a-col span='12'>
                  <a-form-item label={this.$t('platform.fields.status')} labelCol={{ span: 6 }} wrapperCol={{ span: 15 }}>
                    <a-select
                      options={this.statusDataset}
                      placeholder={this.$l.getLocale(['controls.select', 'platform.fields.status'])}
                      style='width:100%;'
                      on-change={(value: any) => { this.currentPermission.status = value; }}
                      v-decorator={[
                        'status',
                        {
                          initialValue: this.currentPermission.status,
                          rules: [{
                            required: true,
                            message: 'data required'
                          }]
                        }]}
                    >
                    </a-select>
                  </a-form-item>
                </a-col>
                :
                null
            }
          </a-row>
          <a-row>
            <a-col span='24'>
              <a-form-item label={this.$t('platform.dataPermission.descripton')} labelCol={{ span: 3 }} wrapperCol={{ span: 21 }}>
                <a-input
                  placeholder={this.$l.getLocale(['controls.input', 'platform.dataPermission.descripton'])}
                  on-change={(e: any) => { this.currentPermission.description = e.target.value; }}
                  v-decorator={[
                    'description',
                    {
                      initialValue: this.currentPermission.description,
                      rules: [{
                        required: true,
                        message: 'data required'
                      }]
                    }]}
                ></a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-card title={this.$t('platform.dataPermission.filterCondition')} class={styles.card} size='small'>
            <div class={styles.block} style='text-align:right;'>
              <a-button
                type='link'
                icon='plus-circle'
                on-click={this.addConditions}>
                {this.$t('platform.dataPermission.addCondition')}
              </a-button>
            </div>
            {
              this.conditions.map((condition: Detail, index: number) => (
                <div class={styles.block} style='display:flex;align-items:center;'>
                  <div class={styles.content} style='width:30px; margin-bottom: 8px; text-align: center;'>{index + 1}</div>
                  <div class={styles.content} style='width:150px;'>
                    <a-form-item>
                      <a-select
                        allowClear
                        showSearch
                        placeholder={this.$t('controls.select')}
                        style='width:100%;'
                        on-change={(value: any) => { condition.tableName = value; this.tableChaneg(condition, value); }}
                        v-decorator={[
                          'tableName_' + condition.rowKey,
                          {
                            initialValue: condition.tableName,
                            rules: [{
                              required: true,
                              message: 'data required'
                            }]
                          }]}
                      >
                        {
                          dataPermissionService.tables.map((table: PermissionTable) => (
                            <a-select-option value={table.tableName}>
                              {table.locales[this.currentLanguageCode]}
                            </a-select-option>
                          ))
                        }
                      </a-select>
                    </a-form-item>
                  </div>
                  <div class={styles.content} style='width:150px;'>
                    <a-form-item>
                      <a-select
                        allowClear
                        showSearch
                        placeholder={this.$t('controls.select')}
                        style='width:100%;'
                        on-change={(value: any) => { condition.columnName = value; }}
                        v-decorator={[
                          'columnName_' + condition.rowKey,
                          {
                            initialValue: condition.columnName,
                            rules: [{
                              required: true,
                              message: 'data required'
                            }]
                          }]}
                      >
                        {
                          (condition.columnOptions as PermissionColumn[]).map((column: PermissionColumn) => (
                            <a-select-option value={column.columnName}>
                              {column.locales[this.currentLanguageCode]}
                            </a-select-option>
                          ))
                        }
                      </a-select>
                    </a-form-item>
                  </div>
                  <div class={styles.content} style='width:100px;'>
                    <a-form-item>
                      <a-select
                        options={this.operators}
                        placeholder={this.$t('controls.select')}
                        style='width:100%;'
                        on-change={(value: any) => { condition.optionType = value; }}
                        v-decorator={[
                          'optionType_' + condition.rowKey,
                          {
                            initialValue: condition.optionType,
                            rules: [{
                              required: true,
                              message: 'data required'
                            }]
                          }]}
                      >
                      </a-select>
                    </a-form-item>
                  </div>
                  <div class={styles.content} style='width:100px;'>
                    <a-form-item>
                      <a-select
                        options={this.conditionTypes}
                        placeholder={this.$t('controls.select')}
                        style='width:100%;'
                        on-change={(value: any) => { condition.conditionType = value; condition.conditionValue = undefined; }}
                        v-decorator={[
                          'conditionType_' + condition.rowKey,
                          {
                            initialValue: condition.conditionType,
                            rules: [{
                              required: true,
                              message: 'data required'
                            }]
                          }]}
                      >
                      </a-select>
                    </a-form-item>
                  </div>
                  <div class={styles.content} style='width:220px;'>
                    <a-form-item>
                      {
                        condition.conditionType === 2 ?
                          <a-select
                            options={this.systemVariables}
                            placeholder={this.$t('controls.select')}
                            style='width:100%;'
                            on-change={(value: any) => { condition.conditionValue = value; }}
                            v-decorator={[
                              'conditionValue_' + condition.rowKey,
                              {
                                initialValue: condition.conditionValue,
                                rules: [{
                                  required: true,
                                  message: 'data required'
                                }]
                              }]}
                          >
                          </a-select>
                          :
                          <a-input
                            placeholder={this.$t('controls.input')}
                            style='width:100%;'
                            on-change={(e: any) => { condition.conditionValue = e.target.value; }}
                            v-decorator={[
                              'conditionValue_' + condition.rowKey,
                              {
                                initialValue: condition.conditionValue,
                                rules: [{
                                  required: true,
                                  message: 'data required'
                                }]
                              }]}
                          ></a-input>
                      }
                    </a-form-item>
                  </div>
                  <div class={styles.content} style='width:10px; margin-bottom: 8px; text-align: left;'>
                    {
                      index > 0 ?
                        <a-icon
                          type='delete'
                          style='color: red; cursor: pointer;'
                          on-click={() => this.deleteCondition(condition.rowKey)}
                        />
                        :
                        null
                    }
                  </div>
                </div>
              ))
            }
            <a-card
              title={this.$t('platform.dataPermission.conditionLogic')}
              class={styles.card}
              size='small'
            >
              <span>逻辑:{this.logicText}</span>
              {
                this.logic(this.conditions, this.conditonRelations, [], -1)
              }
            </a-card>
          </a-card>
        </a-form>
      </div>
    );
  }
}
