import { ValueLabelPair } from '@/PlatformStandard/common/defines';

export enum MenuTypeEnum {
  /**
   * 模块
   */
  module = 'M',

  /**
   * 页面
   */
  page = 'P',

  /**
   * 链接
   */
  link = 'L'
}

/**
 * 菜单组
 */
export interface MenuGroup {
  /**
   * key
   */
  key: string;
  /**
   * 名称
   */
  locales: { [key: string]: string };
  /**
   * 图标
   */
  icon: string;
  /**
   * 页面菜单
   */
  children: PageMenu[];
  /**
   * 页面功能
   */
  actionIds?: string[];
  /**
   * 是否隐藏
   */
  hidden: boolean;
}

/**
 * 页面菜单
 */
export interface PageMenu {
  /**
   * key
   */
  key: string;
  /**
   * 页面名称
   */
  locales: { [key: string]: string };
  /**
   * 页面路由
   */
  route: string;
  /**
   * 是否隐藏
   */
  hidden: boolean;
  /**
   * 按钮权限
   */
  actionIds: string[];
  /**
   * 数据权限
   */
  permissionId: string;
}

/**
 * 面包屑项
 */
export interface BreadcrumbItem {
  link: string;
  locales: { [key: string]: string };
}

/**
 * 菜单视图状态
 */
export enum MenuViewState {
  /**
   * 展开的
   */
  Expanded = 'expanded',
  /**
   * 收起的
   */
  Collapsed = 'collapsed',
}

/**
 * 页面按钮数据类
 */
export interface PageActionDataDto {
  [key: string]: ValueLabelPair[];
}

export enum ActionEnum {
  /**
   * 添加
   */
  add = 'add',
  /**
   * 删除
   */
  delete = 'delete',
  /**
   * 编辑/修改
   */
  edit = 'edit',
  /**
   * 查询
   */
  search = 'search',
  /**
   * 导出
   */
  export = 'export',
  /**
   * 导入
   */
  import = 'import',
  /**
   * 查看/读取
   */
  read = 'read',
  /**
   * 保存
   */
  save = 'save',
  /**
   * 角色成员
   */
  userIAM = 'user-iam',
  /**
   * 菜单权限
   */
  menuIAM = 'menu-iam',
  /**
   * 数据权限
   */
  dataIAM = 'data-iam',
  /**
   * 上移
   */
  up = 'up',
  /**
   * 下移
   */
  down = 'down',
  /**
   * 批量作废
   */
  batchVoid = 'batch-void',
  /**
   * 批量抄送
   */
  batchcc = 'batch-cc',
  /**
   * 流程干预
   */
  processIntervention = 'process-intervention',
  /**
   * 步骤人员干预
   */
  intervStepUser = 'interv-step-user',
  /**
   * 表单数据干预
   */
  intervFormData = 'interv-form-data',
  /**
   * 审批意见干预
   */
  intervApprovalComments = 'interv-approval-comments',
  /**
   * 干预记录查看
   */
  interventionRecord = 'intervention-record',
  /**
   * 流程信息查看
   */
  processInfo = 'process-info',
  /**
   * 批量导入
   */
  batchImport = 'batch-import',
  /**
   * 批量导出
   */
  batchExport = 'batch-export',
  /**
   * 导入权责表
   */
  imortAuthorization = 'imort-authorization',
  /**
   * 导入链接/继承
   */
  imortOther = 'imort-other',
  /**
   * 导出权责表
   */
  exportAuthorization = 'export-authorization',
  /**
   * 新建流程
   */
  createProcess = 'create-process',
  /**
   * 批量组织授权
   */
  batchAuth = 'batch-auth',
  /**
   * 批量设置无效
   */
  batchInvalid = 'batch-invalid',
  /**
   * 发布
   */
  publish = 'publish',
  /**
   * 批量发布
   */
  batchPublish = 'batch-publish',
  /**
   * 导入链接/继承
   */
  batchImportOther = 'batch-import-other',
}
