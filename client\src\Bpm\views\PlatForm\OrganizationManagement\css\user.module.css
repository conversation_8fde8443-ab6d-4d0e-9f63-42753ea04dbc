/*
变量参考 https://github.com/vueComponent/ant-design-vue/blob/master/components/style/themes/default.less
或 https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/components/style/themes/default.less
*/
/* 重写 AntDesign 的主题样式 */
/* 重写 AntDesign 结束 */
/* 自定义相关 */
/* 基础边距 */
.card_top {
  margin-bottom: 10px !important;
  padding: 0px 0px 0 0px !important;
}
.card_top :global(.ant-card-body) {
  padding: 0px 10px 0 10px !important;
}
.card_top :global(.ant-card-body) :global(.ant-layout) {
  padding-bottom: 0 !important;
}
.card_top :global(.ant-form-item-label) {
  line-height: 20px !important;
}
.row_outer {
  background-color: #f8f8f8;
  margin: 8px 0px !important;
}
.rowNew {
  margin: 0;
  padding: 0px;
}
.operation {
  text-align: right;
  cursor: pointer;
}
.status_select {
  width: 100%;
}
.anticon {
  margin-left: 5px;
}
.base_table :global(.ant-card-body) {
  padding: 0 !important;
}
.base_table :global(.ant-table .ant-table-body) {
  margin: 0 !important;
}
.list_button {
  padding: 0 3px !important;
}
.common_btn {
  padding: 0;
  margin: 0;
  width: 70px !important;
  height: 30px;
  text-align: center;
  line-height: 32px;
  border-radius: 5px !important;
  border: 1px solid #ddd !important;
  background-color: #fff !important;
  color: #333 !important;
  opacity: 1;
  text-shadow: none !important;
  box-shadow: none !important;
}
.org_title {
  cursor: default;
  display: inline;
  padding-right: 4px;
  font-weight: normal;
}
.org_title i {
  color: #bfbfbf;
  margin-left: 2px;
}
