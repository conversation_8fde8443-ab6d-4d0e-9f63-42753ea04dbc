import { Component, Prop, Vue } from 'vue-property-decorator';
import { DataAuthorityEntity } from '../types';
import { WrappedFormUtils } from 'ant-design-vue/types/form/form';
import { formHelper, i18nHelper } from '@/PlatformStandard/common/utils';
import { languageService } from '@/PlatformStandard/services/language';

@Component
export class DataAuthorityEdit extends Vue {
    private form!: WrappedFormUtils;
    private currentDataAuthority!: DataAuthorityEntity;
    @Prop() selectDataAuthority!: DataAuthorityEntity;
    @Prop() parentDataAuthority!: DataAuthorityEntity;
    private statusDataset = i18nHelper.getLocaleObject('platform.dataPermission.statusDataset');
    getDataAuthorityInfo(): DataAuthorityEntity {
        return this.currentDataAuthority;
    }
    validateForm(): string[] {
        return formHelper.validateForm(this.form);
    }
    created() {
        this.form = this.$form.createForm(this, { name: 'dataAuthorityForm' });
        languageService.language$.subscribe(lang => {
            this.statusDataset = i18nHelper.getLocaleObject('platform.dataPermission.statusDataset');
        });
        this.currentDataAuthority = JSON.parse(JSON.stringify(this.selectDataAuthority));
    }
    render() {
        return (
            <div>
                <a-form form={this.form} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
                    <a-form-item label={this.$t('platform.fields.name')} required>
                        <a-input
                            on-change={(v: any) => this.currentDataAuthority.dataAuthorityName = v.target.value}
                            placeholder={this.$t('platform.fields.name')}
                            v-decorator={[
                                'name',
                                {
                                    initialValue: this.currentDataAuthority.dataAuthorityName,
                                    rules: [
                                        {
                                            required: true,
                                            message: this.$l.getLocale(['controls.input', 'platform.fields.name']),
                                        },
                                    ],
                                },
                            ]}></a-input>
                    </a-form-item>
                    <a-form-item label={this.$t('platform.fields.inUse')}>
                        <a-select v-model={this.currentDataAuthority.status}>
                            {this.statusDataset.map((item: any) => (
                                <a-select-option value={item.value}>{item.label}</a-select-option>
                            ))}
                        </a-select>
                    </a-form-item>
                    <a-form-item label={this.$t('platform.fields.description')}>
                        <a-textarea v-model={this.currentDataAuthority.remark}
                            placeholder={this.$l.getLocale(['controls.select', 'platform.fields.description'])}></a-textarea>
                    </a-form-item>
                </a-form>
            </div>
        );
    }
}
