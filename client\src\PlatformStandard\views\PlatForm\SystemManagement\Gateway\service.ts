import { Observable } from 'rxjs';

import { httpHelper } from '@/PlatformStandard/common/utils';

class CommGatewayService {
  delGateways(name: string): Observable<any> {
    const _url = `/api/platform/v1/gateway-services?name=${name}`;
    return httpHelper.delete(_url);
  }
  saveGateways(data: any): Observable<any> {
    const _url = `/api/platform/v1/gateway-services`;
    return httpHelper.post(_url, data);
  }
    /**
     * 获取网关列表
     * @param params 查询参数
     */
    getGateways(params: any): Observable<any> {
        const _url = '/api/platform/v1/gateway-services';
        return httpHelper.get(_url, { params });
    }
}

export const commGatewayService = new CommGatewayService();
