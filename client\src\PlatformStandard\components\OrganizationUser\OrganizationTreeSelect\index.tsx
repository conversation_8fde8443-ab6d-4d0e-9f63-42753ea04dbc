import { Component, Vue, Prop, Emit, Watch } from 'vue-property-decorator';

import styles from './component.organization-tree-select.module.less';
import { organizationUserService } from '../service';
import { TreeItem, OrganizationTreeSelectDto } from '../types';

@Component
export class OrganizationTreeSelect extends Vue {
  @Prop({ default: undefined }) value!: string[] | string | OrganizationTreeSelectDto[] | undefined;
  @Prop({ default: true }) multiple!: boolean;
  @Prop({ default: true }) allowClear!: boolean;
  @Prop({ default: false }) required!: boolean;
  @Prop({ default: true }) formItem!: boolean;

  private data: TreeItem[] = [];
  private defaultValue: string[] | string | OrganizationTreeSelectDto[] | undefined;

  @Emit('change')
  private onChange(value: string[] | string) {
    if (!value || value.length === 0 || value === '') {
      this.defaultValue = undefined;
      this.getData().subscribe((data: TreeItem[]) => {
        this.data = data;
      });
    } /* else {
      this.defaultValue = value;
    } */
  }

  @Emit('node-change')
  private onNodeChange(items: OrganizationTreeSelectDto[]) { }

  private onLoad(treeNode: any) {
    const { id, value } = treeNode.dataRef;
    return new Promise<void>(resolve => {
      this.getData(id, value).subscribe((data: TreeItem[]) => {
        this.data = this.data.concat(data);
        resolve();
      });
    });
  }

  private getData(id?: string, value?: string) {
    if (this.defaultValue && this.defaultValue !== '') {
      return organizationUserService.searchOrganizations(String(this.defaultValue), id);
    } else {
      return organizationUserService.getOrganizationsTree(id, value);
    }
  }

  created(): void {
    this.defaultValue = this.value;
    console.log(`🚀 ~ this.value`, this.value);
    this.getData().subscribe((data: TreeItem[]) => {
      this.data = data;
    });
  }

  render() {
    return (
      this.formItem ?
        <a-form-item label={this.$t('platform.organization.org')} required={this.required}>
          <a-tree-select
            multiple={this.multiple}
            style={'width:100%'}
            placeholder={this.$l.getLocale(['controls.select', 'platform.fields.organization'])}
            value={this.value instanceof Array ? (this.value as OrganizationTreeSelectDto[]).map(m => m.value) : this.value}
            on-change={(value: string | string[], label: any, extra: any) => {
              this.onChange(value);
              this.onNodeChange(extra.triggerNode ? [extra.triggerNode.dataRef.datum] : []);
            }}
            allow-clear={this.allowClear}
            max-tag-count={1}
            dropdown-style={{ 'max-height': '300px' }}
            tree-data-simple-mode
            tree-node-filter-prop='title'
            tree-data={this.data}
            load-data={this.onLoad}
          >
          </a-tree-select>
        </a-form-item>
        :
        <a-tree-select
          multiple={this.multiple}
          style={'width:100%'}
          placeholder={this.$l.getLocale(['controls.select', 'platform.fields.organization'])}
          value={this.value instanceof Array ? (this.value as OrganizationTreeSelectDto[]).map(m => m.value) : this.value}
          on-change={(value: string | string[], label: any, extra: any) => {
            this.onChange(value);
            this.onNodeChange(extra.triggerNode ? [extra.triggerNode.dataRef.datum] : []);
          }}
          allow-clear={this.allowClear}
          max-tag-count={1}
          dropdown-style={{ 'max-height': '300px' }}
          tree-data-simple-mode
          tree-node-filter-prop='title'
          tree-data={this.data}
          load-data={this.onLoad}
        >
        </a-tree-select>
    );
  }
}
