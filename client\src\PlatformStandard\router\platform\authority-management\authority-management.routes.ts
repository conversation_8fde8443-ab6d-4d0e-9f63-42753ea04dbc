import { RouteConfig } from 'vue-router';
import { DataAuthority } from '@/PlatformStandard/views/PlatForm/AuthorityManagement/DataAuthority';
import { Configuration } from '@/PlatformStandard/views/PlatForm/AuthorityManagement/DataAuthority/Configuration';
import { Roles } from '@/PlatformStandard/views/PlatForm/AuthorityManagement/DataAuthority/Role';

export const AuthorityManagementRoutes: RouteConfig[] = [
  {
    path: 'data-authority',
    component: DataAuthority,
  },
  {
    path: 'data-authority/role',
    component: Roles,
  },
  {
    path: 'data-authority/config',
    component: Configuration,
  }
];
