@import '../../../../../themes/default/variables.less';

:global(.ant-tabs-top-bar){
  border: 0px solid #ddd !important;
}
:global(.ant-tabs-bar) {
  background: linear-gradient(180deg,#f8fafc, #eef3f7);
  margin: 0 !important;
}
:global(.ant-tabs-tab) {
  margin: 0 !important;
  font-size: 14px !important;
  font-weight: bold !important;
  color: #333;
}
:global(.ant-tabs-tab-active){
  background: #fff;
}
:global(.ant-tabs-ink-bar){
  top: 0;
}
:global(.ant-tabs-nav-wrap){
  padding: 0 20px;
}
.cardTitle{
  position: relative;
  padding: 8px 8px !important;
  box-sizing: border-box;
  font-size: 16px;
  // height: 40px;
  background: #f4f7fa;
  border-radius: 4px;
  width: 100%;
}
.cardTitle:before{
  content: '';
  position: absolute;
  height: 16px;
  width: 4px;
  background-color: @primary-color;
  top: 12px;
  left: 10px;
}
.cardTitle font{
  font-weight: bold;
  padding-left: 15px;
  color: #333;
}
.colorSpan{
  width:35px;
  height:15px;
  margin:7px 10px;
  display:inline-block;
}