@import '../../themes/default/variables.less';
@import './main-layout-base.less';

@avatar-size: 24px;
body{
  background: rgb(240, 242, 245) !important;
}
:global(.ant-card-head){
  min-height: 35px !important;
  border: 0px solid #ddd !important;
}
.main {
  // height: 100vh;
  min-width: 1200px;

  .header {
    display: flex;
    background-color: @title-color;
    align-items: center;
    box-shadow: 0 2px 5px fade(@menu-dark-bg, 30%);
    // padding: 0 @base-size * 2;
    z-index: @zindex-above-float;
  
    .logo {
      color: @heading-color;
      display: flex;
      padding-left: @base-size * 2;
      align-items: flex-end;
      background: #2C3340;
 
      img {
        width: @logo-width;
      }

      :global(.ant-divider-vertical) {
        height: 1em;
      }

      span {
        display: inline-block;
        padding-top: 1px;
        height: @font-size-lg;
        line-height: @font-size-lg;
        font-size: @font-size-lg + 2px;
        font-weight: @font-weight-bold;
      }
    }

    .blank {
      flex: 1 1 auto;
    }

    .top_menu {
      flex: 1 1 auto;
      background: transparent;
      font-size: @font-size-lg;
      // margin-left: @top-menu-margin-left;
    }

    .actions {
      cursor: default;
      color: @white-color;
      padding-right: @base-size * 2;
      display: flex;
      align-items: center;

      a {
        color: @white-color;
      }

      button {
        color: @white-color;
        padding: 0;
      }

      .avatar,
      .impersonate_avatar {
        display: flex;
        align-items: center;
      }

      .impersonate_avatar {
        padding: 0 8px;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        margin-right: 1px;
      }

      .impersonate_exit {
        padding: 0 8px;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }

      :global(.ant-avatar) {
        width: @avatar-size*1.4;
        height: @avatar-size*1.4;
        background: @avator-color;
      }

      .user_info {
        display: flex;
        align-items: center;
        padding: 0 12px;
      }
    }
  }

  .menu_container {
    // width: @menu-expanded-width !important;
    // flex: 0 0 @menu-expanded-width !important;
    // max-width: @menu-expanded-width !important;
    // min-width: @menu-expanded-width !important;
    :global(.ant-layout-sider-children) {
      @supports (grid-area: auto) {
        height: calc(~'100vh' - @layout-header-height);
      }
    }

    &,
    ul,
    li,
    :global(.ant-layout-sider-trigger) {
      // 同步菜单各个元素的动画时间
      transition-duration: 0.2s !important;
      // background: #000C24;
      // height: 28px !important;
      // line-height: 28px !important;
    }
    :global(.ant-layout-sider .ant-layout-sider-trigger){
      // background: #000C24;
    }
   
    &{
      :global(.ant-menu.ant-menu-dark ){
        padding-bottom: 80px !important;
      }
    }
    :global(.menu_container > ul){
      padding-bottom: 80px !important;
    }
    ul,
    li {
      // 避免宽度不一致导致的动画时差
      width: 100% !important;
    }
  }

  .menu {
    // height: 100%;
    height: calc(100vh - 20px) !important;
    overflow-x: hidden;
    overflow-y: auto;
    &::-webkit-scrollbar {
      width: 5px;
      height: 1px;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 5px;
      // background: #eee;
    }
    &::-webkit-scrollbar-track {
      border-radius: 5px;
      background: rgba(0,0,0, 0.1);
    }
    .subMenu {
      font-size: @font-size-lg;
    }

    a {
      color: @menu-dark-color;

      :global(.anticon) {
        font-size: @font-size-lg;
      }
    }

    :global(.ant-badge sup) {
      box-shadow: none;
      margin-left: @base-size * 3;
      margin-top: -4px;
      transition: all ease 0.2s;
    }
    :global(.ant-drawer-content-wrapper){
      width:200px !important;
    }
    :global(.ant-drawer-body) {
      font-size: 16px !important;
    }
    &.collapsed {
      :global(.ant-badge sup) {
        margin-left: -6px;
        margin-top: 0 - @base-size * 2 - 4;
      }
    }
  }
  .menu:hover {
    &::-webkit-scrollbar-thumb {
      background: #eee;
    }
  }
  :global(.ant-drawer-body) {
    font-size: 16px !important;
  }
  .tabSystem{
    border-top: 1px solid #343D4C;
  }
  .tabSystem:hover{
    background: #182E5A !important;
  }
  .tabSystem,.tabSystemPop{
    // position: relative;
    text-align: right;
    padding: 20px 30px 20px 30px;
    width: 100%;
    font-size: 16px;
    // background: rgb(44, 51, 64);
    background: #172748;
    margin-top: 1px;
    color: #fff;
    cursor: pointer;
    overflow: auto;
    .sysbtn{
      // float: left;
      width: 20px;
      margin-left: 0px;
      background-color: none;
      border: 0;
      background: url(../../assets/images/leftnav.png) no-repeat;
      background-size: 20px 20px;
      margin-top: 2px;
      height: 20px;
      display: inline-block;
      text-align: right;
      padding: 0 !important;
    }
  }
  .tabSystem > font{
    // padding-right: 58px;
    float: left;
  }
  .top_bar {
    // padding: 0px 16px;
    padding: 0px 0 4px 0 !important;
    border-bottom: 0px #e8e8e8 solid !important;
    // box-shadow: 0px 1px 4px 1px rgb(0,131,255);
    // background: #fff;
    height: 38px;
    line-height: 38px;
    // background: #fff !important;
    :global(.ant-tabs-tab){
      padding: 0 !important;
    }
  }

  .top_bar > a {
    display: inline-block;
    margin-right: 10px;
  }

  .breadcrumb {
    display: inline-block;
  }

  .top_bar .menu-fold {
    font-size: 14px;
  }

  .main_content {
    overflow: hidden;
  }

  .content_container {
    background-color: @modal-footer-bg;
    padding: @base-size @base-size;
    // overflow-x: auto;
    // overflow: hidden;
    box-sizing: border-box;
  }

}

.helper_dropdown > li {
  text-align: center !important;
}

@supports (grid-area: auto) {
  .main .menu_container :global(.ant-layout-sider-children) {
    height: calc(100vh - 68px);
  }
}
:global(.ant-popover){
  // left:15% !important;
  :global(.ant-popover-arrow){
    color: #172748;
  }
  :global(.ant-popover-inner){
    color: #fff !important;
    background: #172748;
  }
  :global(.ant-popover-inner-content){
    color: #B1B3B6 !important;
    padding: 12px 0px 1px 0px;
    p{
      cursor: pointer;
      padding: 0 16px;
    }
  }
  :global(.ant-popover-inner-content p:hover){
    color: #fff !important;
    background: #182E5A !important;
  }
  .sysIcon{
    width: 30%;
    margin-top: -5px;
  }
}
.sysPop{
  // background-color: red;
  left: 80px !important
}
.sysPopZhan{
  left: 200px !important
}

.aboutLink{
  z-index:100;
  // background: rgb(44, 51, 64);
  background: #F0F2F5;
  color:#999;
  width: calc(~'100%' - 200px); 
  padding:5px;text-align:left;
  position:fixed;
  bottom:0;
  right:0;
  text-align: center;
  a{
    color:#999;
  }
}
.aboutLinkMore{
  z-index:100;
  background: #F0F2F5;
  color:#999;
  width: calc(~'100%' - 80px); 
  padding:5px;text-align:left;
  position:fixed;
  bottom:0;
  right:0;
  text-align: center;
  a{
    color:#999;
  }
}
:global(.ant-menu-sub){
  background:#061739 !important;
  box-shadow:none !important;
  -webkit-box-shadow: none !important;
}
:global(.ant-menu-sub .ant-menu-item:hover){
  background: #2165d9 !important;
}
:global(.ant-menu li.ant-menu-submenu){
  margin-top: -5px;
}
:global(.ant-menu li.ant-menu-submenu:hover){
  background: #182E5A !important;
}
:global(.ant-popover-placement-rightTop > .ant-popover-content > .ant-popover-arrow){
  border-bottom-color: #172748 !important;
  border-left-color: #172748 !important;
}
:global(.ant-popover-placement-right > .ant-popover-content > .ant-popover-arrow){
  // border-bottom-color: red !important;
  // border-left-color: red !important;
}
:global(.ant-popover-placement-rightBottom > .ant-popover-content > .ant-popover-arrow){
  // border-bottom-color: 1px solid red !important;
  // border-left-color: 1px solid red !important;
}
:global(.ant-table .ant-table-body){
  margin: 0 !important;
}
.card_top{
  margin-bottom:8px !important;
  // padding: 10px 10px 0 10px !important;
  padding: 0px 0px 0 0px !important;
  :global(.ant-card-body){
    padding: 0px 10px 0 10px !important;
    // padding: 0px 0px 0 0px !important;
    :global(.ant-layout){
      padding-bottom:0 !important
    }
  }
  :global(.ant-form-item-label){
    // line-height: 20px !important;
  }
}
:global(.ant-layout-sider-dark .ant-layout-sider-trigger){
  background: #0c1f46;
}
.linkAbout{
  color: #333;
}
.linkAbout:hover{
  i{
    color: #2165d9;
  }
  color: #2165d9;
}
