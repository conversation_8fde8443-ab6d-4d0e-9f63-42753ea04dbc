export interface OrganizationTree {
  code: string;
  value: string;
  label: string;
  childCount: number;
}

export interface OrganizationDto {
  organizationId?: string;
  name?: string;
  organizationCode?: string;
  upperId?: string;
  upperName?: string;
  manager?: string;
  managerName?: string;
  telephone?: string;
  level?: number;
  status?: number;
  remark?: string;
  sortCode?: number;
}

export interface UserPositionQueryDto {
  organizationId?: string;
  orgInfo?: string;
  positionInfo?: string;
  userInfo?: string;
}

export interface UserPositionDto {
  organizationId?:   string;
  organizationCode?: string;
  organizationName?: string;
  positionId?:       string;
  positionCode?:     string;
  positionName?:     string;
  primaryPosition?:  boolean;
  userId?:           string;
  userName?:         string;
}
