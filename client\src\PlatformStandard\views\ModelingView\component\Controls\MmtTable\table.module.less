.div{
    position: relative;
    border: 1px dashed #999;
    padding: 3px;
    height: 120px;
}
.content{
    position: relative;
    height: 110px;
    overflow: auto;
}
.system_column{
    height: 90px;
    width: 50px;
    text-align: center;
    float: left;
    border: 1px solid #999;
}
.custom_column{
    height: 90px;
    border: 1px solid #999;
    margin: 0 2px;
    position: relative;
    cursor: pointer;
    overflow: hidden;
}
.column_header{
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #999;
}
.column_content{
    padding: 3px;
    height: 50px;
    line-height: 50px;
}
.draggable{
    float: left;
    height: 90px;
    position: absolute;
    display: flex;
}
.form_item_action {
    position: absolute;
    right: 0px;
    bottom: 0px;
    height: 20px;
    line-height: 20px;
    background: #409eff;
    z-index: 2;
    i {
      font-size: 14px;
      color: #fff;
      margin: 0 5px;
      cursor: pointer;
    }
}
.form_item_drag {
    position: absolute;
    left: 0px;
    top: 0px;
    height: 20px;
    line-height: 20px;
    background: #409eff;
    z-index: 2;
    i {
      font-size: 14px;
      color: #fff;
      margin: 0 5px;
      cursor: move;
    }
}
.column_item_action {
    position: absolute;
    right: 0px;
    top: 0px;
    height: 20px;
    line-height: 20px;
    background: #409eff;
    z-index: 2;
    i {
      font-size: 14px;
      color: #fff;
      margin: 0 5px;
      cursor: pointer;
    }
}
.ghost {
    background: #f56c6c;
    border: 1px solid #f56c6c;
    outline-width: 0;
    width: 3px;
    height: 90px;
    box-sizing: border-box;
    font-size: 0;
    content: "";
    overflow: hidden;
    padding: 0;
}
.active {
    border: 1px solid #409eff !important;
}
