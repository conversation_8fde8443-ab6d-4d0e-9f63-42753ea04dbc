@import '../../../../../themes/default/variables.less';

.row_outer{
    background-color: @content-bg-1;
    margin: @base-size 0px !important;
}

.row{
  
    padding: @content-padding-size;
}

.operation{
    text-align: right;
    cursor: pointer;
}

.status_select{
    width: 100%;
}

.anticon{
    margin-left: 5px;
}

.base_table {
    :global(.ant-card-body) {
      padding: 0;
    }
  }

.list_button {
    padding: 0 3px !important
}

.org_title {
    cursor: default;
    display: inline;
    padding-right: @base-size - 4px;
    font-weight: normal;
    &:extend(.parent_title);
  
    i {
      color: @text-color-desc;
      margin-left: 2px;
    }
}
  