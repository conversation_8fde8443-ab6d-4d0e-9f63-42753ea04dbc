import { Component, Vue } from 'vue-property-decorator';
import { holidayService } from './services';
import styles from './holiday.module.less';
import moment from 'moment';
import { dateHelper, i18nHelper, notificationHelper, removeNullValueProperty } from '@/PlatformStandard/common/utils';
import { HolidayEntity } from './types';
import {EditHoliday} from './Edit';

@Component({
  components: {EditHoliday}
})
export class Holiday extends Vue {
  private loading = false;
  private currDate = new Date();
  private year = this.currDate.getFullYear();
  private month = this.currDate.getMonth() + 1;
  private listHoliday: any[] = [];
  private fieldsSlotMap: any = {};
  private editHoliday: HolidayEntity = {};
  private currDateStr = '';
  private uploadShow = true;
  private fileList: any[] = [];
  private onPanelChange(value: any, mode: any) {
    this.year = value.year();
    this.month = value.month() + 1;
    this.load();
  }
  // 重载数据
  private load(): void {
    this.loading = true;
    const params: any = {
        year: this.year,
        month: this.month
    };
    holidayService.getHolidays(removeNullValueProperty(params)).subscribe(data => {
        this.listHoliday = data;
        this.loading = false;
    });
  }
  private add(date: string) {
    this.editHoliday = {
        holidayDate : date,
        status: 1
    };
    this.openEditHoliday();
  }
  private edit(holiday: any) {
    this.editHoliday = holiday;
    this.openEditHoliday();
  }
  private export() {
    const dto: any = {
      year: this.year
  };
    const fileName = i18nHelper.getLocale('platform.holiday.maintain') + dateHelper.formatDate(new Date(), 'YYYYMMDDHHmmss');
    return holidayService.export(dto, fileName);
  }
  openEditHoliday() {
    (this.$refs.EditHoliday as any).openForm(this.editHoliday);
  }
  holidaySave(holiday: any) {
    holidayService.updateHoliday(holiday).subscribe(data => {
      notificationHelper.success(i18nHelper.getLocale('messages.success'));
      this.load();
    });
  }
  handleRemove(file: any) {
    const index = this.fileList.indexOf(file);
    const newFileList = this.fileList.slice();
    newFileList.splice(index, 1);
    this.fileList = newFileList;
  }
  beforeUpload(file: any) {
      this.fileList = [file];
      this.handleUpload();
      return false;
  }
  handleUpload() {
      const formData = new FormData();
      formData.append('file', this.fileList[0]);
      holidayService.upload(formData).subscribe(s => {
          if (s.code === 1) {
            notificationHelper.success(i18nHelper.getLocale('messages.success'));
            this.load();
          } else {
            notificationHelper.success(i18nHelper.getLocale('messages.fail'));
          }
      });
  }
  created() {
    this.currDateStr = moment(Date.now()).format('YYYY-MM-DD');
    this.editHoliday = {
      holidayDate: this.currDateStr,
      status: 1,
      remark: ''
    };
    this.load();
    this.fieldsSlotMap['dateFullCellRender'] = (date: any) => {
      const dateStr = date.format('YYYY-MM-DD');
      const dayOfWeek  = new Date(date).getDay();
      const hlDate = this.listHoliday.find(f => f.holidayDate === dateStr);
      let cssHoliday = 'ant-fullcalendar-date';
      let tagColor = '#0067d8';
      if (hlDate && hlDate.status === 1) {
        cssHoliday = 'ant-fullcalendar-date holiday';
        tagColor = '#c1c1c1';
      }
      return (
          <div>
              {
                hlDate ?
                <div class={cssHoliday}>
                  <div class='ant-fullcalendar-value'>{date.format('DD')}</div>
                  <div class='ant-fullcalendar-content'>
                  <a-row>
                    <a-icon type='edit' on-click={() => this.edit(hlDate)}/>
                    <a-col span={24}>
                      {hlDate.remark ?
                        <a-tag color={tagColor}  on-click={() => this.edit(hlDate)}>
                          {hlDate.remark}
                        </a-tag>
                        : null
                      }
                      </a-col>
                  </a-row>
                  </div>
                </div>
                :
                <div class='ant-fullcalendar-date'>
                  <div class='ant-fullcalendar-value'>
                    {date.format('DD')}
                  </div>
                  <div class='ant-fullcalendar-content'>
                    <a-row>
                      <a-icon type='edit' on-click={() => this.add(dateStr)}/>
                    </a-row>
                  </div>
                </div>
              }
          </div>
      );
    };
    this.fieldsSlotMap['headerRender'] = (object: any) => {
      const year = object.value.year();
      const options = [];
      for (let i = year - 10; i < year + 10; i += 1) {
        options.push(
          <a-select-option key={i} value={i} class='year-item'>
            {i + '年'}
          </a-select-option>
        );
      }
      const start = 0;
      const end = 12;
      const monthOptions = [];

      const current = object.value.clone();
      const localeData = object.value.localeData();
      const months = [];
      for (let i = 0; i < 12; i++) {
        current.month(i);
        months.push(localeData.monthsShort(current));
      }

      for (let index = start; index < end; index++) {
        monthOptions.push(
          <a-select-option class='month-item' key={`${index}`}>
            {months[index]}
          </a-select-option>
        );
      }
      const month = object.value.month();
      return (
        <div style={{ padding: '10px' }} class='ant-fullcalendar-header'>
          <a-row type='flex' justify='space-between'>
            <a-col span='16'>
            </a-col>
            <a-col span='2' style={{display: 'flex'}}>
              <a-select
                size='default'
                dropdownMatchSelectWidth={false}
                class='my-year-select'
                onChange={(newYear: any) => {
                  const now = object.value.clone().year(newYear);
                  object.onChange(now);
                }}
                value={String(year) + '年'}
              >
                {options}
              </a-select>
              <a-select
                size='default'
                dropdownMatchSelectWidth={false}
                value={String(month)}
                onChange={(selectedMonth: any) => {
                  const newValue = object.value.clone();
                  newValue.month(parseInt(selectedMonth, 10));
                  object.onChange(newValue);
                }}
              >
                {monthOptions}
              </a-select>
            </a-col>
            <a-col  style={{display: 'flex'}}>
            <a-upload v-show={this.uploadShow}
                name={'file'}
                multiple={false}
                showUploadList={false}
                /* action={this.fileUrl} */
                accept={'.xls,.xlsx'}
                file-list={this.fileList}
                remove={this.handleRemove}
                before-upload={this.beforeUpload}
                locale={{ emptyText: '' }}
            >
                <a-button type='primary'>{this.$t('buttons.import')}</a-button>
            </a-upload>
              {/* <a-button type='primary' onClick={() => {}}>导入</a-button> */}
              <a-button type='primary' style='margin-left: 10px;' on-click={() => this.export()}>
                {this.$l.getLocale('buttons.export')}
              </a-button>
            </a-col>
          </a-row>
        </div>
      );
    };
  }
  render() {
    return (<div class={styles.test}>
        <a-calendar scopedSlots={this.fieldsSlotMap} on-panelChange={this.onPanelChange}>
        </a-calendar>
        <edit-holiday parentObj={this} data={this.editHoliday} ref='EditHoliday'></edit-holiday>
    </div>);
  }
}
