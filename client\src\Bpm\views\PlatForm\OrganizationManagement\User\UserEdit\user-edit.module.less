@import '../../../../../../themes/default/variables.less';

.list_button {
  padding: 0 3px !important
} 

.row_outer{
  background-color: @content-bg-1;
  margin: @base-size 0px !important;
}
.row{
  // padding: @content-padding-size;
  margin-bottom: 10px;
}
.operation{
  text-align: right;
  cursor: pointer;
  // margin: 0 !important;
}

.org_title {
  cursor: default;
  display: inline;
  padding-right: @base-size - 4px;
  font-weight: normal;
  &:extend(.parent_title);

  i {
    color: @text-color-desc;
    margin-left: 2px;
  }
}

:global(.cardBorderTop) {
  border-top: 1px solid #ddd !important;
  padding-top: 10px;
  margin-top: -10px !important;
}