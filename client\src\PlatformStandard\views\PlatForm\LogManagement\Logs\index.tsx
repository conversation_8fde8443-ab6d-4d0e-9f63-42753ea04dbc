import { Component, Vue } from 'vue-property-decorator';
import { CompCard } from '@/PlatformStandard/components';
import styles from './Logs.module.less';
import { LogDto } from './types';
import { logService } from './services';
import { notification } from 'ant-design-vue';

@Component({ components: { CompCard } })
export class Logs extends Vue {
  private data: LogDto[] = []; /// 列表加载
  private fieldsSlotMap: any = {};
  private isShowHighFilter = false;
  private keyCode = '';
  private kw = '';
  private loggerList: Array<{ label: string; value: string }> = [];
  private loggers = '';
  private stratTime = '';
  private endTime = '';

  private columns = [
    {
      dataIndex: 'level', slots: { title: 'level' } , width: '120px',
      // scopedSlots: { customRender: 'level' },
    },
    { dataIndex: 'logged', slots: { title: 'logged' }, width: '120px', },
    {
      dataIndex: 'logger', slots: { title: 'logger' }, width: '180px'
      // scopedSlots: { customRender: 'logger' },
      // width: '30%'
    },
    {
      dataIndex: 'message', slots: { title: 'message' },
      // scopedSlots: { customRender: 'action' },
      width: '30%'
    },
    { dataIndex: 'exception', slots: { title: 'exception' }, width: '380px', scopedSlots: { customRender: 'exception' }}
  ];

  private pagination = {
    total: 0,
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    size: 'small',
    showTotal: (total: string) =>
      this.$l
        .getLocale('paginations.total')
        .toString()
        .replace('{{value}}', total),
  };

  // 重载数据
  private load(pageIndex?: number): void {
    if (pageIndex) {
      this.pagination.current = pageIndex;
    }
    const params: any = {
      'kw': this.kw,
      'logger': this.loggers,
      'start-date': this.stratTime,
      'end-date': this.endTime,
      'page-size': this.pagination.pageSize,
      'page-index': this.pagination.current
    };
    logService.getLogs(params).subscribe(data => {
      this.data = data.items;
      this.pagination.total = data.total;
      this.$forceUpdate();
    });
  }
  // 查询
  private onSearch(): void {
    this.load(1);
  }

  // 重置
  private onReset(): void {
    this.kw = '';
    this.loggers = '';
    this.stratTime = '';
    this.endTime = '';
    this.load(1);
  }

  private getloggerList(): void {
    logService.getloggerList('').subscribe((data: Array<{ label: string; value: string; }>) => {
      this.loggerList = data;
    });
  }
  private handleChangeRole(e?: any): void {
    this.loggers = e;
  }
  private onChange(date: any, dateString: any) {
    this.stratTime = dateString[0];
    this.endTime = dateString[1];
    console.log(dateString[0] + '~' + dateString[1]);
  }
  private openNotification(text: any) {
    notification.open({
      message: '异常信息',
      description: text,
      duration: 0,
      style: 'max-height:300px;overflow:auto;min-width:200px;word-break: break-all;'
    });
  }
  created() {
    this.load(1);
    this.getloggerList();

    this.fieldsSlotMap['exception'] = (text: any, record: any, index: number) => {
      return (
        <div on-click={() => this.openNotification(text)} style='width: 360px;overflow:hidden;text-overflow: ellipsis;white-space: nowrap;cursor: pointer;'>{text}</div>
      );
    };
  }

  render() {
    return <div>
      <div>
      <comp-card class={styles.card_top}>
        <a-row class={styles.row} >
            <a-col span='6'>
              <a-form-item  label={this.$t('platform.fields.keyword')}>
                <a-input
                  style='width:90%'
                  v-model={this.kw} />
              </a-form-item>
            </a-col>
            <a-col span='6'>
              <a-form-item  label={this.$t('platform.log.module')}>
                <a-select style='width:80%' mode='multiple'
                  on-change={this.handleChangeRole}>
                  {
                    this.loggerList.map(item => (
                      <a-select-option value={item.value}>
                        {item.label}
                      </a-select-option>
                    ))
                  }
                  </a-select>
              </a-form-item>
            </a-col>
            <a-col span='6' >
              <a-form-item  label={this.$t('platform.log.time')}>
                <a-range-picker on-change={this.onChange}/>
              </a-form-item>
            </a-col>
            <a-col span='6' class={styles.button_top}>
              <a-button type='primary' class={styles.searchBtn} html-type='submit'
              on-click={this.onSearch}>{this.$l.getLocale('buttons.search')}
              </a-button>
              <a-button class={styles.common_btn} html-type='submit' on-click={this.onReset}>
                {this.$l.getLocale('buttons.reset')}</a-button>
            </a-col>
          </a-row>
      </comp-card>
      <comp-card>
              <a-table
              rowKey={(_record: any, index: any) => index}
              size='small'
                // rowKey='id'
                columns={this.columns}
                data-source={this.data}
                pagination={this.pagination}
                on-change={(pagination: any) => {
                  this.pagination = pagination;
                  this.load();
                }}
                scopedSlots={this.fieldsSlotMap}
              >
                <span slot='level'>{this.$t('platform.log.logType')}</span>
                <span slot='logged'>{this.$t('platform.log.time')}</span>
                <span slot='logger'>{this.$t('platform.log.module')}</span>
                <span slot='message'>{this.$t('platform.log.message')}</span>
                <span slot='exception'>{this.$t('platform.log.errorMessage')}</span>
              </a-table>
      </comp-card>
      </div>
    </div >;
  }
}
