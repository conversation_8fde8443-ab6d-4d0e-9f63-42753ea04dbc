import { Component, Vue } from 'vue-property-decorator';
import styles from './Menu.module.less';
import { ActionEnum, MenuTypeEnum } from '@/PlatformStandard/services/menu';
import { PutRoleMenu } from './types';
import { rightMenuService } from './service';
import { guid<PERSON><PERSON>per, i18n<PERSON><PERSON>per, notification<PERSON>elper } from '@/PlatformStandard/common/utils';
import { languageService } from '@/PlatformStandard/services/language';
import { DataPermission } from '../../DataPermission';
import { DataPermissionDto } from '../../DataPermission/types';
import { dataPermissionService } from '../../DataPermission/service';
import { SelectDepartment } from '@/PlatformStandard/components/SelectDepartment';

@Component(
  { components: { DataPermission, SelectDepartment } }
)
export class RightsMenu extends Vue {
  private actions = ActionEnum;
  private roleId = '';
  private tbData: any = [];
  private zdmbVisible = false;
  private currentRecord: any = null;
  private columns = [
    {
      dataIndex: 'name',
      key: 'name',
      slots: { title: 'name' },
      width: '30%'
    },
    {
      dataIndex: 'inUse',
      key: 'inUse',
      slots: { title: 'inUse' },
      scopedSlots: { customRender: 'inUse' },
      width: '5%'
    },
    {
      dataIndex: 'actionIds',
      key: 'actionIds',
      slots: { title: 'actionIds' },
      scopedSlots: { customRender: 'actionIds' },
      width: '30%'
    },
    {
      dataIndex: 'dataAuthority',
      key: 'dataAuthority',
      slots: { title: 'dataAuthority' },
      scopedSlots: { customRender: 'dataAuthority' },
      width: '30%'
    },
    /* {
      dataIndex: 'permissionIds',
      key: 'permissionIds',
      slots: { title: 'permissionIds' },
      scopedSlots: { customRender: 'permissionIds' }
    } */
  ];

  private inUseIndeterminate = true;
  private inUseCheckAll = false;
  private actionsIndeterminate = true;
  private actionsCheckAll = false;
  private fieldsSlotMap: any = {};

  private rowData: any = [];

  private permissionItems: DataPermissionDto[] = [];
  private dataPermissionShow = false;
  private dataAuthorityItems = [];
  private ontbDataChange() {
    if (this.tbData.length > 0) {
      this.tbData.forEach((item: any) => {
        this.refreshDataCache(item);
      });
      this.refreshStatus('inUse');
      this.refreshStatus('actionIds');
    }
  }

  private refreshDataCache(item: any) {
    item['_GUID'] = guidHelper.generate();
    item['_CHECK'] = false;
    item['_EXPANDDATA'] = this.treeConvertToList(item);
    return item;
  }

  private getPermissionItems() {
    dataPermissionService.getAllDataPermissions().subscribe(data => {
      this.permissionItems = data.items;
    });
  }

  /**
   * table-tree-行转子集list
   * @param root
   */
  private treeConvertToList(root: object): any[] {
    const stack: any = [];
    const array: any = [];
    const hashMap = {};
    stack.push({ ...root, _LEVEL: 0, expand: false });

    while (stack.length !== 0) {
      const node = stack.pop();
      this.treeVisitNode(node, hashMap, array);
      if (node.children) {
        for (let i = node.children.length - 1; i >= 0; i--) {
          node.children[i]._GUID = guidHelper.generate();
          node.children[i]._CHECK = false;
          stack.push({ ...node.children[i], _LEVEL: node._LEVEL + 1, expand: false, parent: node });
        }
      }
    }
    return array;
  }

  private treeVisitNode(node: any, hashMap: any, array: any[]): void {
    if (!hashMap[node._GUID]) {
      hashMap[node._GUID] = true;
      array.push(node);
    }
  }

  /**
   * 重置列选择状态
   * @param field 列选择-列属性
   */
  private refreshStatus(keyId: string) {
    let _inUseAllCheck = 0;
    let _inUseIndeterminate = 0;
    let _actionAllCheck = 0;
    let _actionIndeterminate = 0;
    const mtDataLength = this.tbData.length;

    // step 1，重置全选按钮的状态：全选、未选、部分选中
    this.tbData.forEach((f: any) => {
      const showCheckDatas = f._EXPANDDATA.filter((data: any) => data.type === 'P');
      if (keyId === 'inUse') {
        // InUse
        if (showCheckDatas.every((e: any) => e.inUse === true)) {
          _inUseAllCheck++;
        }
        if (showCheckDatas.every((e: any) => !e.inUse)) {
          _inUseIndeterminate++;
        }
      }
      if (keyId === 'actionIds') {
        // actions
        if (showCheckDatas.every((e: any) => e.actionIds && e.actions && e.actionIds.length === e.actions.length)) {
          _actionAllCheck++;
        }
        if (showCheckDatas.every((e: any) => !e.actionIds || (e.actions && e.actionIds.length !== e.actions.length))) {
          _actionIndeterminate++;
        }
      }
    });
    if (keyId === 'inUse') {
      this.inUseCheckAll = _inUseAllCheck === mtDataLength;
      this.inUseIndeterminate = !this.inUseCheckAll && _inUseIndeterminate !== mtDataLength;
    }
    if (keyId === 'actionIds') {
      this.actionsCheckAll = _actionAllCheck === mtDataLength;
      this.actionsIndeterminate = !this.actionsCheckAll && _actionIndeterminate !== mtDataLength;
    }

    // step2、重置中间父按钮的状态：全选、未选、部分选中
    this.tbData.forEach((f: any) => {
      f._EXPANDDATA.forEach((e: any) => {
        let checkCount = 0;
        if (e.children && e.children.length > 0) {
          checkCount = this.calculatSelectedCount(f._EXPANDDATA, e, keyId, keyId === 'actionIds');
          if (keyId === 'inUse') {
            e.inUse = checkCount > 0;
          }
          if (keyId === 'actionIds') {
            if (checkCount > 0) {
              e.actionIds = [true];
            } else {
              e.actionIds = [];
            }
          }
        }
        this.refreshRowStatus(f, e.id, e.inUse, e.actionIds, keyId);
      });
    });
  }

  private refreshRowStatus(row: any, id: string, inUseCheck: boolean, actionCheck: [], keyId: string) {
    if (row.id === id) {
      if (keyId === 'inUse') {
        row.inUse = inUseCheck;
      }
      if (keyId === 'actionIds') {
        row.actionIds = actionCheck;
      }
      return;
    }
    if (row.children && row.children.length > 0) {
      row.children.forEach((d: any) => {
        this.refreshRowStatus(d, id, inUseCheck, actionCheck, keyId);
      });
    }
  }

  private calculatSelectedCount(expandData: any, row: any, keyId: string, multiple: boolean) {
    let checkCount = 0;
    if (row.children && row.children.length > 0) {
      row.children.forEach((element: any) => {
        checkCount += this.calculatSelectedCount(expandData, element, keyId, multiple);
      });
    } else {
      if (multiple) {
        if (expandData.find((e: any) => e.id === row.id && (e[keyId] || []).length > 0)) {
          checkCount++;
        }
      } else {
        if (expandData.find((e: any) => e.id === row.id && e[keyId] === true)) {
          checkCount++;
        }
      }
    }

    return checkCount;
  }

  private get mtRowData(): any[] {
    this.rowData = [];
    this.tbData.forEach((m: any) => {
      if (m._EXPANDDATA) {
        m._EXPANDDATA.forEach((f: any) => {
          this.rowData.push(f);
        });
      }
    });
    return this.rowData;
  }
  private zdmnName(orgs: any) {
    const text: any = [];
    (orgs || []).map((m: any) => {
      text.push(m.organizationName);
    });
    return text.join(';');
  }
  private onClosepop(result: boolean, resultList: any, record: any) {
    if (result) {
      const orgs = resultList.map((m: any) => {
        return {
          organizationId: m.key,
          organizationName: m.title,
          organizationCode: m.code,
          organizationPath: m.path
        };
      });
      record.dataAuthorityAppointOrganizations = orgs;
      this.tbData.forEach((f: any) => {
        if (f._EXPANDDATA) {
          const extendRow = f._EXPANDDATA.find((b: any) => b.id === record.id);
          if (extendRow) {
            extendRow.dataAuthorityAppointOrganizations = orgs;
          }
        }
      });
    }
    this.zdmbVisible = false;
  }
  created() {
    this.roleId = this.$route.query['role-id'] as string;
    this.dataAuthorityItems = i18nHelper.getLocaleObject('platform.role.dataAuthorityItems') || [];
    this.fieldsSlotMap['inUse'] = (text: any, record: any, index: number) => {
      return (
        <a-checkbox
          v-model={record.inUse}
          on-change={(e: any) => this.onInUseCheckChange(e.target.checked, record)} >
        </a-checkbox>
      );
    };
    this.fieldsSlotMap['actionIds'] = (text: any, record: any, index: number) => {
      return (
        (record.children && record.children.length > 0) ?
          <a-checkbox
            v-model={record.actionIds[0]}
            on-change={(e: any) => this.onActionsCheckChange(e.target.checked, record)} >
          </a-checkbox>
          :
          <a-checkbox-group
            v-model={record.actionIds}
            options={record.actionList}
            on-change={(e: any) => this.onActionsCheckChange(true, record)}
          >
          </a-checkbox-group>
      );
    };
    this.fieldsSlotMap['permissionIds'] = (text: any, record: any, index: number) => {
      return (
        <div>
          <a-select
            v-model={record.permissionIds[0]}
            allowClear
            showSearch
            mode='default'
            style='width: 97%'
            placeholder={this.$t('platform.role.dataRights')}
            class='mr-1'
            on-change={(e: any) => this.onDatePermissionChange(e, record)}
          >
            {
              this.permissionItems.map(permission => (
                <a-select-option key={permission.permissionId}>
                  {permission.description}
                </a-select-option>
              ))
            }
          </a-select>
          <a-icon
            type='profile'
            style='font-size: 16px; position: absolute; margin-top: 5px; cursor: pointer;'
            on-click={() => { this.dataPermissionShow = true; }}
          />
        </div>
      );
    };
    this.fieldsSlotMap['dataAuthority'] = (text: any, record: any, index: number) => {
      const dataAuthority = record.dataAuthority ? record.dataAuthority.split(',')[0] : '';
      return record.type === 'P' ? <div class={styles.dataAuthority_group}><a-select
        value={dataAuthority}
        allowClear
        placeholder={this.$t('platform.role.dataRights')}
        class={styles.dataAuthority}
        options={this.dataAuthorityItems}
        on-change={(e: any) => this.onDataAuthorityChange(e, record)}
      ></a-select>{dataAuthority === 'zdbm' ? <a-input
        placeholder='请选择指定部门'
        value={this.zdmnName(record.dataAuthorityAppointOrganizations)}
        on-click={() => {
          this.zdmbVisible = true;
          this.currentRecord = record;
        }}
      ></a-input> : ''}</div> : '';
    };
    this.tbRefresh();
    this.getPermissionItems();
  }

  private tbRefresh() {
    rightMenuService.getRoleMenus(this.roleId).subscribe(s => {
      this.tbData = this.tranActions(s);
      this.ontbDataChange();
    });
  }

  private tranActions(data: any[]) {
    const resultData: any = [];
    data.forEach(m => {
      m.actionList = (m.actions || []).map((f: any) => ({
        label: i18nHelper.getLocale('buttons.' + f.id),
        value: f.id,
        checked: m.actionIds.indexOf(f.id) !== -1
      }));
      if (!m.permissionIds) {
        m.permissionIds = [];
      }
      m['inUse'] = m.isRoleMenu;
      m['name'] = languageService.current.code === 'en' ? m.englishName || m.name : m.name;
      m['key'] = m.id;
      if (m.children && m.children.length > 0) {
        m.children = this.tranActions(m.children || []);
      } else {
        m.children = null;
      }
      resultData.push(m);
    });
    return resultData;
  }

  private onBack() {
    this.$router.push('/system-management/rights');
  }

  private onSave() {
    const saveData: PutRoleMenu[] = this.mtRowData
      .filter(f => f.type === MenuTypeEnum.page && f.inUse)
      .map(e => ({
        menuId: e.id,
        actionIds: e.actionIds.filter((f: any) => !!f),
        permissionIds: e.permissionIds,
        dataAuthority: e.dataAuthority,
        dataAuthorityAppointOrganizations: e.dataAuthorityAppointOrganizations
      }));
    rightMenuService.putRoleMenus(this.roleId, saveData).subscribe(s => {
      notificationHelper.success(i18nHelper.getLocale('messages.success'));
      this.tbRefresh();
    });
  }

  private onInUseCheckAllChange(checkedValues: boolean) {
    this.inUseIndeterminate = false;
    this.refreshAllStatus(checkedValues, 'inUse', false);
    this.refreshStatus('inUse');
  }

  private onActionsCheckAllChange(checkedValues: boolean) {
    this.actionsIndeterminate = false;
    this.refreshAllStatus(checkedValues, 'actionIds', true);
    this.refreshStatus('actionIds');
  }

  private onInUseCheckChange(checkedValues: boolean, row: any) {
    this.checkedChange(checkedValues, 'inUse', row, false);
  }

  private onActionsCheckChange(checkedValues: boolean, row: any) {
    this.checkedChange(checkedValues, 'actionIds', row, true);
  }

  private onDatePermissionChange(checkedValues: any, row: any) {
    this.tbData.forEach((f: any) => {
      if (f._EXPANDDATA) {
        const extendRow = f._EXPANDDATA.find((b: any) => b.id === row.id);
        if (extendRow) {
          extendRow.permissionIds = checkedValues ? [checkedValues] : [];
        }
      }
    });
  }

  private onDataAuthorityChange(e: any, row: any) {
    row.dataAuthority = e;
    row.dataAuthorityAppointOrganizations = [];
    this.tbData.forEach((f: any) => {
      if (f._EXPANDDATA) {
        const extendRow = f._EXPANDDATA.find((b: any) => b.id === row.id);
        if (extendRow) {
          extendRow.dataAuthority = e;
        }
      }
    });
  }

  private checkedChange($event: boolean, keyId: string, row: any, multiple: boolean) {
    if (multiple) {
      if (row.children && row.children.length > 0) {
        row[keyId] = $event ? [true] : [];
        this.selectChildren($event, keyId, row, multiple);
      } else {
        row.actionList.forEach((item: any) => {
          item['checked'] = (row.actionIds as []).findIndex(action => action === item.value) >= 0;
        });
        this.tbData.forEach((f: any) => {
          if (f._EXPANDDATA) {
            const extendRow = f._EXPANDDATA.find((b: any) => b.id === row.id);
            if (extendRow) {
              extendRow.actionIds = row.actionIds;
              extendRow.actionList = row.actionList;
            }
          }
        });
      }
      this.refreshStatus('actionIds');
    } else {
      row[keyId] = $event;
      this.tbData.forEach((f: any) => {
        if (f._EXPANDDATA) {
          const extendRow = f._EXPANDDATA.find((b: any) => b.id === row.id);
          if (extendRow) {
            extendRow.inUse = row.inUse;
          }
        }
      });
      this.selectChildren($event, keyId, row, multiple);
      this.refreshStatus('inUse');
    }
  }

  private selectChildren($event: boolean, keyId: string, row: any, multiple: boolean) {
    this.tbData.forEach((f: any) => {
      if (f._EXPANDDATA && f._EXPANDDATA.find((b: any) => b.id === row.id)) {
        f._EXPANDDATA.forEach((e: any) => {
          // 遍历所有页面，一旦发现页面属于点击行的子页面，则自动选中/未选中
          const parents = this.getParents(f._EXPANDDATA, e);
          if (parents.find(z => z.id === row.id)) {
            if (multiple) {
              if (e.children && e.children.length > 0) {
                e[keyId] = $event ? [true] : [];
              } else {
                e.actionList.forEach((item: any) => {
                  item['checked'] = $event;
                });
                e[keyId] = $event ? (e.actionList || []).map((m: any) => m.value) : [];
              }
            } else {
              e[keyId] = $event;
            }
          }
        });
      }
    });
  }

  private getParents(expandData: any, row: any) {
    const lstParents = [];
    let parent = row;
    while (parent && parent.parentId) {
      parent = expandData.find((f: any) => f.id === parent.parentId);
      lstParents.push(parent);
    }
    return lstParents;
  }

  /**
   * 列选择-更新所有状态
   */
  private refreshAllStatus($event: boolean, keyId: string, multiple: boolean) {
    this.tbData.forEach((f: any) => {
      if (f._EXPANDDATA) {
        f._EXPANDDATA.forEach((e: any) => {
          if (multiple) {
            if (e.children && e.children.length > 0) {
              e[keyId] = $event ? [true] : [];
            } else {
              const dataset = e.actionList;
              dataset.forEach((item: any) => {
                item['checked'] = $event;
              });
              e[keyId] = $event ? (dataset || []).map((m: any) => m.value) : [];
            }
          } else {
            e[keyId] = $event;
          }
        });
      }
    });
  }

  render() {
    return (
      <div>
        <a-card>
          <div class={styles.actions}>
            <a-button on-click={this.onBack}>{this.$t('buttons.back')}</a-button>
            <a-button
              type='primary'
              v-permission={this.actions.save}
              class='ml-1'
              on-click={this.onSave}>
              {this.$t('buttons.save')}
            </a-button>
          </div>
          <a-table
            columns={this.columns}
            data-source={this.tbData}
            scopedSlots={this.fieldsSlotMap}
            pagination={false}
          >
            <div slot='name'>
              {this.$t('platform.fields.menu')}
            </div>
            <div slot='inUse'>
              <a-checkbox
                indeterminate={this.inUseIndeterminate}
                v-model={this.inUseCheckAll}
                on-change={(e: any) => this.onInUseCheckAllChange(e.target.checked)} style='display: block'></a-checkbox>
              {this.$t('platform.role.show')}
            </div>
            <div slot='actionIds'>
              <a-checkbox
                indeterminate={this.actionsIndeterminate}
                v-model={this.actionsCheckAll}
                on-change={(e: any) => this.onActionsCheckAllChange(e.target.checked)} style='display: block'></a-checkbox>
              {this.$t('platform.role.action')}
            </div>
            <div slot='permissionIds'>
              {this.$t('platform.role.dataRights')}
            </div>
            <div slot='dataAuthority'>
              {this.$t('platform.role.dataRights')}
            </div>
          </a-table>
        </a-card>
        <a-modal
          width='1000px'
          title={this.$t('platform.dataPermission.permission')}
          visible={this.dataPermissionShow}
          destroyOnClose={true}
          maskClosable={false}
          on-cancel={() => { this.dataPermissionShow = false; }}
          on-ok={() => { this.dataPermissionShow = false; this.getPermissionItems(); }}
        >
          <data-permission></data-permission>
        </a-modal>
        <select-department
          visible={this.zdmbVisible}
          multiple={true}
          value={((this.currentRecord && this.currentRecord.dataAuthorityAppointOrganizations) || []).map((m: any) => {
            return {
              key: m.organizationId,
              title: m.organizationName,
              code: m.organizationCode,
              path: m.organizationPath
            };
          })}
          on-closepop={(result: boolean, resultList: any) =>
            this.onClosepop(result, resultList, this.currentRecord)}></select-department>
      </div>
    );
  }
}
