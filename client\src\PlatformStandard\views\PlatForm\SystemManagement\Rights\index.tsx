import { Component, Vue } from 'vue-property-decorator';
import { CompCard, CompTableHeader } from '@/PlatformStandard/components';
import { rightService } from './service';
import { i18nHelper, notificationHelper, toCasedStyleObject } from '@/PlatformStandard/common/utils';
import { languageService } from '@/PlatformStandard/services/language';
import { ActionEnum } from '@/PlatformStandard/services/menu';
import { RoleDto, RoleQueryDto } from './types';
import styles from './Rights.module.less';
import { RoleEdit } from './RoleEdit';
import { commonService } from '@/PlatformStandard/services/common';

@Component({ components: { CompCard, CompTableHeader, RoleEdit } })
export class RightsManagement extends Vue {
  private pagination = {
    total: 0,
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    size: 'default',
    showTotal: (total: string) =>
      this.$l
        .getLocale('paginations.total')
        .toString()
        .replace('{{value}}', total),
  };
  private query: RoleQueryDto = {};
  private dataSource: RoleDto[] = [];
  private fieldsSlotMap: any = {};
  private actions = ActionEnum;
  private statusDataset = i18nHelper.getLocaleObject('platform.role.inUseDataset');
  private roleEditShow = false;
  private selectRole: RoleDto = {};
  private memberRoute = `/system-management/rights/member`;
  private menuRoute = `/system-management/rights/menu`;
  private applicationDataset: any = [];

  private columns = [
    {
      dataIndex: 'roleCode',
      slots: { title: 'roleCode' }
    },
    {
      dataIndex: 'roleName',
      slots: { title: 'roleName' },
    },
    {
      dataIndex: 'description',
      slots: { title: 'description' },
      width: '35%'
    },
    {
      dataIndex: 'applicationIds',
      slots: { title: 'applicationName' },
      scopedSlots: { customRender: 'applicationName' },
      width: '15%'
    },
    {
      dataIndex: 'status',
      slots: { title: 'status' },
      scopedSlots: { customRender: 'status' },
    },
    {
      dataIndex: 'action',
      slots: { title: 'action' },
      scopedSlots: { customRender: 'action' }
    },
  ];

  private loadData(reset: boolean = false) {
    if (reset) {
      this.pagination.current = 1;
    }
    const params = { 'page-index': this.pagination.current, 'page-size': this.pagination.pageSize };
    rightService.getRoles({ ...toCasedStyleObject(this.query), ...params }).subscribe(data => {
      this.dataSource = data.items;
      this.pagination.total = data.total;
      this.$forceUpdate();
    });
  }

  private reset() {
    this.query = {};
  }

  private addRole() {
    this.roleEditShow = true;
    this.selectRole = {};
  }

  private editRole(roleDto: RoleDto) {
    this.roleEditShow = true;
    this.selectRole = roleDto;
  }

  private saveRole() {
    const errorMsgs = (this.$refs.editRole as RoleEdit).validateForm() as string[];
    if (errorMsgs.length === 0) {
      const role = (this.$refs.editRole as RoleEdit).save() as RoleDto;
      rightService.saveRole(role).subscribe(() => {
        this.roleEditShow = false;
        this.loadData(false);
        notificationHelper.success(i18nHelper.getLocale('messages.success'));
      });
    } else {
      notificationHelper.error(errorMsgs);
    }
  }

  private deleteRole(roleDto: RoleDto) {
    rightService.deleteRole(String(roleDto.roleId)).subscribe(() => {
      this.loadData(true);
      notificationHelper.success(i18nHelper.getLocale('messages.success'));
    });
  }

  created() {
    if (this.$route.query['app-code']) {
      this.query.appCode = this.$route.query['app-code'] as string;
    }
    if (!this.query.appCode && sessionStorage.getItem('appCode')) {
      this.query.appCode = sessionStorage.getItem('appCode') as string;
    }
    commonService.getApplications().subscribe(rs => {
      this.applicationDataset = rs;
    });
    languageService.language$.subscribe(() => {
      this.statusDataset = i18nHelper.getLocaleObject('platform.role.inUseDataset');
    });
    this.fieldsSlotMap['applicationName'] = (ids: any[], record: RoleDto, index: number) => {
      const appNames = ids.map((m: any) => this.applicationDataset.find((f: any) => f.value === m).label);
      return (
        <div>
          {appNames.join(',')}
        </div>
      );
    };
    this.fieldsSlotMap['status'] = (text: RoleDto[], record: RoleDto, index: number) => {
      // return text ? this.$l.getLocale('platform.fields.valid') : this.$l.getLocale('platform.fields.inValid');
      return (
        <div>
          {text ?
          <a-tag color='blue'> { this.$l.getLocale('platform.fields.valid')} </a-tag> : <a-tag color='red'> { this.$l.getLocale('platform.fields.inValid') } </a-tag>}
        </div>
      );
    };

    this.fieldsSlotMap['action'] = (text: RoleDto[], record: RoleDto, index: number) => {
      return (
        <div>
          <span v-permission={this.actions.edit}>
            <a-button type='link' on-click={() => this.editRole(record)} size='small' class={styles.list_button}>
              {this.$l.getLocale('buttons.edit')}
            </a-button>
          </span>

          <span v-permission={this.actions.userIAM}>
            <a-button type='link' on-click={() => {
              this.$router.push(`${this.memberRoute}?role-id=${record.roleId}`);
            }} size='small' class={styles.list_button}>
              {this.$l.getLocale('buttons.user-iam')}
            </a-button>
          </span>

          <span v-permission={this.actions.menuIAM}>
            <a-button type='link' on-click={() => {
              this.$router.push(`${this.menuRoute}?role-id=${record.roleId}`);
            }} size='small' class={styles.list_button}>
              {this.$l.getLocale('buttons.menu-iam')}
            </a-button>
          </span>

          <span v-permission={this.actions.delete}>
            <a-popconfirm title={this.$t('messages.delete')} on-confirm={() => this.deleteRole(record)}>
              <a-button type='link' size='small' class={styles.list_button}>
                {this.$l.getLocale('buttons.delete')}
              </a-button>
            </a-popconfirm>
          </span>
        </div>
      );
    };
    this.loadData(true);
  }

  render() {
    return (
      <div>
       <comp-card class={styles.card_top}>
          <comp-table-header class={styles.table_header}
            on-search={() => {
              this.loadData(true);
            }}
            on-reset={() => {
              this.reset();
            }}
          >
            <template slot='base'>
              <a-row>
                <a-row>
                  <a-col span='5' class='mr-1'>
                    <a-form-item label={this.$t('platform.fields.application')}>
                      <a-select default-value={this.query.appCode}
                        on-change={(value: any) => this.query.appCode = value}
                        placeholder={this.$t('platform.fields.application')}
                        allowClear>
                        {this.applicationDataset.map((item: any) => (
                          <a-select-option value={item.code}>{item.label}</a-select-option>
                        ))}
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col span='5' class='mr-1'>
                    <a-form-item label={this.$t('platform.fields.code')}>
                      <a-input v-model={this.query.roleCode}></a-input>
                    </a-form-item>
                  </a-col>
                  <a-col span='5' class='mr-1'>
                    <a-form-item label={this.$t('platform.fields.name')}>
                      <a-input v-model={this.query.roleName}></a-input>
                    </a-form-item>
                  </a-col>
                  <a-col span='5' class='mr-1'>
                    <a-form-item label={this.$t('platform.fields.status')}>
                      <a-select v-model={this.query.status}
                      allowClear >
                        {this.statusDataset.map((item: any) => (
                          <a-select-option value={item.value}>{item.label}</a-select-option>
                        ))}
                      </a-select>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-row>
            </template>
          </comp-table-header>
        </comp-card>
        <comp-card>
          <a-card class={styles.base_table} bordered={false}>
            <div slot='extra'>
            <a-button type='primary' class={styles.common_btn} on-click={() => this.addRole()}>{this.$t('buttons.add')}</a-button>
            </div>
            <a-table
              size='small'
              columns={this.columns}
              data-source={this.dataSource}
              pagination={this.pagination}
              scopedSlots={this.fieldsSlotMap}
              on-change={(pagination: any) => {
                this.pagination = pagination;
                this.loadData(false);
              }}
            >
              <span slot='roleCode'>{this.$t('platform.fields.code')}</span>
              <span slot='roleName'>{this.$t('platform.fields.name')}</span>
              <span slot='description'>{this.$t('platform.fields.description')}</span>
              <span slot='applicationName'>{this.$t('platform.fields.application')}</span>
              <span slot='status'>{this.$t('platform.fields.status')}</span>
              <span slot='action'>{this.$t('platform.fields.operation')}</span>
            </a-table>
          </a-card>
        </comp-card>
        <a-modal
          width='800px'
          title={this.$t('platform.fields.role')}
          visible={this.roleEditShow}
          destroyOnClose={true}
          maskClosable={false}
          on-cancel={() => { this.roleEditShow = false; this.selectRole = {}; }}
          on-ok={() => this.saveRole()}
        >
          <role-edit ref='editRole' selectRole={this.selectRole} />
        </a-modal>
      </div>
    );
  }
}
