import { RouteConfig } from 'vue-router';
import { routerLoginGuard, routerLogoutGuard, routerPermissionGuard } from '@/PlatformStandard/common/utils';
import { MainLayout } from '@/layout/MainLayout';
import { BpmOrganizationManagementRoutes } from './bpm/organization-management/organization-management.router';
import { BpmSystemManagementRoutes } from './bpm/system-management/system-management.routes';

export const bpmRoutes: RouteConfig[] = [
  {
    path: '/organization-management/',
    // path: '/home-management/',
    component: MainLayout,
    children: BpmOrganizationManagementRoutes,
    beforeEnter: routerPermissionGuard
  },
  {
    path: '/system-management/',
    component: MainLayout,
    children: BpmSystemManagementRoutes,
    beforeEnter: routerPermissionGuard
  },
];
