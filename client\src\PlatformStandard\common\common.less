@import '../../themes/default/variables.less';
// 盒模型
.card_top {
  margin-bottom:8px !important;
  padding: 10px 10px 0 10px !important;

  :global(.ant-card-body){
    padding: 0px 10px 0 10px !important;
    :global(.ant-layout){
      padding-bottom:0 !important
    }
  }
}
.info_card {
  display: flex !important;
  width: 100%;
  flex-direction: column;
  height: auto;
  .buttons {
    height: @btn-height-sm;
  }
  .row_outer{
    background-color: @content-bg-1;
    margin: @base-size 0px !important;
  }
  .operation{
    text-align: right;
    cursor: pointer;
  }
} 

// 表格
:global(.ant-table-thead){
  background: #eee;
}
:global(.ant-table-small > .ant-table-content > .ant-table-body){
  margin: 0 !important;
}
// 行样式
.row_outer{
  background-color: @content-bg-1;
  margin: @base-size 0px !important;
}
.rowNew{
  margin: 0;
  padding:0px;
}
.base_table {
  :global(.ant-card-body) {
    padding: 0;
  }
}
    
// 按钮样式
.list_button {
  padding: 0 3px !important
} 
.common_btn {
  padding:0;
  margin: 0;
  width: 70px !important;
  height: 30px;
  text-align: center;
  line-height: 32px;
  border-radius: 5px !important;
  border: 1px solid #ddd !important;
  background-color: #fff !important;
  color: #333 !important;
  opacity: 1;
  text-shadow: none !important;
  box-shadow: none !important;
}
.operation{
  text-align: right;
  cursor: pointer;
}
.searchBtn{
  margin-right: 10px;
  width: 72px;
  height: 32px;
  opacity: 1;
  background: @btn-color;
  text-align: center;
  line-height: 32px;
  border-radius: 5px !important;
}
