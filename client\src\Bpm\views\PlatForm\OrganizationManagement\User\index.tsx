import { Component, Vue } from 'vue-property-decorator';
import { SearchUserDto, UserListDto } from './types';
import styles from './user.module.less';
import { userService } from './service';
import { i18nHelper, toCasedStyleObject } from '@/PlatformStandard/common/utils';
import { languageService } from '@/PlatformStandard/services/language';
import { ActionEnum } from '@/PlatformStandard/services/menu';
import { CompCard, CompTableHeader } from '@/PlatformStandard/components';
import { OrganizationTreeSelect } from '@/PlatformStandard/components/OrganizationUser/OrganizationTreeSelect';

@Component({
  components: { CompCard, CompTableHeader, OrganizationTreeSelect },
})
export class User extends Vue {
  private pagination = {
    total: 0,
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    size: 'default',
    showTotal: (total: string) =>
      this.$l
        .getLocale('paginations.total')
        .toString()
        .replace('{{value}}', total),
  };

  private queryParam: SearchUserDto = {};
  private department: any[] = [];
  private dataSource: UserListDto[] = [];
  private fieldsSlotMap: any = {};
  private actions = ActionEnum;

  private columns = [
    {
      key: 'account',
      dataIndex: 'account',
      width: '10%',
      slots: { title: 'account' }
    },
    {
      key: 'name',
      dataIndex: 'name',
      width: '8%',
      scopedSlots: { customRender: 'name' },
      slots: { title: 'name' }
    },
    {
      key: 'phoneNumber',
      dataIndex: 'phoneNumber',
      slots: { title: 'phoneNumber' }
    },
    {
      key: 'email',
      dataIndex: 'email',
      slots: { title: 'email' }
    },
    {
      key: 'positionName',
      dataIndex: 'positionName',
      width: '150px',
      slots: { title: 'positionName' }
    },
    {
      key: 'organizationName',
      dataIndex: 'organizationName',
      width: '150px',
      scopedSlots: { customRender: 'organizationName' },
      slots: { title: 'organizationName' }
    },
    {
      key: 'status',
      dataIndex: 'status',
      width: '80px',
      scopedSlots: { customRender: 'status' },
      slots: { title: 'status' }
    },
    {
      dataIndex: 'operation',
      width: '150px',
      scopedSlots: { customRender: 'operation' },
      slots: { title: 'operation' }
    },
  ];

  private statusItems = i18nHelper.getLocaleObject('platform.user.statusDataset');

  private loadData(reset: boolean = false) {
    if (reset) {
      this.pagination.current = 1;
    }
    const params = { 'page-index': this.pagination.current, 'page-size': this.pagination.pageSize };
    userService.getUserList({ ...toCasedStyleObject(this.queryParam), ...params }).subscribe(data => {
      this.dataSource = data.items;
      this.pagination.total = data.total;
      this.$forceUpdate();
    });
  }

  // 重置
  private reset() {
    this.queryParam = {};
  }

  private add() {
    this.$router.push('./users/add');
  }

  private edit(user: UserListDto) {
    this.$router.push('./users/edit?user-id=' + user.id);
  }

  private query(id: string) {
    this.$router.push('./users/view?user-id=' + id);
  }

  public impersonate(user: UserListDto) {
    userService.getImpernateUrl(String(user.id)).subscribe(url => {
      // window.open(url, '_blank');
      location.href = url;
    });
  }

  created(): void {
    languageService.language$.subscribe(() => {
      this.statusItems = i18nHelper.getLocaleObject('platform.user.statusDataset');
    });

    this.fieldsSlotMap['status'] = (text: UserListDto[], record: UserListDto, index: number) => {
      // const statustxt = this.statusItems.find((f: any) => f.value === text).label;
      // return statustxt;
      const statustxt = this.statusItems.find((f: any) => f.value === text).label;
      // return statustxt;
      console.log(statustxt);
      return (
        <div>
          {statustxt === '无效' ? <a-tag color='blue'> {statustxt} </a-tag> : <a-tag color='red'> {statustxt} </a-tag>}
        </div>
      );
    };

    this.fieldsSlotMap['organizationName'] = (text: UserListDto[], record: UserListDto, index: number) => {
      return (
        record.organizationName ?
          <span class={styles.org_title}>
            <a-tooltip>
              <template slot='title'>
                {record.organizationFullName}
              </template>
              {record.organizationName}
              <a-icon type='info-circle' theme='filled' />
            </a-tooltip>
          </span>
          :
          null
      );
    };

    this.fieldsSlotMap['name'] = (text: UserListDto[], record: UserListDto, index: number) => {
      return (
        <a-button type='link' on-click={() => this.query(String(record.id))}>
          {record.name}
        </a-button>
      );
    };

    this.fieldsSlotMap['operation'] = (text: UserListDto[], record: UserListDto, index: number) => {
      return (
        <div>
          <span v-permission={this.actions.edit} class='mr-1'>
            <a-button type='link' on-click={() => this.edit(record)} size='small' class={styles.list_button}>
              {this.$l.getLocale('buttons.edit')}
            </a-button>
          </span>
          <span class='mr-1'>
            <a-button type='link' on-click={() => this.impersonate(record)} size='small' class={styles.list_button}>
              {this.$l.getLocale('buttons.impersonate')}
            </a-button>
          </span>
        </div>
      );
    };
    this.loadData(true);
  }

  render() {
    return (
      <div>
        <comp-card class={styles.card_top} >
          <comp-table-header class={styles.table_header}
            on-search={() => {
              this.loadData(true);
            }}
            on-reset={() => {
              this.reset();
            }}
          >
            <template slot='base'>
              <a-row class={styles.rowNew} gutter={8} align='middle' type='flex'>
                <a-col span='8'>
                  <a-form-item label={this.$t('platform.fields.keyword')}>
                    <a-input v-model={this.queryParam.keyWord}></a-input>
                  </a-form-item>
                </a-col>
                <a-col span='8'>
                  <organization-tree-select value={this.queryParam.organizationId} multiple={false} allowClear={true}
                    onChange={(value: string) => { this.queryParam.organizationId = value; this.$forceUpdate(); }}>
                  </organization-tree-select>
                </a-col>
                <a-col span='8'>
                  <a-form-item label={this.$t('platform.fields.status')}>
                    <a-select
                      allowClear
                      class={styles.status_select}
                      v-model={this.queryParam.status}
                    >
                      {(this.$t('platform.user.statusDataset') as any).map((v: any) => {
                        if (v.value !== 2) {
                          return <a-select-option value={v.value}>{v.label}</a-select-option>;
                        }
                      })}
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
            </template>
          </comp-table-header>
        </comp-card>
        <comp-card>
          <a-card class={styles.base_table} bordered={false}>
            <div slot='extra' style='line-height:0'>
              <a-button type='primary' on-click={() => this.add()} class={styles.common_btn} >{this.$t('buttons.add')}</a-button>
            </div>
            <a-table
              size='small'
              bordered={false}
              columns={this.columns}
              data-source={this.dataSource}
              pagination={this.pagination}
              scopedSlots={this.fieldsSlotMap}
              on-change={(pagination: any) => {
                this.pagination = pagination;
                this.loadData(false);
              }}
              style='margin-top:0'
            >
              <span slot='account'>{this.$t('platform.fields.account')}</span>
              <span slot='name'>{this.$t('platform.user.name')}</span>
              <span slot='phoneNumber'>{this.$t('platform.user.phone')}</span>
              <span slot='email'>{this.$t('platform.user.email')}</span>
              <span slot='positionName'>{this.$t('platform.fields.primaryPosition')}</span>
              <span slot='organizationName'>{this.$t('platform.fields.organization')}</span>
              <span slot='status'>{this.$t('platform.fields.status')}</span>
              <span slot='operation'>{this.$t('platform.fields.operation')}</span>
            </a-table>
          </a-card>
        </comp-card>
      </div>
    );
  }
}
