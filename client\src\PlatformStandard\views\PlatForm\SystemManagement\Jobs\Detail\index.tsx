import { Component, Prop, Vue } from 'vue-property-decorator';
import { jobsService } from '../service';
import { JobDto } from '../types';

@Component
export class JobDetail extends Vue {
  @Prop() selectJob!: JobDto;
  private currentJob: JobDto = {};

  created() {
    jobsService.getJob(Number(this.selectJob.jobId)).subscribe(rs => {
      this.currentJob = rs;
    });
  }

  render() {
    return (
      <div>
        <a-row>
          <a-col span='24'>
            <a-form-item label={this.$t('platform.jobManage.jobData')}>
              <a-textarea v-model={this.currentJob.arguments} rows='8' />
            </a-form-item>
          </a-col>
          <a-col span='24'>
            <a-form-item label={this.$t('platform.jobManage.jobResult')}>
              <a-textarea v-model={this.currentJob.excuteResult} rows='8' />
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    );
  }
}
