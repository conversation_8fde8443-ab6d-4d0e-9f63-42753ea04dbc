import { Component, Vue } from 'vue-property-decorator';
import { CompCard } from '@/PlatformStandard/components';
import styles from './menu.module.less';
import { menuService } from './service';
import { ValueLabelPair } from '@/PlatformStandard/common/defines';
import { AppMenuLevelDto, MenuEntity } from './types';
import { notificationHelper, i18nHelper } from '@/PlatformStandard/common/utils';
import { MenuEdit } from './Edit/index';
import { MenuTypeEnum } from '@/PlatformStandard/services/menu';

@Component({
  components: { MenuEdit, CompCard }
})
export class MenuManagement extends Vue {
  private replaceFields = { children: 'children', title: 'name', key: 'id' };
  private options: any = [];
  private treeData: MenuEntity[] = [];
  private leftHeight = 530;
  private menuId = '';
  private selectMenu: MenuEntity = {};
  private editMenu: MenuEntity = {};
  private selectMenuIndex = 1;
  private modelShow = false;
  private modelType = 'add';
  private appMenuLevel: AppMenuLevelDto[] = [];
  private maxMenuLevel = 3;

  private onSelect(selectedKeys: any, e: any) {
    console.log(`🚀 ~ e:`, e);
    if (selectedKeys.length > 0) {
      this.menuId = selectedKeys[0];
      this.selectMenuIndex = e.selectedNodes[0].data.props.levelData;
      this.selectMenu = e.selectedNodes[0].data.props;
      const appCode = e.selectedNodes[0].data.props.appId;
      this.maxMenuLevel = (this.appMenuLevel.find(d => d.appCode === appCode) || {menuLevel: 3}).menuLevel;
    } else {
      this.menuId = '';
      this.selectMenuIndex = 1;
      this.selectMenu = {};
    }
  }

  private add(node: any) {
    console.log(`🚀 ~ this.selectMenuIndex:`, this.selectMenuIndex);
    if (!this.menuId) {
      notificationHelper.warning(this.$l.getLocale(['controls.select', 'platform.routes.menu']));
    } else if (node.key === MenuTypeEnum.page && this.selectMenuIndex < 3) {
      notificationHelper.warning(this.$l.getLocale(['controls.select', 'platform.menu.subModule']));
    } else if (node.key === MenuTypeEnum.module && this.selectMenuIndex > this.maxMenuLevel - 1) {
      notificationHelper.warning(this.$l.getLocale(['controls.select', 'platform.menu.topModule']));
    } else if (this.selectMenu.type === 'P') {
      notificationHelper.warning(this.$l.getLocale(['controls.select', 'platform.menu.topModule']));
    } else {
      this.editMenu = {
        parentId: this.selectMenu.id,
        type: node.key,
        appId: this.selectMenu.appId,
        actionIds: [],
        hidden: false
      };
      this.modelType = 'add';
      this.modelShow = true;
    }
  }

  private edit() {
    this.editMenu = this.selectMenu;
    this.modelType = 'edit';
    this.modelShow = true;
  }

  private generateTreeNodeOptions(items: any[], levelData: number): MenuEntity[] {
    return items.map((data: MenuEntity) => {
      return {
        ...data,
        levelData: levelData,
        children: (data.children && data.children.length > 0) ? this.generateTreeNodeOptions(data.children, levelData + 1) : []
      };
    });
  }

  private saveEdit(): void {
    const errorMsgs = (this.$refs.editMenuInfo as any).validateForm() as string[];
    if (errorMsgs.length === 0) {
      const menu = (this.$refs.editMenuInfo as any).getMenuInfo() as MenuEntity;
      if (this.modelType === 'add') {
        menuService.addMenu(menu).subscribe(data => {
          this.getTreeData();
          this.modelShow = false;
        });
      } else {
        menuService.updateMenu(menu.id || '', menu).subscribe(data => {
          this.selectMenu = menu;
          this.getTreeData();
          this.modelShow = false;
        });
      }
    } else {
      notificationHelper.error(errorMsgs);
    }
  }

  private getTreeData(): void {
    menuService.getMenus().subscribe(data => {
      this.treeData = this.generateTreeNodeOptions(data, 1);
    });
  }

  private deleteOk(): void {
    menuService.deleteMenu(this.selectMenu.id || '').subscribe(data => {
      this.getTreeData();
    });
  }

  created() {
    this.options = this.$t('platform.menu.typeDataset');
    menuService.getApplicationMenuLevel().subscribe(data => {
      this.appMenuLevel = data;
      this.getTreeData();
    });
  }

  mounted() {
    this.leftHeight = document.documentElement.offsetHeight - 50;
  }
  render() {
    return <div class={styles.page}>
      <div class={styles.tree_list} style={`height:${this.leftHeight}px`}>
        <comp-card title={this.$t('platform.menu.list')} class={styles.tree_card}
          bodyStyle={{ overflow: 'auto', height: `${this.leftHeight - 170}px` }}>
          <a-tree  tree-data={this.treeData} replace-fields={this.replaceFields} on-select={this.onSelect}>
            <a-icon slot='switcherIcon' type='down' />
            {/* <a-icon slot='folder' type='folder' style='color:#1890FF' theme='filled'/>
            <a-icon slot='file' type='file'  style='color:#9BB2C6' theme='filled'/> */}
          </a-tree>
        </comp-card>
      </div>
      <div class={styles.content_info}>
      <div class={styles.edit_info}>
          <a-form labelCol={{ span: 3 }} wrapperCol={{ span: 20 }}>
            <comp-card title={this.$t('platform.menu.info')} class={styles.info_card}>
              <div slot='extra'>
                <a-dropdown style='margin:0 0 0 10px' class={styles.common_btn}>
                  <a-menu slot='overlay' on-click={this.add}>
                    {this.options.map((d: ValueLabelPair) => {
                      return (<a-menu-item key={d.value}>
                        {d.label}
                      </a-menu-item>);
                    })}
                  </a-menu>
                  <a-button type='primary'>{this.$t('buttons.add')}<a-icon type='down' /> </a-button>
                </a-dropdown>
                {this.selectMenuIndex > 1 ? ([<a-button type='primary' style='margin:0 0 0 10px' class={styles.common_btn}
                 on-click={this.edit}>
                  {this.$t('buttons.edit')}
                </a-button>,
                <a-popconfirm
                  title={this.$t('messages.delete')}
                  on-confirm={this.deleteOk}
                >
                  <a-button class={styles.buttons}>
                    {this.$t('buttons.delete')}
                  </a-button>
                </a-popconfirm>
                ]) : ''}
              </div>
              <a-row>
                <a-form-item label={this.$t('platform.fields.chineseName')}>
                  {this.selectMenu.name}
                </a-form-item>
              </a-row>
              <a-row>
                <a-form-item label={this.$t('platform.fields.englishName')}>
                  {this.selectMenu.englishName}
                </a-form-item>
              </a-row>
              <a-row>
                <a-form-item label={this.$t('platform.menu.route')}>
                  {this.selectMenu.path}
                </a-form-item>
              </a-row>
              {
                this.selectMenu.type === MenuTypeEnum.page ? (
                  <a-row>
                    <a-form-item label={this.$t('platform.menu.path')}>
                      {this.selectMenu.filePath}
                    </a-form-item>
                  </a-row>
                ) : (
                  <a-row>
                    <a-form-item label={this.$t('platform.menu.icon')}>
                      {this.selectMenu.icon}
                    </a-form-item>
                  </a-row>
                )
              }
              <a-row>
                <a-form-item label={this.$t('platform.fields.order')}>
                  {this.selectMenu.order}
                </a-form-item>
              </a-row>
              <a-row>
                <a-form-item label={this.$t('platform.menu.hidden')}>
                  {this.selectMenu.id ?
                    (this.selectMenu.hidden ? this.$t('platform.fields.yes')
                      : this.$t('platform.fields.no')) : ''}
                </a-form-item>
              </a-row>
              {
                this.selectMenu.type === MenuTypeEnum.page ? ([
                  <a-row>
                    <a-form-item label={this.$t('platform.menu.action')}>
                      {this.selectMenu.actionIds ? this.selectMenu.actionIds.map(d => {
                        return this.$t(`buttons.${d}`);
                      }).join(';') : ''}
                    </a-form-item>
                  </a-row>
                ]) : ''
              }
            </comp-card>
          </a-form>
        </div>
      </div>
      <a-modal width='600px'
        title={this.modelType === 'add' ? this.$t('buttons.add') : this.$t('buttons.edit')}
        visible={this.modelShow}
        destroyOnClose={true}
        maskClosable={false}
        on-cancel={(c: any) => this.modelShow = false}
        on-ok={this.saveEdit}>
        <menu-edit selectMenu={this.editMenu} ref='editMenuInfo'></menu-edit>
      </a-modal>
    </div>;
  }
}
