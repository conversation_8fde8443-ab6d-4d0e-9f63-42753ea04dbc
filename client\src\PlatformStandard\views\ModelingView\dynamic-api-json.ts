import { guidHelper } from '../../common/utils';

function selectJson(
    dataSource: any,
    searchData: any,
    routeQuery: any,
    hasPagination: boolean,
    pageIndex: number,
    pageSize: number,
    openModel: string,
    ejectParameter: any) {
    const apiJson = {} as any;
    dataSource.tables.forEach((f: any) => {
        if (f.isMain) {
            apiJson[f.key] = {};
        }
        // else {
        //     if (!api<PERSON>son['join']) {
        //         apiJson['join'] = {};
        //     }
        //     apiJson['join'][f.key] = {};
        //     f.related.condition.forEach((s: any) => {
        //         apiJson['join'][f.key][s.left] = `${f.related.type === 'left' ? '>' : ''}${s.right}`;
        //     });
        // }
        apiJson[f.key]['@select'] = f.select.map((s: any) => {
            return `${s.key.replace(`${f.key}.`, '')}:${s.key.replace('.', '_')}`;
        }).join(',');
        f.where.forEach((s: any) => {
            let leftValue = s.leftValue;
            let rightValue = '';
            switch (s.rightValueType) {
                case 'search':
                    if (searchData[s.rightValue]) {
                        rightValue = searchData[s.rightValue];
                    }
                    break;
                case 'fixed':
                    rightValue = s.rightValue;
                    break;
                case 'parameter':
                    if (openModel === 'newTab') {
                        rightValue = routeQuery[s.rightValue] as string;
                    } else if (openModel === 'eject') {
                        const par = ejectParameter.find((m: any) =>
                            m.code === s.rightValue
                        );
                        rightValue = par ? par.actualValue : '';
                    }
                    break;
            }
            if (rightValue || (!rightValue && s.emptySearch)) {
                switch (s.operator) {
                    case 'include':
                        leftValue += '%';
                        rightValue = `%${rightValue}%`;
                        break;
                    default:
                        break;
                }
                apiJson[f.key][leftValue] = rightValue.toString();
            }
        });
    });
    dataSource.orderby.forEach((s: any) => {
        const keys = s.key.split('.');
        apiJson[keys[0]]['@order'] = apiJson[keys[0]]['@order']
            ? apiJson[keys[0]]['@order'] +
            `,${s.key}${s.sort === 'asc' ? '+' : '-'}`
            : `${s.key}${s.sort === 'asc' ? '+' : '-'}`;
    });
    const params = {
        '[]': {
            ...apiJson,
            page: pageIndex,
            count: hasPagination
                ? pageSize
                : 0,
            query: 2
        },
        'total@': '/[]/total'
    };
    return params;
}
function addJson(dataSource: any, searchData: any) {
    const apiJson: any = {};
    const columns: any = {};
    const mainTableId = guidHelper.generate();
    const mainTable = dataSource.tables.find(
        (f: any) => f.isMain
    );
    Object.keys(searchData[mainTable.key]).forEach(key => {
        columns[key.split('.')[1]] = searchData[mainTable.key][key];
    });
    columns[mainTable.primaryKey[0]] = mainTableId;
    apiJson[mainTable.key] = { data: [columns] };
    // const detailTable = dataSource.tables.filter(
    //     (f: any) => !f.isMain
    // );
    // detailTable.map((m: any) => {
    //     const table = searchData[m.key];
    //     table.map((t: any) => {
    //         const detailJson: any = {};
    //         detailJson[m.key] = {};
    //         Object.keys(t).map((o: any) => {
    //             if (o !== 'rowAction' && o !== 'rowStatus' && o !== 'defaultData') {
    //                 detailJson[m.key][o.split('.')[1]] = t[o];
    //             }
    //         });
    //         // 加入外键
    //         m.related.condition.map((c: any) => {
    //             const arrs1 = c.left.split('.');
    //             const arrs2 = c.right.split('.');
    //             if (arrs1[0] === m.key) {
    //                 detailJson[m.key][arrs1[1]] = mainJson[mainTable.key][arrs2[1]];
    //             } else if (arrs2[0] === m.key) {
    //                 detailJson[m.key][arrs2[1]] = mainJson[mainTable.key][arrs1[1]];
    //             }
    //         });
    //         detailJson[m.key][m.primaryKey[0]] = guidHelper.generate();
    //         apiJson.add.push(detailJson);
    //     });
    // });
    return apiJson;
}
function editJson(dataSource: any, searchData: any, defaultData: any) {
    const apiJson: any = {};
    const columns: any = {};
    const mainTable = dataSource.tables.find(
        (f: any) => f.isMain
    );
    Object.keys(searchData[mainTable.key]).forEach(key => {
        columns[key.split('.')[1]] = searchData[mainTable.key][key];
    });
    const where: any = `${mainTable.primaryKey[0]}=@${mainTable.primaryKey[0]}`;
    const params: any =  [{param: `@${mainTable.primaryKey[0]}`, value: defaultData[`${mainTable.key}_${mainTable.primaryKey[0]}`]}];
    apiJson[mainTable.key] = [{ data: columns, where: where, params: params }];
    // const detailTable = dataSource.tables.filter(
    //     (f: any) => !f.isMain
    // );
    // detailTable.map((m: any) => {
    //     const table = searchData[m.key];
    //     table.map((t: any) => {
    //         const detailJson: any = {};
    //         detailJson[m.key] = {};
    //         if (t.rowStatus === 'delete') {
    //             detailJson[m.key][m.primaryKey[0]] = t.defaultData[m.primaryKey[0]];
    //             apiJson.delete.push(detailJson);
    //         } else if (t.rowStatus === 'edit') {
    //             Object.keys(t).map((o: any) => {
    //                 if (o !== 'rowAction' && o !== 'rowStatus' && o !== 'defaultData') {
    //                     const arrs = o.split('.');
    //                     if (arrs[0] === m.key) {
    //                         detailJson[m.key][arrs[1]] = t[o];
    //                     }
    //                 }
    //             });
    //             detailJson[m.key][m.primaryKey[0]] = t.defaultData[`${m.key}_${m.primaryKey[0]}`];
    //             apiJson.edit.push(detailJson);
    //         } else if (t.rowStatus === 'add') {
    //             Object.keys(t).map((o: any) => {
    //                 if (o !== 'rowAction' && o !== 'rowStatus' && o !== 'defaultData') {
    //                     const arrs = o.split('.');
    //                     if (arrs[0] === m.key) {
    //                         detailJson[m.key][arrs[1]] = t[o];
    //                     }
    //                 }
    //             });
    //             // 加入外键
    //             m.related.condition.map((c: any) => {
    //                 const arrs1 = c.left.split('.');
    //                 const arrs2 = c.right.split('.');
    //                 if (arrs1[0] === m.key) {
    //                     detailJson[m.key][arrs1[1]] = defaultData[c.right.replace('.', '_')];
    //                 } else if (arrs2[0] === m.key) {
    //                     detailJson[m.key][arrs2[1]] = defaultData[c.left.replace('.', '_')];
    //                 }
    //             });
    //             detailJson[m.key][m.primaryKey[0]] = guidHelper.generate();
    //             apiJson.add.push(detailJson);
    //         }
    //     });
    // });
    return apiJson;
}

export default {
    selectJson,
    addJson,
    editJson
};
