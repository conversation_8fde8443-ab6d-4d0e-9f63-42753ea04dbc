import { Component, Vue } from 'vue-property-decorator';
import styles from './Init.module.less';
import { ActionEnum } from '@/PlatformStandard/services/menu';
import { WrappedFormUtils } from 'ant-design-vue/types/form/form';
import { GlobleSetting, CustomerSetting } from './types';
import { notificationHelper, formHelper, i18nHelper } from '@/PlatformStandard/common/utils';
import { initService } from './service';
import { FooterBottom } from '@/PlatformStandard/components/FooterBottom';
import { menuService, MenuViewState } from '@/PlatformStandard/services/menu';
import { notification } from 'ant-design-vue';

@Component({ components: { FooterBottom } })
export class InitManagement extends Vue {
  private actions = ActionEnum;
  private globleForm!: WrappedFormUtils;
  private globleSettings: GlobleSetting = {
    enableWatermarkValue: false,
    isShowSystemMenuValue: false,
    isShowSystemMenu: 'false'
  };
  private customerSettings: CustomerSetting = {
    enableWatermarkValue: false,
    isShowSystemMenuValue: false,
    isShowSystemMenu: 'false',
    buttonColor: '#2165d9',
    fontColor: '#2165d9',
    borderColor: '#2165d9',
    tableheadColor: '#dce4f1',
    heightLightColor: '#2165d9',
    fixedTopHeaderColorLeft: '#2165d9',
    fixedTopHeaderColorRight: '#1789ff'
  };
  private loading = false;
  private logoImageUrl = '';
  private logoThumbnailImageUrl = '';
  private customerlogoThumbnailImageUrl = '';
  private iconImageUrl = '';
  private logoImageUrlreception = '';
  private collapsed = menuService.menuViewState === MenuViewState.Collapsed;
  private groupValue = 'global';
  private saveData = {};
  // private buttonColor = '#2165d9';
  // private fontColor = '#2165d9';
  // private borderColor = '#2165d9';
  // private tableheadColor = '#dce4f1';   // .ant-table-body table>thead>tr>th
  // private heightLightColor = '#2165d9';
  // private fixedTopHeaderColorLeft = '#2165d9';
  // private fixedTopHeaderColorRight = '#1789ff';

  created() {
    this.globleForm = this.$form.createForm(this, { name: 'globleForm' });
    initService.getSettings('global').subscribe(rs => {
      this.globleSettings = rs;
      this.globleSettings.enableWatermarkValue = this.globleSettings.enableWatermark === 'true' ? true : false;
      if (this.globleSettings.logo) {
        this.logoImageUrl = this.globleSettings.logo;
        // initService.downloadPicture(this.globleSettings.logo).subscribe(img => {
        //   this.getBase64(this.globleSettings.logo, (imageUrl: any) => {
        //   });
        // });
      }
      if (this.globleSettings.logoThumbnail) {
        this.logoThumbnailImageUrl = this.globleSettings.logoThumbnail;
        // initService.downloadPicture(this.globleSettings.logoThumbnail).subscribe(img => {
        //   this.getBase64(img, (imageUrl: any) => {
        //   });
        // });
      }
      if (this.globleSettings.systemIcon) {
        this.iconImageUrl = this.globleSettings.systemIcon;
        // initService.downloadPicture(this.globleSettings.systemIcon).subscribe(img => {
        //   this.getBase64(img, (imageUrl: any) => {
        //   });
        // });
      }
      // if (this.globleSettings.logoReception) {
      //   initService.downloadPicture(this.globleSettings.logoReception).subscribe(img => {
      //     this.getBase64(img, (imageUrl: any) => {
      //       this.logoImageUrlreception = imageUrl;
      //     });
      //   });
      // }
    });
  }

  private saveGlobleSetting() {
    const errorMsgs = formHelper.validateForm(this.globleForm);

    if (!this.globleSettings.logo || this.globleSettings.logo === '') {
      errorMsgs.push(i18nHelper.getLocale(['controls.upload', 'platform.init.logo']));
    }

    if (!this.globleSettings.logoThumbnail || this.globleSettings.logoThumbnail === '') {
      errorMsgs.push(i18nHelper.getLocale(['controls.upload', 'platform.init.logoThumbnail']));
    }

    if (!this.globleSettings.systemIcon || this.globleSettings.systemIcon === '') {
      errorMsgs.push(i18nHelper.getLocale(['controls.upload', 'platform.init.icon']));
    }

    if (errorMsgs.length === 0) {
      // debugger
      this.globleSettings.enableWatermark = this.globleSettings.enableWatermarkValue ? 'true' : 'false';
      // delete this.globleSettings['enableWatermarkValue'];
      this.globleSettings.isShowSystemMenu = this.globleSettings.isShowSystemMenuValue ? 'true' : 'false';

      if (this.groupValue === 'global') {
        this.saveData = this.globleSettings;
      } else if (this.groupValue === 'customer') {
        this.saveData = this.customerSettings;
      }
      initService.submitSettings(this.groupValue, this.saveData).subscribe(() => {
        notificationHelper.success(i18nHelper.getLocale('messages.success'));
      });
    } else {
      notificationHelper.error(errorMsgs);
    }
  }

  private beforeLogoUpload(file: any) {
    const isPermissionType = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/svg+xml';
    if (!isPermissionType) {
      notificationHelper.error(i18nHelper.getLocale('platform.init.message.onlyJpg'));
    }
    const isLt150k = file.size / 1024 < 150;
    if (!isLt150k) {
      notificationHelper.error(i18nHelper.getLocale('platform.init.message.imageSize'));
    }
    return isPermissionType && isLt150k;
  }

  private logoUpload(fileInfo: any) {
    return null;
    // if (fileInfo.file) {
    //   const formData = new FormData();
    //   formData.append('file', fileInfo.file);
    //   formData.append('directoryName', 'SystemInit/Logo');
    //   initService.uploadPicture(formData).subscribe(rs => {
    //     this.globleSettings.logo = rs.filePath;
    //   });
    // }
  }

  private logoChange(info: any) {
    if (info.file && info.file.originFileObj) {
      this.getBase64(info.file.originFileObj, (imageUrl: any) => {
        this.globleSettings.logo = this.logoImageUrl = imageUrl;
        this.loading = false;
      });
    }
  }

  private getBase64(img: any, callback: any) {
    const reader = new FileReader();
    reader.addEventListener('load', () => callback(reader.result));
    reader.readAsDataURL(img);
  }

  private logoThumbnailUpload(fileInfo: any) {
    return null;
    // if (fileInfo.file) {
    //   const formData = new FormData();
    //   formData.append('file', fileInfo.file);
    //   formData.append('directoryName', 'SystemInit/LogoThumbnail');
    //   initService.uploadPicture(formData).subscribe(rs => {
    //     this.globleSettings.logoThumbnail = rs.filePath;
    //   });
    // }
  }

  private customerlogoThumbnailUpload(fileInfo: any) {
    return null;
    // if (fileInfo.file) {
    //   const formData = new FormData();
    //   formData.append('file', fileInfo.file);
    //   formData.append('directoryName', 'SystemInit/customerLogoThumbnail');
    //   initService.uploadPicture(formData).subscribe(rs => {
    //     this.customerSettings.logoThumbnail = rs.filePath;
    //   });
    // }
  }

  private logoThumbnailChange(info: any) {
    if (info.file && info.file.originFileObj) {
      this.getBase64(info.file.originFileObj, (imageUrl: any) => {
        this.globleSettings.logoThumbnail = this.logoThumbnailImageUrl = imageUrl;
        this.loading = false;
      });
    }
  }
  private customerlogoThumbnailChange(info: any) {
    if (info.file && info.file.originFileObj) {
      this.getBase64(info.file.originFileObj, (imageUrl: any) => {
        this.customerSettings.logoThumbnail = this.customerlogoThumbnailImageUrl = imageUrl;
        this.loading = false;
      });
    }
  }
  private logoReceptionChange(info: any) {
    if (info.file && info.file.originFileObj) {
      this.getBase64(info.file.originFileObj, (imageUrl: any) => {
        this.customerSettings.logoReception = this.logoImageUrlreception = imageUrl;
        this.loading = false;
      });
    }
  }

  private receptionUpload(fileInfo: any) {
    return null;
    // if (fileInfo.file) {
    //   const formData = new FormData();
    //   formData.append('file', fileInfo.file);
    //   formData.append('directoryName', 'SystemInit/Recepetion');
    //   initService.uploadPicture(formData).subscribe(rs => {
    //     this.customerSettings.logoReception = rs.filePath;
    //   });
    // }
  }
  private beforeIconUpload(file: any) {
    const isPermissionType = file.type === 'image/x-icon';
    if (!isPermissionType) {
      notificationHelper.error(i18nHelper.getLocale('platform.init.message.onlyIcon'));
    }
    const isLt150k = file.size / 1024 < 150;
    if (!isLt150k) {
      notificationHelper.error(i18nHelper.getLocale('platform.init.message.imageSize'));
    }
    return isPermissionType && isLt150k;
  }

  private iconUpload(fileInfo: any) {
    return null;
    // if (fileInfo.file) {
    //   const formData = new FormData();
    //   formData.append('file', fileInfo.file);
    //   formData.append('directoryName', 'SystemInit/Icon');
    //   initService.uploadPicture(formData).subscribe(rs => {
    //     this.globleSettings.systemIcon = rs.filePath;
    //   });
    // }
  }

  private iconChange(info: any) {
    if (info.file && info.file.originFileObj) {
      this.getBase64(info.file.originFileObj, (imageUrl: any) => {
        this.globleSettings.systemIcon = this.iconImageUrl = imageUrl;
        this.loading = false;
      });
    }
  }
  private waterTypeClick(e: any) {
    this.globleSettings.enableWatermarkValue = e;
  }
  private customerwaterTypeClick(e: any) {
    this.customerSettings.enableWatermarkValue = e;
  }
  private tabClick(e: any) {
    this.groupValue = e;
    // console.log(this.groupValue);
    if (e === 'customer') {
      initService.getSettings(e).subscribe(rs => {
        this.customerSettings = rs;
        this.customerSettings.enableWatermarkValue = this.customerSettings.enableWatermark === 'true' ? true : false;
        this.customerSettings.isShowSystemMenuValue = this.customerSettings.isShowSystemMenu === 'true' ? true : false;
        // this.customerSettings.enableWatermarkValue = this.customerSettings.enableWatermark === 'true' ? true : false;
        if (this.customerSettings.logoReception) {
          initService.downloadPicture(this.customerSettings.logoReception).subscribe(img => {
            this.getBase64(img, (imageUrl: any) => {
              this.logoImageUrlreception = imageUrl;
            });
          });
        }
        if (this.customerSettings.logoThumbnail) {
          initService.downloadPicture(this.customerSettings.logoThumbnail).subscribe(img => {
            this.getBase64(img, (imageUrl: any) => {
              this.customerlogoThumbnailImageUrl = imageUrl;
            });
          });
        }
      });
    }
  }
  render() {
    return <div style='background: #fff;position:relative;overflow:hidden; width: 100%;'>
      <a-card>
        <a-tabs default-active-key='global' on-tabClick={this.tabClick}>
          <a-tab-pane key='1' tab='全局'>
            <a-form style='padding-top:20px' form={this.globleForm} labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
              {/* <div class={styles.actions}>
                <a-button
                  type='primary'
                  v-permission={this.actions.save}
                  class='ml-1'
                  on-click={this.saveGlobleSetting}>
                  {this.$t('buttons.save')}
                </a-button>
              </div> */}
              <a-row>
                <a-col span='8'>
                  <a-form-item label={this.$t('platform.init.name')}>
                    <a-input
                      on-change={(e: any) => { this.globleSettings.systemName = e.target.value; }}
                      placeholder={this.$l.getLocale(['controls.input', 'platform.init.name'])}
                      v-decorator={['systemName', {
                        initialValue: this.globleSettings.systemName,
                        rules: [{
                          required: true,
                          message: this.$l.getLocale(['controls.input', 'platform.init.name'])
                        }]
                      }]}
                    ></a-input>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col span='8'>
                  <a-form-item label={this.$t('platform.init.logo')}>
                    <a-upload
                      name='logo'
                      list-type='picture-card'
                      class='logouploader'
                      show-upload-list={false}
                      before-upload={(file: any) => this.beforeLogoUpload(file)}
                      customRequest={(file: any) => this.logoUpload(file)}
                      on-change={(info: any) => this.logoChange(info)}
                    >
                      {
                        this.logoImageUrl ?
                          <img src={this.logoImageUrl} alt='logo' style='width:128px; max-height:33.72px;' />
                          :
                          <div>
                            <a-icon type={this.loading ? 'loading' : 'plus'} />
                            <div>
                              {this.$t('buttons.upload')}
                            </div>
                          </div>
                      }
                    </a-upload>
                    <span style='color: red'>{this.$t('platform.init.helpText.logo.admin')}</span>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col span='8'>
                  <a-form-item label={this.$t('platform.init.logoThumbnail')}>
                    <a-upload
                      name='logoThumbnail'
                      list-type='picture-card'
                      class='logoThumbnailuploader'
                      show-upload-list={false}
                      before-upload={(file: any) => this.beforeLogoUpload(file)}
                      customRequest={(file: any) => this.logoThumbnailUpload(file)}
                      on-change={(info: any) => this.logoThumbnailChange(info)}
                    >
                      {
                        this.logoThumbnailImageUrl ?
                          <img src={this.logoThumbnailImageUrl} alt='logo' style='width:30px; max-height:30px;' />
                          :
                          <div>
                            <a-icon type={this.loading ? 'loading' : 'plus'} />
                            <div>
                              {this.$t('buttons.upload')}
                            </div>
                          </div>
                      }
                    </a-upload>
                    <span style='color: red'>{this.$t('platform.init.helpText.logoThumbnail.admin')}</span>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col span='8'>
                  <a-form-item label={this.$t('platform.init.icon')}>
                    <a-upload
                      name='icon'
                      list-type='picture-card'
                      class='logoThumbnailuploader'
                      show-upload-list={false}
                      before-upload={(file: any) => this.beforeIconUpload(file)}
                      customRequest={(file: any) => this.iconUpload(file)}
                      on-change={(info: any) => this.iconChange(info)}
                    >
                      {
                        this.iconImageUrl ?
                          <img src={this.iconImageUrl} alt='logo' style='width:30px; max-height:30px;' />
                          :
                          <div>
                            <a-icon type={this.loading ? 'loading' : 'plus'} />
                            <div>
                              {this.$t('buttons.upload')}
                            </div>
                          </div>
                      }
                    </a-upload>
                    <span style='color: red'>{this.$t('platform.init.helpText.icon')}</span>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col span='8'>
                  <a-form-item label={this.$t('platform.init.enableWatermark')}>
                    <a-switch v-model={this.globleSettings.enableWatermarkValue} on-click={this.waterTypeClick} />
                  </a-form-item>
                </a-col>
              </a-row>
              {
                this.globleSettings.enableWatermarkValue ?
                  <a-row>
                    <a-col span='8'>
                      <a-form-item label={this.$t('platform.init.watermarkType')} required>
                        <a-select
                          allowClear
                          placeholder={this.$l.getLocale(['controls.select', 'platform.init.watermarkType'])}
                          on-change={(value: any) => {
                            this.globleSettings.watermarkType = value;
                          }}
                          v-decorator={['watermarkType', {
                            initialValue: this.globleSettings.watermarkType,
                            rules: [{
                              required: true,
                              message: this.$l.getLocale(['controls.select', 'platform.init.watermarkType'])
                            }]
                          }]}
                        >
                          {(this.$t('platform.init.watermarkTypeDataset') as any).map((v: any) => (
                            <a-select-option value={v.value}>{v.label}</a-select-option>
                          ))}
                        </a-select>
                      </a-form-item>
                    </a-col>
                  </a-row>
                  :
                  null
              }
              {
                this.globleSettings.enableWatermarkValue && this.globleSettings.watermarkType === '99' ?
                  <a-row>
                    <a-col span='8'>
                      <a-form-item label={this.$t('platform.init.watermark')} required>
                        <a-input
                          on-change={(e: any) => { this.globleSettings.watermark = e.target.value; }}
                          placeholder={this.$l.getLocale(['controls.input', 'platform.init.watermark'])}
                          v-decorator={['watermark', {
                            initialValue: this.globleSettings.watermark,
                            rules: [{
                              required: true,
                              message: this.$l.getLocale(['controls.input', 'platform.init.watermark'])
                            }]
                          }]}
                        ></a-input>
                      </a-form-item>
                    </a-col>
                  </a-row>
                  :
                  null
              }
            </a-form>
          </a-tab-pane>
          {<a-tab-pane key='customer' tab='前台'>
            <a-form style='padding-top:20px' form={this.globleForm} labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
              <a-row>
                <a-col span='8'>
                  <a-form-item label='主logo' required>
                    <a-upload
                      name='logo'
                      list-type='picture-card'
                      class='logouploader'
                      show-upload-list={false}
                      before-upload={(file: any) => this.beforeLogoUpload(file)}
                      customRequest={(file: any) => this.receptionUpload(file)}
                      on-change={(info: any) => this.logoReceptionChange(info)}
                    >
                      {
                        this.logoImageUrlreception ?
                          <img src={this.logoImageUrlreception} alt='logo' style='width:128px; max-height:33.72px;' />
                          :
                          <div>
                            <a-icon type={this.loading ? 'loading' : 'plus'} />
                            <div>
                              {this.$t('buttons.upload')}
                            </div>
                          </div>
                      }
                    </a-upload>
                    <span style='color: red'>{this.$t('platform.init.helpText.logo.admin')}</span>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col span='8'>
                  <a-form-item label={this.$t('platform.init.logoThumbnail')}>
                    <a-upload
                      name='logoThumbnail'
                      list-type='picture-card'
                      class='logoThumbnailuploader'
                      show-upload-list={false}
                      before-upload={(file: any) => this.beforeLogoUpload(file)}
                      customRequest={(file: any) => this.customerlogoThumbnailUpload(file)}
                      on-change={(info: any) => this.customerlogoThumbnailChange(info)}
                    >
                      {
                        this.customerlogoThumbnailImageUrl ?
                          <img src={this.customerlogoThumbnailImageUrl} alt='logo' style='width:30px; max-height:30px;' />
                          :
                          <div>
                            <a-icon type={this.loading ? 'loading' : 'plus'} />
                            <div>
                              {this.$t('buttons.upload')}
                            </div>
                          </div>
                      }
                    </a-upload>
                    <span style='color: red'>{this.$t('platform.init.helpText.logoThumbnail.admin')}</span>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col span='8'>
                  <a-form-item label='是否显示系统菜单' required>
                    <a-switch v-model={this.customerSettings.isShowSystemMenuValue} />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col span='8'>
                  <a-form-item label={this.$t('platform.init.enableWatermark')}>
                    <a-switch v-model={this.customerSettings.enableWatermarkValue} on-click={this.customerwaterTypeClick} />
                  </a-form-item>
                </a-col>
              </a-row>
              {
                this.customerSettings.enableWatermarkValue ?
                  <a-row>
                    <a-col span='8'>
                      <a-form-item label={this.$t('platform.init.watermarkType')} required>
                        <a-select
                          allowClear
                          placeholder={this.$l.getLocale(['controls.select', 'platform.init.watermarkType'])}
                          on-change={(value: any) => {
                            this.customerSettings.watermarkType = value;
                          }}
                          v-decorator={['watermarkType', {
                            initialValue: this.customerSettings.watermarkType,
                            rules: [{
                              required: true,
                              message: this.$l.getLocale(['controls.select', 'platform.init.watermarkType'])
                            }]
                          }]}
                        >
                          {(this.$t('platform.init.watermarkTypeDataset') as any).map((v: any) => (
                            <a-select-option value={v.value}>{v.label}</a-select-option>
                          ))}
                        </a-select>
                      </a-form-item>
                    </a-col>
                  </a-row>
                  :
                  null
              }
              {
                this.customerSettings.enableWatermarkValue && this.customerSettings.watermarkType === '99' ?
                  <a-row>
                    <a-col span='8'>
                      <a-form-item label={this.$t('platform.init.watermark')} required>
                        <a-input
                          on-change={(e: any) => { this.customerSettings.watermark = e.target.value; }}
                          placeholder={this.$l.getLocale(['controls.input', 'platform.init.watermark'])}
                          v-decorator={['watermark', {
                            initialValue: this.customerSettings.watermark,
                            rules: [{
                              required: true,
                              message: this.$l.getLocale(['controls.input', 'platform.init.watermark'])
                            }]
                          }]}
                        ></a-input>
                      </a-form-item>
                    </a-col>
                  </a-row>
                  :
                  null
              }
              <a-row>
                <a-col span='8'>
                  <a-form-item label='按钮配色' required>
                    <a-input v-model={this.customerSettings.buttonColor} placeholder='例：#2165d9' allow-clear />
                  </a-form-item>
                </a-col>
                <a-col span='2'>
                  <span class={styles.colorSpan} style={'background:' + this.customerSettings.buttonColor + ';'}></span>
                </a-col>
              </a-row>
              <a-row>
                <a-col span='8'>
                  <a-form-item label='字体配色' required>
                    <a-input v-model={this.customerSettings.fontColor} placeholder='例：#2165d9' allow-clear />
                  </a-form-item>
                </a-col>
                <a-col span='2'>
                  <span class={styles.colorSpan} style={'background:' + this.customerSettings.fontColor + ';'}></span>
                </a-col>
              </a-row>
              <a-row>
                <a-col span='8'>
                  <a-form-item label='边框配色' required>
                    <a-input v-model={this.customerSettings.borderColor} placeholder='例：#2165d9' allow-clear />
                  </a-form-item>
                </a-col>
                <a-col span='2'>
                  <span class={styles.colorSpan} style={'background:' + this.customerSettings.borderColor + ';'}></span>
                </a-col>
              </a-row>
              <a-row>
                <a-col span='8'>
                  <a-form-item label='表格表头配色' required>
                    <a-input v-model={this.customerSettings.tableheadColor} placeholder='例：#dce4f1' allow-clear />
                  </a-form-item>
                </a-col>
                <a-col span='2'>
                  <span class={styles.colorSpan} style={'background:' + this.customerSettings.tableheadColor + ';'}></span>
                </a-col>
              </a-row>
              <a-row>
                <a-col span='8'>
                  <a-form-item label='高亮' required>
                    <a-input v-model={this.customerSettings.heightLightColor} placeholder='例：#2165d9' allow-clear />
                  </a-form-item>
                </a-col>
                <a-col span='2'>
                  <span class={styles.colorSpan} style={'background:' + this.customerSettings.heightLightColor + ';'}></span>
                </a-col>
              </a-row>
              <a-row>
                <a-col span='8'>
                  <a-form-item label='详情页吸顶' required>
                    <a-input v-model={this.customerSettings.fixedTopHeaderColorLeft} placeholder='例：#2165d9' allow-clear />
                    <a-input v-model={this.customerSettings.fixedTopHeaderColorRight} placeholder='例：#1789ff' allow-clear />
                  </a-form-item>
                </a-col>
                <a-col span='2'>
                  <span class={styles.colorSpan} style={'background:' + this.customerSettings.fixedTopHeaderColorLeft + ';'}></span>
                  <span class={styles.colorSpan} style={'background:' + this.customerSettings.fixedTopHeaderColorRight + ';'}></span>
                </a-col>
              </a-row>
            </a-form>
          </a-tab-pane>
         /*  <a-tab-pane key='3' tab='移动端'>
            Content of Tab Pane 3
          </a-tab-pane>
          <a-tab-pane key='4' tab='后台'>
            Content of Tab Pane 4
          </a-tab-pane> */}
        </a-tabs>
      </a-card>
      <div style='width:100%;overflow:hidden'>
        <footer-bottom>
          <template slot='extra'>
            <a-button
              type='primary'
              v-permission={this.actions.save}
              class={styles.common_btn}
              on-click={this.saveGlobleSetting}>
              {this.$t('buttons.save')}
            </a-button>
          </template>
        </footer-bottom>
      </div>
    </div>;
  }
}
