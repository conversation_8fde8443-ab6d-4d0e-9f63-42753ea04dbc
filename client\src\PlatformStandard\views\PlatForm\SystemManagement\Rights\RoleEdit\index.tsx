import { form<PERSON><PERSON><PERSON>, i18n<PERSON>elper } from '@/PlatformStandard/common/utils';
import { WrappedFormUtils } from 'ant-design-vue/types/form/form';
import { Component, Prop, Vue } from 'vue-property-decorator';
import { RoleDto } from '../types';
import { dictionaryService } from '@/PlatformStandard/services/dictionary';
import { roleService } from '../../../AuthorityManagement/DataAuthority/Role/service';
import { rightService } from '../service';
import { commonService } from '@/PlatformStandard/services/common';

@Component
export class RoleEdit extends Vue {
  @Prop() selectRole!: RoleDto;
  private currentRole: RoleDto = {};
  private form!: WrappedFormUtils;
  private statusDataset = i18nHelper.getLocaleObject('platform.role.inUseDataset');
  private roleTypeDataset: any = [];
  private applicationDataset: any = [];

  save(): RoleDto {
    return this.currentRole;
  }

  validateForm(): string[] {
    return formHelper.validateForm(this.form);
  }

  created() {
    this.form = this.$form.createForm(this, { name: 'roleForm' });
    if (this.selectRole.roleId) {
      this.currentRole = JSON.parse(JSON.stringify(this.selectRole));
      rightService.getRoleApplications(this.currentRole.roleId as string).subscribe(rs => {
        this.currentRole.applicationIds = rs;
      });
    } else {
      this.currentRole = {
        roleType: 'C',
        status: 1,
        applicationIds: []
      };
    }
    dictionaryService.groupDictionary('RoleType').subscribe(rs => {
      this.roleTypeDataset = rs;
    });
    commonService.getApplications().subscribe(rs => {
      this.applicationDataset = rs;
    });
  }

  render() {
    return (
      <div>
        <a-form form={this.form} labelCol={{ span: 5 }} wrapperCol={{ span: 16 }}>
          <a-row>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.fields.code')} required>
                <a-input on-change={(e: any) => { this.currentRole.roleCode = e.target.value; }}
                  placeholder={this.$l.getLocale(['controls.input', 'platform.fields.code'])}
                  v-decorator={['roleCode', {
                    initialValue: this.currentRole.roleCode,
                    rules: [{ required: true, message: this.$l.getLocale(['controls.input', 'platform.fields.code']) }]
                  }]} />
              </a-form-item>
            </a-col>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.fields.name')} required>
                <a-input on-change={(e: any) => { this.currentRole.roleName = e.target.value; }}
                  placeholder={this.$l.getLocale(['controls.input', 'platform.fields.name'])}
                  v-decorator={['roleName', {
                    initialValue: this.currentRole.roleName,
                    rules: [{ required: true, message: this.$l.getLocale(['controls.input', 'platform.fields.name']) }]
                  }]} />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.fields.status')}>
                <a-select
                  placeholder={this.$l.getLocale(['controls.select', 'platform.fields.status'])}
                  style='width:100%;'
                  on-change={(value: any) => { this.currentRole.status = value; }}
                  v-decorator={[
                    'status',
                    {
                      initialValue: this.currentRole.status,
                      rules: [{
                        required: true,
                        message: this.$l.getLocale(['controls.select', 'platform.fields.status'])
                      }]
                    }]}
                >
                  {this.statusDataset.map((item: any) => (
                    <a-select-option value={item.value}>{item.label}</a-select-option>
                  ))}
                </a-select>
              </a-form-item>
            </a-col>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.fields.type')}>
                <a-select
                  placeholder={this.$l.getLocale(['controls.select', 'platform.fields.type'])}
                  style='width:100%;'
                  on-change={(value: any) => { this.currentRole.roleType = value; }}
                  v-decorator={[
                    'roleType',
                    {
                      initialValue: this.currentRole.roleType,
                      rules: [{
                        required: true,
                        message: this.$l.getLocale(['controls.select', 'platform.fields.type'])
                      }]
                    }]}
                >
                  {this.roleTypeDataset.map((item: any) => (
                    <a-select-option value={item.code}>{item.label}</a-select-option>
                  ))}
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
          <a-col span='12'>
              <a-form-item label={this.$t('platform.fields.application')}>
                <a-select
                  mode='tags'
                  placeholder={this.$l.getLocale(['controls.select', 'platform.fields.application'])}
                  style='width:100%;'
                  v-model={this.currentRole.applicationIds}
                >
                  {this.applicationDataset.map((item: any) => (
                    <a-select-option value={item.value}>{item.label}</a-select-option>
                  ))}
                </a-select>
              </a-form-item>
            </a-col>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.fields.description')}>
                <a-textarea v-model={this.currentRole.description}
                  placeholder={this.$l.getLocale(['controls.input', 'platform.fields.description'])} rows='4' />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
    );
  }
}
