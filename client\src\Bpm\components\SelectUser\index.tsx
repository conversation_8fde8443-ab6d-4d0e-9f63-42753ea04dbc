import { Component, Vue, Prop, Emit, Watch } from 'vue-property-decorator';
import { ValueLabelPair } from '@/PlatformStandard/common/defines';
import styles from './select-user.module.less';
import { departmentService } from './service';
import { organizationService } from '@/Bpm/services/organization';
import { getComponentFromProp } from '@/PlatformStandard/common/utils';
import { OrganizationDto, OrganizationTree } from './types';

@Component({
  // mixins: [InstanceParamMixin],
})
export class SelectUser extends Vue {
  @Prop() test!: boolean;
  @Prop() title!: string;
  @Prop() visible!: boolean;
  // private visible = false;
  private keyword = '';
  private treeData = [{}];
  private lstOrgLevel: ValueLabelPair[] = [];
  private selectDepart: OrganizationDto = {};
  private userArry: any = [];
  private checkedList: any = [];
  private checkedListChecked: any = [];
  private titleDom: any;

  @Emit('closepop')

  private closepop(result: boolean, resultList: any) {
  }
  private onSearch() {
    departmentService.getUserList(this.keyword).subscribe(rs => {
      console.log(rs);
      // this.userArry = rs;
    });
  }
  private onExpand(expandedKeys: string, e: any) {
    const params = { 'parent-id': e.node.dataRef.key};
    if (e.node.dataRef.children) {
      return;
    }
    departmentService.getDepartmentTree(params).subscribe(rs => {
      e.node.dataRef.children = this.transformTreeData(rs);
      if (rs.length === 0) {
        e.node.dataRef.isLeaf = true;
      }
      this.treeData = [...this.treeData];
    });
  }
  private onSelect(selectedKeys: string, e: any) {
    if (selectedKeys && selectedKeys.length > 0 && String(e.node.dataRef.datum) === 'department') {
      departmentService.getOrganization(selectedKeys[0]).subscribe(rs => {
        this.selectDepart = rs;
        const orgLevel = this.lstOrgLevel.find(d => d.value === this.selectDepart.tagId);
        if (orgLevel) {
          this.selectDepart.tagName = orgLevel.label;
        }
      });
      departmentService.getUserList(selectedKeys[0]).subscribe(rs => {
        this.userArry = rs;
      });
    } else {
      this.selectDepart = {};
    }
  }
  private transformTreeData(data: OrganizationTree[]) {
    return data.map(o => ({
      title: o.label,
      key: o.value,
      datum: o.childCount,
      isLeaf: o.childCount === 0,
      slots: { icon: o.childCount === 0 ? 'file' : 'folder' },
    }));
  }
  private onChange(checkedValues: any) {
    console.log('checked = ', checkedValues);
    this.checkedList = checkedValues;
  }
  private handleClose(e: any) {
    this.checkedList.splice(this.checkedList.findIndex((d: any) => d.value === e.value), 1);
    this.checkedList = this.checkedList;
  }
  private handleCancel() {
    this.checkedList = [];
    this.closepop(false, this.checkedList);
  }
  private handleOk() {
    this.closepop(false, this.checkedList);
  }
  created() {
    const params = { 'parent-id': '', 'parent-type': 'company' };
    departmentService.getDepartmentTree(params).subscribe(rs => {
      this.treeData = this.transformTreeData(rs);
    });
    organizationService.getOrgLevelTags().subscribe(data => {
      this.lstOrgLevel = data;
    });
    const titleDom = getComponentFromProp(this, 'title');
    this.titleDom = titleDom;
    const visible = getComponentFromProp(this, 'visible');
    this.visible = visible;
    console.log(this.visible);
  }
  render() {
    return (
      <div>
        {/* {this.titleDom}
        {this.visible} */}
        <a-modal
          width='680px'
          title={this.$t('bpm.form.user')}
          visible={this.visible}
          on-cancel={this.handleCancel}
          on-ok={this.handleOk}>
           <div>
             <a-input style='width:88%' placeholder={this.$l.getLocale('controls.input')} v-model={this.keyword} />
             <a-button type='primary' icon='search' html-type='submit' on-click={this.onSearch}>
              {this.$l.getLocale('buttons.search')}
            </a-button>
           </div>
           <div class={styles.main}>
             <div class={styles.left}>
             <a-tree on-expand={this.onExpand} on-select={this.onSelect} tree-data={this.treeData} show-icon>
              <a-icon slot='switcherIcon' type='down' /><a-icon slot='company' type='contacts' />
              <a-icon slot='department' type='team' />
            </a-tree>
             </div>
             <div class={styles.right}>
                <div class={styles.rightTop}>
                  <a-checkbox-group on-change={this.onChange} v-model={this.checkedListChecked}>
                    { this.userArry.map((item: any) => {
                      return(
                        <a-checkbox value={item}>{item.label}</a-checkbox>
                        );
                    }) }
                  </a-checkbox-group>
                </div>
                <div class={styles.rightBottom}>
                  {
                    this.checkedList.map((item: { label: any; value: any }) => (
                      [console.log(item),
                       <a-tag closable
                         key={item.value}
                         on-close={() => this.handleClose(item)}
                         >{item.label}</a-tag>]
                    ))
                  }
                </div>
             </div>
           </div>
         </a-modal>
      </div>
    );
  }
}
