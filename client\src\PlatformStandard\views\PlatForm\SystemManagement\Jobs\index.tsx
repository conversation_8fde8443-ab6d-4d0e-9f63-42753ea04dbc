import { i18n<PERSON>elper, notification<PERSON><PERSON><PERSON>, toCasedStyleObject } from '@/PlatformStandard/common/utils';
import { CompCard, CompTableHeader } from '@/PlatformStandard/components';
import { Component, Vue } from 'vue-property-decorator';
import { jobsService } from './service';
import { JobDto, JobQueryDto } from './types';
import styles from './Jobs.module.less';
import { JobDetail } from './Detail';
import { languageService } from '@/PlatformStandard/services/language';

@Component({ components: { CompCard, CompTableHeader, JobDetail } })
export class JobsManagement extends Vue {
  private query: JobQueryDto = {};
  private pagination = {
    total: 0,
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    size: 'default',
    showTotal: (total: string) =>
      this.$l
        .getLocale('paginations.total')
        .toString()
        .replace('{{value}}', total),
  };

  private dataSource: JobDto[] = [];
  private jobStatusSource = i18nHelper.getLocaleObject('platform.jobManage.jobStatus');
  private selectedRowKeys: string[] = [];
  private selectedRows: JobDto[] = [];

  private columns = [
    {
      dataIndex: 'queueName',
      slots: { title: 'queueName' },
      width: '15%'
    },
    {
      dataIndex: 'jobName',
      slots: { title: 'jobName' },
      // width: '20%'
    },
    {
      dataIndex: 'createdAt',
      slots: { title: 'createdAt' },
      width: '15%'
    },
    {
      dataIndex: 'finishAt',
      slots: { title: 'finishAt' },
      // scopedSlots: { customRender: 'finishTime' },
      width: '15%'
    },
    {
      dataIndex: 'stateName',
      slots: { title: 'stateName' },
      scopedSlots: { customRender: 'stateName' },
      width: '10%'
    },
    {
      dataIndex: 'action',
      slots: { title: 'action' },
      scopedSlots: { customRender: 'action' },
      width: '10%'
    },
  ];
  private fieldsSlotMap: any = {};
  private selectJob: JobDto = {};
  private jobDetailShow = false;

  private loadData(reset: boolean = false) {
    if (reset) {
      this.pagination.current = 1;
      this.selectedRowKeys = [];
      this.selectedRows = [];
    }
    const params = { 'page-index': this.pagination.current, 'page-size': this.pagination.pageSize };
    jobsService.getJobs({ ...toCasedStyleObject(this.query), ...params }).subscribe(data => {
      this.dataSource = data.items;
      this.pagination.total = data.total;
      this.$forceUpdate();
    });
  }

  private detail(job: JobDto) {
    this.selectJob = job;
    this.jobDetailShow = true;
  }

  private retryJob(job: JobDto) {
    jobsService.retryJobs([String(job.jobId)]).subscribe(() => {
      this.loadData(true);
      notificationHelper.success(i18nHelper.getLocale('messages.success'));
    });
  }

  private onRoleSelectChange(selectedRowKeys: any, selectedRows: any) {
    this.selectedRowKeys = selectedRowKeys;
    this.selectedRows = selectedRows;
  }

  private batchRetry() {
    if (this.selectedRows.length === 0) {
      notificationHelper.error(i18nHelper.getLocale('controls.selectItems'));
      return;
    }
    const jobIds: string[] = [];
    this.selectedRows.forEach(data => {
      if (data.stateName === 'Failed') {
        jobIds.push(String(data.jobId));
      }
    });
    if (jobIds.length > 0) {
      jobsService.retryJobs(jobIds).subscribe(() => {
        this.loadData(true);
        notificationHelper.success(i18nHelper.getLocale('messages.success'));
      });
    } else {
      notificationHelper.error(i18nHelper.getLocale('controls.selectItems'));
      return;
    }
  }

  created() {
    this.loadData();

    languageService.language$.subscribe(() => {
      this.jobStatusSource = i18nHelper.getLocaleObject('platform.jobManage.jobStatus');
    });

    this.fieldsSlotMap['stateName'] = (text: JobDto[], record: JobDto, index: number) => {
      return (
        <div>
          {record.stateName === 'Failed' ?
            <a-tag color='red'> {this.jobStatusSource.find((d: any) => d.value === record.stateName).label} </a-tag>
            : (record.stateName === 'Succeeded' ?
              <a-tag color='green'> {this.jobStatusSource.find((d: any) => d.value === record.stateName).label} </a-tag>
              : <a-tag color='blue'> {this.jobStatusSource.find((d: any) => d.value === record.stateName).label} </a-tag>)}
        </div>
      );
    };

    this.fieldsSlotMap['action'] = (text: JobDto[], record: JobDto, index: number) => {
      return (
        <div>
          <span>
            <a-button type='link' on-click={() => this.detail(record)} size='small' class={styles.list_button}>
              {this.$l.getLocale('buttons.read')}
            </a-button>
          </span>
          {
            record.stateName === 'Failed' ?
              <span>
                <a-button type='link' on-click={() => this.retryJob(record)} size='small' class={styles.list_button}>
                  {this.$l.getLocale('buttons.retry')}
                </a-button>
              </span>
              :
              null
          }
        </div>
      );
    };
  }

  render() {
    return (
      <div>
        <comp-card class={styles.card_top}>
          <comp-table-header
            hideReset={true}
            on-search={() => {
              this.loadData(true);
            }}
          >
            <template slot='base'>
              <a-row>
                <a-row>
                  {/* <a-col span='6' class='mr-1'>
                    <a-form-item label={this.$t('platform.jobManage.jobName')}>
                      <a-input v-model={this.query.jobName} placeholder={this.$t('platform.jobManage.jobName')}></a-input>
                    </a-form-item>
                  </a-col> */}
                  <a-col span='6' class='mr-1'>
                    <a-form-item label={this.$t('platform.jobManage.keyWords')}>
                      <a-input v-model={this.query.keyWord} placeholder={this.$t('platform.jobManage.keyWords')}></a-input>
                    </a-form-item>
                  </a-col>
                  <a-col span='6' class='mr-1'>
                    <a-form-item label={this.$t('platform.fields.status')}>
                      <a-select v-model={this.query.jobStatus} placeholder={this.$t('platform.fields.status')} allowClear>
                        {this.jobStatusSource.map((item: any) => (
                          <a-select-option value={item.value}>{item.label}</a-select-option>
                        ))}
                      </a-select>
                    </a-form-item>
                  </a-col>
                  {/* <a-col span='6' class='mr-1'>
                    <a-form-item label={this.$t('platform.jobManage.jobData')}>
                      <a-input v-model={this.query.jobParameter} placeholder={this.$t('platform.jobManage.jobData')}></a-input>
                    </a-form-item>
                  </a-col> */}
                </a-row>
              </a-row>
            </template>
          </comp-table-header>
        </comp-card>
        <comp-card>
          <a-card class={styles.base_table} bordered={false}>
            <div slot='extra'>
              {
                this.query.jobStatus === 'Failed' ?
                  <a-button type='primary' class={styles.common_btn}
                    on-click={() => this.batchRetry()}>{this.$t('buttons.batchRetry')}</a-button>
                  :
                  ''
              }
            </div>
            <a-table
              size='small'
              row-selection={{
                selectedRowKeys: this.selectedRowKeys,
                onChange: this.onRoleSelectChange
              }}
              columns={this.columns}
              data-source={this.dataSource}
              pagination={this.pagination}
              scopedSlots={this.fieldsSlotMap}
              on-change={(pagination: any) => {
                this.pagination = pagination;
                this.loadData(false);
              }}
            >
              <span slot='queueName'>{this.$t('platform.jobManage.jobCatetory')}</span>
              <span slot='jobName'>{this.$t('platform.jobManage.jobName')}</span>
              <span slot='createdAt'>{this.$t('platform.jobManage.createTime')}</span>
              <span slot='finishAt'>{this.$t('platform.jobManage.finishTime')}</span>
              <span slot='stateName'>{this.$t('platform.fields.status')}</span>
              <span slot='action'>{this.$t('platform.fields.operation')}</span>
            </a-table>
          </a-card>
        </comp-card>
        <a-modal
          width='800px'
          title={this.$t('platform.jobManage.jobDetail')}
          visible={this.jobDetailShow}
          destroyOnClose={true}
          maskClosable={false}
          on-cancel={() => { this.jobDetailShow = false; this.selectJob = {}; }}
        >
          <job-detail selectJob={this.selectJob} />
        </a-modal>
      </div>
    );
  }
}
