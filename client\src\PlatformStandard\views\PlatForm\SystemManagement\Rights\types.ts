/**
 * 角色查询
 */
export interface RoleQueryDto {
  roleCode?: string;
  roleName?: string;
  status?: number;
  appCode?: string;
}

/**
 * 系统角色
 */
export interface RoleDto {
  roleId?: string;
  roleCode?: string;
  roleType?: string;
  roleName?: string;
  description?: string;
  status?: number;
  applicationIds?: string[];
}

/**
 * 角色成员查询
 */
export interface RoleMemberQueryDto {
  roleId?: string;
  userInfo?: string;
  organizationInfo?: string;
}

/**
 * 系统角色用户关系
 */
export interface RoleUserRelationDto {
  roleUserRelationId?: string;
  roleId?: string;
  userId: string;
  userLoginId?: string;
  userName?: string;
}

export interface RoleOrganizationRelationDto {
  roleOrganizationRelationId?: string;
  roleId?: string;
  organizationId: string;
  organizationCode?: string;
  organizationName?: string;
  organizationFullName?: string;
  organizationFullCode?: string;
}
