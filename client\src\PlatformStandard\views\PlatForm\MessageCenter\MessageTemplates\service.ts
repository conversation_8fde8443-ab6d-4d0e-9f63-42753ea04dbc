import { MessageTemplateDto, TemplateStatusChange } from './types';
import { httpHelper } from '@/PlatformStandard/common/utils';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

class MessageTemplateService {
  /**
   * 获取消息模板列表
   * @param params 查询参数
   * @returns 消息模板列表
   */
  getMessageTemplateList(params: any): Observable<any> {
    const url = '/api/platform/v1/message-templates';
    return httpHelper.get(url, { params: params }).pipe(
      map(data => ({
        total: data.total,
        items: data.items.map((m: any) => ({
          ...m,
          key: m.templateId
        })),
      }))
    );
  }

  /**
   * 获取消息模板明细
   * @param templateId 模板Id
   * @returns 模板实体
   */
  getMessageTemplate(templateId: string): Observable<any> {
    const url = `/api/platform/v1/message-template/${templateId}`;
    return httpHelper.get(url);
  }

  /**
   * 保存消息模板
   * @param template 消息模板
   * @returns 返回状态
   */
  saveMessageTemplate(template: MessageTemplateDto): Observable<any> {
    const url = '/api/platform/v1/message-template';
    return httpHelper.post(url, template);
  }

  /**
   * 更新消息模板状态
   * @param template 消息模板状态
   * @returns 返回状态
   */
  changeTemplateStatus(template: TemplateStatusChange): Observable<any> {
    const url = '/api/platform/v1/message-template';
    return httpHelper.put(url, template);
  }

  /**
   * 删除消息模板
   * @param templateId 消息模板Id
   * @returns 返回状态
   */
  deleteMessageTemplate(templateId: string): Observable<any> {
    const url = `/api/platform/v1/message-template/${templateId}`;
    return httpHelper.delete(url);
  }
}
export const messageTemplateService = new MessageTemplateService();
