import { httpHelper } from '@/PlatformStandard/common/utils';
import { Observable } from 'rxjs';
import { PutRoleMenu } from './types';

class RightMenuService {

  /**
   * 获取角色菜单
   * @param id 角色ID
   */
  getRoleMenus(id: string): Observable<any> {
    const path = `/api/platform/v1/manage/roles/${id}/menus`;
    return httpHelper.get(path, { params: { 'show-all-menu': 'true' } });
  }

  /**
   * 角色授权数据保存
   * @param id 角色ID
   * @param data 数据
   */
  putRoleMenus(id: string, data: PutRoleMenu[]) {
    const path = `/api/platform/v1/manage/roles/${id}/menus`;
    return httpHelper.post(path, data);
  }

  getDepartmentTree(params: any): Observable<any[]> {
    const _url = '/api/platform/v1/manage/tree-organizations';
    return httpHelper.get(_url, { params });
  }
  searchOrganizations(params: any) {
    const _url = '/api/platform/v1/manage/search-organizations';
    return httpHelper.get(_url, { params });
  }
}
export const rightMenuService = new RightMenuService();
