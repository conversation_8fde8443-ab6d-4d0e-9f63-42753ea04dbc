import { formHelper } from '@/PlatformStandard/common/utils';
import { WrappedFormUtils } from 'ant-design-vue/types/form/form';
import { Component, Prop, Vue } from 'vue-property-decorator';
import { UserPositionDto } from '../../types';
import { OrganizationTreeSelect } from '@/PlatformStandard/components/OrganizationUser/OrganizationTreeSelect';

@Component({
  components: { OrganizationTreeSelect },
})
export class UserPositionEdit extends Vue {
  @Prop() selectUserPosition!: UserPositionDto;
  private currentUserPosition: UserPositionDto = {};
  private form!: WrappedFormUtils;

  save(): UserPositionDto {
    return this.currentUserPosition;
  }

  validateForm(): string[] {
    return formHelper.validateForm(this.form);
  }

  created() {
    this.form = this.$form.createForm(this, { name: 'userPositionForm' });

    if (this.selectUserPosition.organizationId && this.selectUserPosition.organizationId !== '') {
      this.currentUserPosition = JSON.parse(JSON.stringify(this.selectUserPosition));
    } else {
      this.currentUserPosition = {
        primaryPosition: true,
      };
    }
  }

  render() {
    return (
      <div>
        <a-form form={this.form} labelCol={{ span: 5 }} wrapperCol={{ span: 16 }}>
          <a-row>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.organization.org')} required>
                <organization-tree-select multiple={false} allowClear={true} formItem={false}
                  onChange={(value: string) => { this.currentUserPosition.organizationId = value; this.$forceUpdate(); }}
                  v-decorator={['organizationId', {
                    initialValue: this.currentUserPosition.organizationId,
                    rules: [{ required: true, message: this.$l.getLocale(['controls.select', 'platform.organization.org']) }]
                  }]}>
                </organization-tree-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.organization.positionCode')} required>
                <a-input on-change={(e: any) => { this.currentUserPosition.positionCode = e.target.value; }}
                  placeholder={this.$l.getLocale(['controls.input', 'platform.organization.positionCode'])}
                  v-decorator={['positionCode', {
                    initialValue: this.currentUserPosition.positionCode,
                    rules: [{ required: true, message: this.$l.getLocale(['controls.input', 'platform.organization.positionCode']) }]
                  }]} />
              </a-form-item>
            </a-col>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.organization.positionName')} required>
                <a-input on-change={(e: any) => { this.currentUserPosition.positionName = e.target.value; }}
                  placeholder={this.$l.getLocale(['controls.input', 'platform.organization.positionName'])}
                  v-decorator={['positionName', {
                    initialValue: this.currentUserPosition.positionName,
                    rules: [{ required: true, message: this.$l.getLocale(['controls.input', 'platform.organization.positionName']) }]
                  }]} />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.organization.isPrimary')} required>
                <a-switch v-model={this.currentUserPosition.primaryPosition} />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
    );
  }
}
