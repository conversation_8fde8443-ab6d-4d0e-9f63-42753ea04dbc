import { Component, Vue } from 'vue-property-decorator';
import styles from './organization.module.less';
import { OrganizationDto } from './types';
import { organizationService } from './service';
import { i18nHelper, notificationHelper } from '@/PlatformStandard/common/utils';
@Component

export class Organization extends Vue {
    private fieldsSlotMap: any = {};
    private listOrganization: OrganizationDto[] = [];
    private selectedRowKeys: string[] = [];
    private selectedRows: any = [];
    private authorityId = '';
    private columns = [
        { key: 'name', dataIndex: 'name', width: '40%', slots: { title: 'name' } },
        { key: 'code', dataIndex: 'code', width: '40%', slots: { title: 'code' } },
    ];
    private load() {
        this.authorityId = this.$route.query['id'] as string;
        const params = { 'parent-id': '' };
        organizationService.getOrganizations(params).subscribe(data => {
            this.listOrganization = data as OrganizationDto[];
        });
        organizationService.getDataAuthoritysOrganization(this.authorityId).subscribe(s => {
            this.selectedRowKeys = [];
            this.selectedRows = [];
            s.forEach(item => {
                this.selectedRowKeys = [...this.selectedRowKeys, item.organizationId];
                this.selectedRows.push({ id: item.organizationId, name: item.organizationName, path: item.organizationPath });
            });
        });
    }
    private get rowSelection() {
        return {
            selectedRowKeys: this.selectedRowKeys,
            onChange: (selectedRowKeys: any, selectedRows: any) => {
                this.selectedRowKeys = selectedRowKeys;
                this.selectedRows = selectedRows;
            },
            onSelect: (record: any, selected: boolean, selectedRows: any[]) => {
                if (selected) {
                    const rowKeys = this.selectedRowKeys;
                    const reRows = selectedRows.filter(f => (f.path.indexOf(record.path) > -1 || record.path.indexOf(f.path) > -1)
                    && f.path !== record.path);
                    if (reRows.length > 0) {
                        reRows.forEach(f => {
                            if (rowKeys.length > 0 && rowKeys.includes(f.id)) {
                                rowKeys.splice(rowKeys.indexOf(f.id), 1);
                            }
                            selectedRows.splice(selectedRows.indexOf(f), 1);
                        });
                        this.selectedRowKeys = rowKeys;
                        this.selectedRows = selectedRows;
                    }
                }
            }
            /* getCheckboxProps: (record:any) => ({
              props: {
                defaultChecked: record.code === 'MY_ERP', // Column configuration not to be checked
                name: record.name,
              }
            }) */
        };
    }
    private save() {
        const saveData = this.selectedRows
            .map((m: any) => {
                return {
                    id: m.id,
                    name: m.name,
                    path: m.path
                };
            });
        organizationService.saveDataAuthoritysOrganization(this.authorityId, saveData).subscribe(s => {
            notificationHelper.success(i18nHelper.getLocale('messages.success'));
        });
    }
    private onExpand(expanded: any, record: any) {
        if (!expanded) {
            return;
        }
        if (record.children && record.children.length > 0) {
            return;
        }
        const params = { 'parent-id': record.id };
        organizationService.getOrganizations(params).subscribe(rs => {
            const children = rs;
            const dataMap = (items: any[]) => {
                items.map(item => {
                    if (item.id === record.id) {
                        item.children = children;
                        return items;
                    }
                    if (item.children && item.children.length > 0) {
                        dataMap(item.children);
                    }
                });
            };
            dataMap(this.listOrganization);
        });
    }
    created() {
        this.load();

    }
    render() {
        return <a-card>
            <a-row class={styles.row} gutter={8} align='middle' type='flex'>
                <a-button type='primary' on-click={this.save}>
                    {this.$t('buttons.save')}
                </a-button>
            </a-row>

            <a-table
                size='small'
                rowKey='id'
                columns={this.columns}
                dataSource={this.listOrganization}
                scopedSlots={this.fieldsSlotMap}
                row-selection={this.rowSelection}
                on-expand={this.onExpand}
                pagination={false}
            >
                <span slot='name'>{this.$t('platform.fields.name')}</span>
                <span slot='code'>{this.$t('platform.fields.code')}</span>
            </a-table>
        </a-card>;
    }
}
