import { Vue } from 'vue-property-decorator';
import { ActionEnum } from '@/PlatformStandard/services/menu';
import { i18nHelper } from '../utils';
import { languageService } from '@/PlatformStandard/services/language';
import { Settings } from '.';

export abstract class VueModule extends Vue {
  /**
   * 模块多语言
   */
  locale: { [key: string]: any } = {};
  /**
   * 所有按钮
   */
  actions = ActionEnum;
  /**
   * 当前页面所用功能
   */
  abstract pageActions: string[];
  /**
   * i18n多语言模块key
   */
  frameworkI18n = 'framework';
  boostI18n = 'boost';
  buttonsI18n = 'buttons';
  platformI18n = 'platform';
  bpmI18n = 'bpm';
  componentsI18n = 'components';
  fieldsI18n = 'platform.fields';
  controlsI18n = 'controls';
  paginationsI18n = 'paginations';
  messagesI18n = 'messages';
  private i18nLang = '';

  constructor() {
    super();
    const keys = [
      this.frameworkI18n,
      this.buttonsI18n,
      this.fieldsI18n,
      this.platformI18n,
      this.controlsI18n,
      this.messagesI18n,
      this.componentsI18n,
    ];
    this.locale = Object.assign({}, this.locale, this.initLocale(keys));
    languageService.language$.subscribe(s => {
      if (this.i18nLang === s) {
        return;
      }

      this.i18nLang = s;
      this.doI18nObject(this.initLocale(Object.keys(this.locale)), this.locale);
    });
  }

  public set localeKeys(v: string[]) {
    this.locale = Object.assign({}, this.locale, this.initLocale(v));
  }

  /**
   * 返回父级页面
   * 路由面向：path: 'rights/member'
   * 使用说明：需要已方法形式调用，不然this指向不明确
   */
  backParentPage() {
    const len = this.$route.matched.length;
    const currentPath = this.$route.matched[len - 1].path;
    const currentPathIndex = currentPath.lastIndexOf('/');
    if (len > 1 && currentPathIndex !== -1) {
      this.$router.push(currentPath.substring(0, currentPathIndex));
    }
  }

  private initLocale(v: string[]): { [key: string]: object } {
    const result: any = {};
    v.forEach(k => {
      result[k] = JSON.parse(JSON.stringify(i18nHelper.getLocaleObject(k)));
    });

    return result;
  }

  private doI18nObject(obj: any, oldObj: any) {
    Object.keys(obj).forEach(f => {
      if (oldObj[f]) {
        switch (typeof obj[f]) {
          case 'string':
          case 'number':
          case 'boolean':
            this.$set(oldObj, f, obj[f]);
            break;
          default:
            if (obj[f] instanceof Array) {
              obj[f].forEach((e: any, i: number) => {
                this.doI18nObject(obj[f][i], oldObj[f][i]);
              });
            } else if (obj[f] instanceof Object) {
              this.doI18nObject(obj[f], oldObj[f]);
            }
            break;
        }
      }
    });
  }
}
