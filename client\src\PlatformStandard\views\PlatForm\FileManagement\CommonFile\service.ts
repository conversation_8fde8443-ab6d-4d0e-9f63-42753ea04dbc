import { Observable } from 'rxjs';
import { httpHelper } from '@/PlatformStandard/common/utils';
import { CommonFileListDto } from './types';
class CommonFileService {
    getCommonFiles(params: any): Observable<CommonFileListDto> {
        const _url = `/api/platform/v1/common-file/common-files`;
        return httpHelper.get(_url, { params });
    }
    updateCommonFile(id: string, data: any): Observable<void> {
        const _url = `/api/platform/v1/common-file/update/${id}`;
        return httpHelper.put(_url, data);
    }
    uploadCommonFile(data: any): Observable<void> {
        const _url = `/api/platform/v1/common-file/upload`;
        return httpHelper.post(_url, data);
    }
    deleteCommonFile(id: string): Observable<void> {
        const path = `/api/platform/v1/common-file/delete/${id}`;
        return httpHelper.delete(path);
    }
    previewCommonFile(id: string): Observable<any> {
        const _url = `/api/platform/v1/common-file/preview/${id}`;
        return httpHelper.get(_url, {}, { loading: false });
    }
    downCommonFile(id: string): Observable<any> {
        const _url = `/api/platform/v1/common-file/download/${id}`;
        return httpHelper.get(_url, {}, { loading: false });
    }
}
export const commonFileService = new CommonFileService();
