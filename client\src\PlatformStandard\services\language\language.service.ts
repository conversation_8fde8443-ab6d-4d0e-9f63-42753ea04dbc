import { Settings } from '@/PlatformStandard/common/defines';
import { LanguageOption } from './language.types';
import moment from 'moment';
import { httpHelper } from '@/PlatformStandard/common/utils';
import { i18nService } from '../i18n';
import { BehaviorSubject, Subject } from 'rxjs';

class LanguageService {
  private langeAsync = new BehaviorSubject<string>(Settings.DefaultLang);
  langeAsync$ = this.langeAsync.asObservable();
  public language$: Subject<string> = new Subject<string>();

  get languages(): LanguageOption[] {
    return Settings.Languages;
  }

  get current(): LanguageOption {
    let lang = localStorage.getItem(Settings.UserLanguageCacheKey);
    if (!lang) {
      lang = Settings.DefaultLang;
    }
    const languageQuery = Settings.Languages.filter(l => l.code === lang);
    return languageQuery.length > 0 ? languageQuery[0] : Settings.Languages[0];
  }

  set(lang: string) {
    if (!!lang) {
      localStorage.setItem(Settings.UserLanguageCacheKey, lang);
      i18nService.use(lang);
      moment.locale(lang === 'zh' ? 'zh-cn' : lang);
      httpHelper.put('/auth/state', { lang: lang }).subscribe(() => {
        this.language$.next(lang);
        this.langeAsync.next(lang);
      });
    }
  }

  useDefault() {
    moment.locale(this.current.code);
  }
}

export const languageService = new LanguageService();
