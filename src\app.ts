import Koa from 'koa';
import logger from 'koa-logger';
import serve from 'koa-static';
import session from 'koa-generic-session';
import redisStore from 'koa-redis';
import path from 'path';
import http from 'http';
import statuses from 'statuses';
import { gatewayAccessCheck } from './middlewares/gateway-access-check';
import { authStateCheck } from './middlewares/auth-state-check';
import { ApiRouter } from './routes';
import { environment, nacos } from './environment';
import { connect } from './sockets/websocket';

// 添加自定义登录超时状态码
statuses[440] = 'Login Timeout';

const app = new Koa();

nacos().then(() => {
  app.keys = ['movitech production'];
  app.use(async (ctx, next) => {
    if (/\.js|\.css/.test(ctx.url)) {
      if (ctx.url.includes('/static/')) {
        ctx.set('Cache-Control', 'max-age=604800');
      } else {
        ctx.set('Cache-Control', 'max-age=2000');
      }
    }
    await next();
  });
  app.use(
    session({
      key: 'admin.bpm.sid',
      store: redisStore({
        host: environment.session.redis.host, // environment.session.redis.host,
        port: environment.session.redis.port, // environment.session.redis.port,
        db: environment.session.redis.db, // environment.session.redis.db,
        password: environment.session.redis.password, // environment.session.redis.password,
      }),
      cookie: { maxAge: environment.session.maxAge * 1000 },
    })
  );

  app.use(logger());

  // 先检查网关授权
  app.use(gatewayAccessCheck());

  // 用户状态验证后再进行 sso 验证
  app.use(authStateCheck());

  // 静态文件
  app.use(serve(path.resolve(__dirname, '../public')));
  if (!environment.isDevelopment) {
    app.use(serve(path.resolve(__dirname, '../public/platform/')));
    app.use(serve(path.resolve(__dirname, '../public/process/')));
    app.use(serve(path.resolve(__dirname, '../public/data-center/')));
    app.use(serve(path.resolve(__dirname, '../public/function-center/')));
    app.use(serve(path.resolve(__dirname, '../public/program-center/')));
    app.use(serve(path.resolve(__dirname, '../public/integration-center/')));
    app.use(serve(path.resolve(__dirname, '../public/business-for-a/')));
    app.use(serve(path.resolve(__dirname, '../public/rules-engine/')));
  }

  // 路由
  app.use(ApiRouter());

  const server = http.createServer(app.callback());
  // connect(server);
  server.listen(environment.port, () => {
    console.log(`[*] web app listening on :${environment.port}`);
    console.log('Server started - you can set breakpoints now!'); // 测试日志
  });
});
