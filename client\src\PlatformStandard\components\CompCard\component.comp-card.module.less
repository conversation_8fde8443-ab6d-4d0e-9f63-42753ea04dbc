@import '../../../themes/default/variables.less';

.card {
  height: 100%;
  overflow: hidden;
  border-radius: 4px !important;
  // :global(.ant-card-head) {
  //   background: white;
  // }
  :global(.ant-form-item-label){
    // line-height: 20px !important;
  }
  > :global(.ant-card-head) {
    border-bottom: 1px solid #ddd !important;
    min-height: 51px !important;
    > :global(.ant-card-head-wrapper) {
      // padding: 1px 15px !important;
      padding: 1px 8px 1px 15px ;
      > :global(.ant-card-head-title) {
        padding: 12px 10px !important;
        box-sizing: border-box;
        font-size: 16px;
      }
    }
    > :global(.ant-card-head-wrapper::before) {
      content: '';
      position: absolute;
      height: 16px;
      width: 4px;
      background-color: @primary-color;
      // top: (@card-head-height - 20)/2;
      top: 16px;
      left: 10px;
      // padding: 2px;
    }
  }
  > :global(.ant-card-head-title) {
    padding:15px !important;
    color: #666 !important;
  }
  :global(.ant-card-extra) {
    // height: @card-head-height;
    // line-height: @card-head-height;
    padding: 0px;
  }

  :global(.ant-card-body) {
    overflow: auto;
    height: calc(~'100%' - 50px);
    // padding: 8px !important;
    // padding:0 10px !important;
    :global(table){
      border-top:2px solid #2165d9 !important;
    }
    :global(.ant-table-thead) {
      // background: #f5f5f5 !important;
      background: rgba(33,101,217,0.07) !important;
      // border-top:2px solid #2165d9 !important;
    }
    :global(.ant-table-thead th) {
      color:#666 !important;
      font-weight: bold !important;
    }
    :global(.ant-table-tbody tr:nth-child(2n)){
      background: #F7FAFE !important;
    }
  }

  :global(.ant-card-head) {
    // padding: 0 @base-size;
    // padding: 0 @base-size * 2;
    padding: 0px;
    font-weight: bold;
    font-size: @font-size-base;
  }
  :global(.commonBtn) {
    padding:0;
    margin: 0;
    width: 70px !important;
    height: 30px;
    text-align: center;
    line-height: 32px;
    border-radius: 5px !important;
    // border: 1px solid #ddd !important;
    background-color: #fff !important;
    color: #333 !important;
    opacity: 1;
  }
  :global(.ant-table-thead > tr > th){
    border-left:1px solid #ddd;
    background: #f5f5f5;
  }
  :global(.ant-table-thead > tr > th:first-child){
    border-left:0px solid #ddd;
  }
  :global(.ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td) {
    background-color : #f5f5f5
  }
  :global(.ant-btn-link){
    color: #1677FF;
  }
  .no-result-tips {
    width: 445px;
    margin: 36px;
  }
}
:global(.ant-card-bordered){
  border: 0px solid #fff !important;
}
:global(.component-comp-card-module_card_2wiex .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td){
  background: #EFF4FC !important;
}
// :global(.cardTitleBg){
//   background: red;
// }
.cardTitleBg {
  height: 100%;
  overflow: hidden;
  border-radius: 4px !important;
  // :global(.ant-card-head) {
  //   background: white;
  // }
  > :global(.ant-card-head) {
    // border-bottom: 1px solid #ddd !important;
    min-height: 51px !important;
    background: #f4f7fa;
    > :global(.ant-card-head-wrapper) {
      // padding: 1px 15px !important;
      padding: 1px 8px 1px 15px ;
      > :global(.ant-card-head-title) {
        padding: 12px 10px !important;
        box-sizing: border-box;
        font-size: 16px;
      }
    }
    > :global(.ant-card-head-wrapper::before) {
      content: '';
      position: absolute;
      height: 16px;
      width: 4px;
      background-color: @primary-color;
      // top: (@card-head-height - 20)/2;
      top: 16px;
      left: 10px;
      // padding: 2px;
    }
  }
  > :global(.ant-card-head-title) {
    padding:15px !important;
    color: #666 !important;
  }
  :global(.ant-card-extra) {
    // height: @card-head-height;
    // line-height: @card-head-height;
    padding: 0px;
  }

  :global(.ant-card-body) {
    overflow: auto;
    height: 100%;
    // padding: 8px !important;
    // padding:0 10px !important;
    :global(table){
      border-top:2px solid #2165d9 !important;
    }
    :global(.ant-table-thead) {
      // background: #f5f5f5 !important;
      background: rgba(33,101,217,0.07) !important;
      // border-top:2px solid #2165d9 !important;
    }
    :global(.ant-table-thead th) {
      color:#666 !important;
      font-weight: bold !important;
    }
    :global(.ant-table-tbody tr:nth-child(2n)){
      background: #F7FAFE !important;
    }
  }

  :global(.ant-card-head) {
    // padding: 0 @base-size;
    // padding: 0 @base-size * 2;
    padding: 0px;
    font-weight: bold;
    font-size: @font-size-base;
  }
  :global(.commonBtn) {
    padding:0;
    margin: 0;
    width: 70px !important;
    height: 30px;
    text-align: center;
    line-height: 32px;
    border-radius: 5px !important;
    // border: 1px solid #ddd !important;
    background-color: #fff !important;
    color: #333 !important;
    opacity: 1;
  }
  :global(.ant-table-thead > tr > th){
    border-left:1px solid #ddd;
    background: #f5f5f5;
  }
  :global(.ant-table-thead > tr > th:first-child){
    border-left:0px solid #ddd;
  }
  :global(.ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td) {
    background-color : #f5f5f5
  }
  :global(.ant-btn-link){
    color: #1677FF;
  }
  .no-result-tips {
    width: 445px;
    margin: 36px;
  }
}