import { Component, Vue } from 'vue-property-decorator';
import { dataAuthorityService } from './service';
import { DataAuthorityEntity } from './types';
import { VueModule } from '@/PlatformStandard/common/defines';
import styles from './dataauthority.module.less';
import { i18nHelper, notificationHelper, removeNullValueProperty } from '@/PlatformStandard/common/utils';
import { DataAuthorityEdit } from './Edit/index';
import { CompCard, CompTableHeader } from '@/PlatformStandard/components';
import { languageService } from '@/PlatformStandard/services/language';
@Component({
    components: { DataAuthorityEdit, CompCard, CompTableHeader }
})
export class DataAuthority extends VueModule {
    pageActions: string[] = [];
    private fieldsSlotMap: any = {};
    private loading = false;
    private listDataAuthority: DataAuthorityEntity[] = [];
    private modelType = 'and';
    private modelShow = false;
    private nameOrCode = '';
    private dataStatus = 1;
    private editDataAuthority: DataAuthorityEntity = {};
    private parentDataAuthority: DataAuthorityEntity = {};
    private statusDataset = i18nHelper.getLocaleObject('platform.dataPermission.statusDataset');
    private pagination = {
        total: 0,
        current: 1,
        pageSize: 10,
        showSizeChanger: true,
        size: 'small',
        showTotal: (total: string) => i18nHelper.getReplaceLocale(`${this.paginationsI18n}.total`, total),
    };
    private columns = [
        { key: 'dataAuthorityName', dataIndex: 'dataAuthorityName', width: '30%', slots: { title: 'dataAuthorityName' } },
        { key: 'remark', dataIndex: 'remark', width: '30%', slots: { title: 'remark' } },
        { key: 'status', dataIndex: 'status', width: '10%', scopedSlots: { customRender: 'status' }, slots: { title: 'status' } },
        { dataIndex: 'operation', scopedSlots: { customRender: 'operation' }, slots: { title: 'operation' } },
    ];
    private onTableChange(pagination: any, filters: any, sorter: any): void {
        this.pagination = pagination;
        this.nameOrCode = '';
        this.load();
    }
    // 重载数据
    private load(pageIndex?: number): void {
        this.loading = true;
        if (pageIndex) {
            this.pagination.current = pageIndex;
        }
        const params: any = {
            'page-size': this.pagination.pageSize,
            'page-index': this.pagination.current,
            'kw': this.nameOrCode,
            'status': this.dataStatus
        };
        dataAuthorityService.getDataAuthoritys(removeNullValueProperty(params)).subscribe(data => {
            this.pagination.total = data.total;
            this.listDataAuthority = data.items;
            this.loading = false;
        });
    }
    private addchildern(record: any) {
        this.editDataAuthority = {
            status: 1
        };
        this.parentDataAuthority = record;
        this.modelType = 'add';
        this.modelShow = true;
    }
    private authConfig(record: any) {
        this.$router.push('/authority-management/data-authority/config?id=' + record.dataAuthorityId + '&name=' + record.dataAuthorityName);
    }
    private roleAuth(record: any) {
        this.$router.push('/authority-management/data-authority/role?id=' + record.dataAuthorityId + '&name=' + record.dataAuthorityName);
    }
    private add() {
        this.editDataAuthority = {
            status: 1
        };
        this.parentDataAuthority = {};
        this.modelType = 'add';
        this.modelShow = true;
    }
    private edit(record: any) {
        this.parentDataAuthority = {};
        this.editDataAuthority = record;
        this.modelType = 'edit';
        this.modelShow = true;
    }
    private delete(record: any): void {
        dataAuthorityService.deleteDataAuthority(record.dataAuthorityId || '').subscribe(data => {
            notificationHelper.success(i18nHelper.getLocale('messages.success'));
            this.load();
        });
    }
    private saveEdit(): void {
        const errorMsgs = (this.$refs.editDataAuthorityInfo as any).validateForm() as string[];
        if (errorMsgs.length === 0) {
            const dataAuthority = (this.$refs.editDataAuthorityInfo as any).getDataAuthorityInfo() as DataAuthorityEntity;
            if (this.modelType === 'add') {
                dataAuthorityService.addDataAuthority(dataAuthority).subscribe(data => {
                    notificationHelper.success(i18nHelper.getLocale('messages.success'));
                    this.modelShow = false;
                    this.load();
                });
            } else {
                dataAuthorityService.updateDataAuthority(dataAuthority.dataAuthorityId || '', dataAuthority).subscribe(data => {
                    notificationHelper.success(i18nHelper.getLocale('messages.success'));
                    this.modelShow = false;
                    this.load();
                });
            }
        } else {
            notificationHelper.error(errorMsgs);
        }
    }
    // 查询
    private onSearch(): void {
        this.load(1);
    }
    // 重置
    private onReset(): void {
        this.nameOrCode = '';
        this.dataStatus = 1;
        this.load(1);
    }
    created() {
        languageService.language$.subscribe(lang => {
            this.statusDataset = i18nHelper.getLocaleObject('platform.dataPermission.statusDataset');
        });
        this.fieldsSlotMap['status'] = (text: number, record: any, index: number) => {
            return (
                <div>
                    {text === 1 ? this.$t('platform.fields.valid') : this.$t('platform.fields.inValid')}
                </div>
            );
        };
        this.fieldsSlotMap['operation'] = (text: number, record: any, index: number) => {
            return ([
                <a-button type='link' on-click={() => this.authConfig(record)}>
                    {this.$t('bpm.data-authority.config')}
                </a-button>,
                <a-button type='link' on-click={() => this.roleAuth(record)}>
                    {this.$t('bpm.data-authority.role')}
                </a-button>,
                <a-button type='link' on-click={() => this.edit(record)}>
                    {this.$t('buttons.edit')}
                </a-button>,
                <a-popconfirm
                    title={this.$t('messages.delete')}
                    on-confirm={() => this.delete(record)}
                >
                    <a-button type='link'>
                        {this.$t('buttons.delete')}
                    </a-button>
                </a-popconfirm>
            ]);
        };
        this.load(1);
    }
    render() {
        return (
            <div>
                <comp-card class={styles.card_top}>
                    <comp-table-header class={styles.table_header}
                        on-search={() => {
                            this.onSearch();
                        }}
                        on-reset={() => {
                            this.onReset();
                        }}
                    >
                        <template slot='base'>
                            <a-row >
                                <a-col span='6' class='mr-1'>
                                    <a-form-item label={this.$t('platform.fields.name')}>
                                        <a-input v-model={this.nameOrCode}></a-input>
                                    </a-form-item>
                                </a-col>
                                <a-col span='6'>
                                    <a-form-item label={this.$t('platform.fields.inUse')}>
                                        <a-select v-model={this.dataStatus} allowClear>
                                            {this.statusDataset.map((item: any) => (
                                                <a-select-option value={item.value}>{item.label}</a-select-option>
                                            ))}
                                        </a-select>
                                    </a-form-item>
                                </a-col>
                            </a-row>
                        </template>
                    </comp-table-header>
                </comp-card>
                <comp-card>
                    <a-card class={styles.base_table} bordered={false}>
                        <div slot='extra'>
                            <a-row>
                                <a-col span='24' style='text-align:right;padding-bottom:10px'>
                                    <a-button type='primary' class={styles.common_btn} on-click={this.add}>
                                        {this.$t('buttons.add')}
                                    </a-button>
                                </a-col>
                            </a-row>
                        </div>
                        <a-table
                            size='small'
                            rowKey='dataAuthorityId'
                            columns={this.columns}
                            dataSource={this.listDataAuthority}
                            pagination={this.pagination}
                            scopedSlots={this.fieldsSlotMap}
                            on-change={this.onTableChange}
                        >
                            <span slot='dataAuthorityName'>{this.$t('platform.fields.name')}</span>
                            <span slot='remark'>{this.$t('platform.fields.remark')}</span>
                            <span slot='status'>{this.$t('platform.fields.inUse')}</span>
                            <span slot='operation'>{this.$t('platform.fields.operation')}</span>
                        </a-table>
                    </a-card>
                </comp-card>
                <a-modal width='600px'
                    title={this.modelType === 'add' ? this.$t('buttons.add') : this.$t('buttons.edit')}
                    visible={this.modelShow}
                    destroyOnClose={true}
                    maskClosable={false}
                    on-cancel={(c: any) => this.modelShow = false}
                    on-ok={this.saveEdit}>
                    <data-authority-edit selectDataAuthority={this.editDataAuthority} parentDataAuthority={this.parentDataAuthority} ref='editDataAuthorityInfo'></data-authority-edit>
                </a-modal>
            </div >
        );
    }
}
