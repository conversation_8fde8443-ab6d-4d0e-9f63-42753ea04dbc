/*
变量参考 https://github.com/vueComponent/ant-design-vue/blob/master/components/style/themes/default.less
或 https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/components/style/themes/default.less
*/
/* 重写 AntDesign 的主题样式 */
/* 重写 AntDesign 结束 */
/* 自定义相关 */
/* 基础边距 */
.list_button {
  padding: 0 3px !important;
}
.row_outer {
  background-color: #f8f8f8;
  margin: 8px 0px !important;
}
.row {
  margin-bottom: 10px;
}
.operation {
  text-align: right;
  cursor: pointer;
}
.org_title {
  cursor: default;
  display: inline;
  padding-right: 4px;
  font-weight: normal;
}
.org_title i {
  color: #bfbfbf;
  margin-left: 2px;
}
:global(.cardBorderTop) {
  border-top: 1px solid #ddd !important;
  padding-top: 10px;
  margin-top: -10px !important;
}
