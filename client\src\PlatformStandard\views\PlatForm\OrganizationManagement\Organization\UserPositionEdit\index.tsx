import { formHelper } from '@/PlatformStandard/common/utils';
import { WrappedFormUtils } from 'ant-design-vue/types/form/form';
import debounce from 'lodash/debounce';
import { Component, Prop, Vue } from 'vue-property-decorator';
import { organizationService } from '../service';
import { OrganizationDto, UserPositionDto } from '../types';

@Component
export class UserPositionEdit extends Vue {
  @Prop() selectOrganization!: OrganizationDto;
  @Prop() selectUserPosition!: UserPositionDto;
  @Prop() option!: number; // 1：添加 2：编辑
  private currentUserPosition: UserPositionDto = {};
  private userFetching = false;
  private fetchUser = debounce((value: any) => this.userSearch(value), 500);
  private userItems: any = [];
  private form!: WrappedFormUtils;

  save(): UserPositionDto {
    return this.currentUserPosition;
  }

  validateForm(): string[] {
    return formHelper.validateForm(this.form);
  }

  created() {
    this.form = this.$form.createForm(this, { name: 'userPositionForm' });

    switch (this.option) {
      case 1:
        this.currentUserPosition = {
          organizationId: this.selectOrganization.organizationId,
          organizationCode: this.selectOrganization.organizationCode,
          organizationName: this.selectOrganization.name,
          primaryPosition: true,
        };
        break;
      case 2:
      default:
        this.currentUserPosition = JSON.parse(JSON.stringify(this.selectUserPosition));
        this.userSearch((this.currentUserPosition.userName as string).substring(0, String(this.currentUserPosition.userName).indexOf('(')));
        break;
    }
  }

  private userSearch(value: string) {
    organizationService.getUsers(value).subscribe(data => {
      this.userItems = data;
      this.userFetching = false;
      this.$forceUpdate();
    });
  }

  render() {
    return (
      <div>
        <a-form form={this.form} labelCol={{ span: 5 }} wrapperCol={{ span: 16 }}>
          <a-row>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.organization.organizationCode')}>
                {this.currentUserPosition.organizationCode}
              </a-form-item>
            </a-col>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.organization.organizationName')}>
                {this.currentUserPosition.organizationName}
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.organization.positionCode')} required>
                <a-input on-change={(e: any) => { this.currentUserPosition.positionCode = e.target.value; }}
                  placeholder={this.$l.getLocale(['controls.input', 'platform.organization.positionCode'])}
                  v-decorator={['positionCode', {
                    initialValue: this.currentUserPosition.positionCode,
                    rules: [{ required: true, message: this.$l.getLocale(['controls.input', 'platform.organization.positionCode']) }]
                  }]} />
              </a-form-item>
            </a-col>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.organization.positionName')} required>
                <a-input on-change={(e: any) => { this.currentUserPosition.positionName = e.target.value; }}
                  placeholder={this.$l.getLocale(['controls.input', 'platform.organization.positionName'])}
                  v-decorator={['positionName', {
                    initialValue: this.currentUserPosition.positionName,
                    rules: [{ required: true, message: this.$l.getLocale(['controls.input', 'platform.organization.positionName']) }]
                  }]} />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.organization.isPrimary')} required>
                <a-switch v-model={this.currentUserPosition.primaryPosition} />
              </a-form-item>
            </a-col>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.organization.user')} required>
                <a-select
                  show-search
                  placeholder={this.$l.getLocale(['controls.select', 'platform.organization.user'])}
                  default-active-first-option={false}
                  show-arrow={false}
                  filter-option={false}
                  not-found-content={this.userFetching ? undefined : null}
                  on-search={this.fetchUser}
                  on-change={(value: any) => {
                    this.currentUserPosition.userId = value;
                    this.currentUserPosition.userName = (this.userItems.find((d: any) => d.value === value) as any).label;
                  }}
                  v-decorator={['user', {
                    initialValue: this.currentUserPosition.userId,
                    rules: [{
                      required: true,
                      message: this.$l.getLocale(['controls.select', 'platform.organization.user'])
                    }]
                  }]}
                >
                  {
                    this.userFetching ?
                      <a-spin slot='notFoundContent' size='small' />
                      :
                      null
                  }

                  {
                    this.userItems.map((item: any) => (
                      <a-select-option value={item.value}>
                        {item.label}
                      </a-select-option>
                    ))
                  }
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
    );
  }
}
