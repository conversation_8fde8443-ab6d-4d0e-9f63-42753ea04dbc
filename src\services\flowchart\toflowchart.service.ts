import { DagreLayout} from '@antv/layout';

export class ToFlowchartService {
    // 旧流程图对象
    private _oldObject: any;
    // 旧、新节点映射关系
    private _oldNewMap: { Nodes: Array<{ old: string , new: string}>, Lines: Array<{ old: any , new: any}>} = {Nodes: [], Lines: []};
    // 新流程图对象
    private _newObject: any = {};
    // 字段映射,__ 分隔字段层级
    private _fieldMap: any = {
        Nodes: 'Nodes',
        Nodes__NodeId: 'NodeId',
        Nodes__x: 'x',
        Nodes__y: 'y',
        Lines: 'Lines',
        Lines__SourceNodeId: 'SourceNodeId',
        Lines__TargetNodeId: 'TargetNodeId',
        Lines__Name: 'Name',
        Lines__Expression: 'Expression'
    };
    // 树型布局配置
    private _dagreLayoutDefaultCfg: any = {
        rankdir: 'TB', // layout 方向, 可选 TB, BT, LR, RL
        align: undefined, // 节点对齐方式，可选 UL, UR, DL, DR
        nodeSize: undefined, // 节点大小
        nodesepFunc: undefined, // 节点水平间距(px)
        ranksepFunc: undefined, // 每一层节点之间间距
        nodesep: 100, // 节点水平间距(px)
        ranksep: 70, // 每一层节点之间间距
        controlPoints: true, // 是否保留布局连线的控制点
        radial: false, // 是否基于 dagre 进行辐射布局
        focusNode: null, // radial 为 true 时生效，关注的节点
    };
    // 节点对比字段集合
    private _nodeComparisonFieldList: any[] = ['Name'];
    // 节点对比方法(旧节点,新节点,节点对比字段集合,是否匹配)
    private _nodeComparisonFun: any = (o: any, n: any, l: string[]): boolean => {
        let isSame = true;
        l.forEach(item => {
            if (item.includes('[]')) {
                // 包含集合的对比
                const name = item.substring(0, item.indexOf('[]'));     // 集合
                const subName = item.substring(item.indexOf('[]') + 3); // 子路径
                const o_c = JSON.parse(JSON.stringify(o[name])) as [];  // 复杂数组子对象
                const n_c = JSON.parse(JSON.stringify(n[name])) as [];  // 复杂数组子对象

                if ( o_c && n_c) {  // 2个都有数据
                    if (o_c.length === n_c.length) {    // 数量一致
                        o_c.forEach(e => {
                            const number = n_c.findIndex(f => {
                                if (subName && subName !== '') {
                                    // 有子路径：迭代比较
                                    return this._nodeComparisonFun(e, f, [subName]);
                                } else {
                                    // 没子路径：对比数量、子元素json序列化字符串后比较内容
                                    return JSON.stringify(f) === JSON.stringify(e);
                                }
                            });
                            if (number >= 0) {
                                n_c.splice(number, 1);
                            }
                        });
                        if (n_c && n_c.length > 0) {    // n_c还有剩余，认为不相等
                            isSame = false;
                        }
                    } else {    // 数量不一致，认为不相等
                        isSame = false;
                    }
                } else if (!o_c && !n_c) {  // 2个都没有数据，认为相等

                } else {    // 1个有数据，1个没有数据，认为不相等
                    isSame = false;
                }
            } else {
                // 非集合的对比
                if (this.getValueByPath(o, item) !== this.getValueByPath(n, item)) {
                    isSame = false;
                }
            }
        });
        return isSame;
    }

    // 构造函数
    constructor(bodyParams: any) {
        this._oldObject = bodyParams.Data;
        if (bodyParams.DagreLayoutCfg) {
          this._dagreLayoutDefaultCfg = {...this._dagreLayoutDefaultCfg, ...bodyParams.DagreLayoutCfg};
        }
        if (bodyParams.NodeComparisonFieldList && bodyParams.NodeComparisonFieldList.length > 0) {
          this._nodeComparisonFieldList = bodyParams.NodeComparisonFieldList;
        }
        if (bodyParams.FieldMap) {
          this._fieldMap = {...this._fieldMap, ...bodyParams.FieldMap};
          console.log(this._fieldMap);
        }
        this._newObject[this._fieldMap.Nodes] = [];
        this._newObject[this._fieldMap.Lines] = [];
    }

    // 优化
    optimization() {
        this.findNewNode();
        this.findNewLine();
        this.dagreLayout();
        return {Data: {...this._oldObject, ...this._newObject}, OdlNewMap: this._oldNewMap};
    }
    //#region 找出新节点集合
    // 找出新节点集合
    findNewNode() {
        this._oldObject[this._fieldMap.Nodes].forEach((node: any) => {
            // 根据节点对比方法查找
            const newNode = this._newObject[this._fieldMap.Nodes].find((o: any) =>
              this._nodeComparisonFun(node, o, this._nodeComparisonFieldList)
            );
            if (!newNode) {
                // 如果没有匹配到：1.添加新节点；
                this._newObject[this._fieldMap.Nodes].push(node);
                // 2.直接添加原、新节点映射关系
                this._oldNewMap.Nodes.push({old: node[this._fieldMap.Nodes__NodeId], new: node[this._fieldMap.Nodes__NodeId]});
            } else {
                // 2.直接添加原、新节点映射关系
                this._oldNewMap.Nodes.push({old: node[this._fieldMap.Nodes__NodeId], new: newNode[this._fieldMap.Nodes__NodeId]});
            }
        });
    }
    //#endregion

    //#region 找出新线集合
    // 找出新线集合
    findNewLine() {
        this.findNewLine_Distinct();

        this.findNewLine_MergeNameAndExpression();

        this.findNewLine_SplitNameAndExpression();
    }
    // 找出新线集合_去重
    findNewLine_Distinct() {
        this._oldObject[this._fieldMap.Lines].forEach((line: any) => {
            const newLine = this._newObject[this._fieldMap.Lines].find((o: any) =>
            o[this._fieldMap.Lines__SourceNodeId] ===
            this._oldNewMap.Nodes.find(p => p.old === line[this._fieldMap.Lines__SourceNodeId]).new
            && o[this._fieldMap.Lines__TargetNodeId] ===
            this._oldNewMap.Nodes.find(p => p.old === line[this._fieldMap.Lines__TargetNodeId]).new);
            if (!newLine) {
                const tempLine: any = {};
                tempLine[this._fieldMap.Lines__SourceNodeId] =
                this._oldNewMap.Nodes.find(p => p.old === line[this._fieldMap.Lines__SourceNodeId]).new;
                tempLine[this._fieldMap.Lines__TargetNodeId] =
                this._oldNewMap.Nodes.find(p => p.old === line[this._fieldMap.Lines__TargetNodeId]).new;
                tempLine[this._fieldMap.Lines__Name] = [line[this._fieldMap.Lines__Name]];
                tempLine[this._fieldMap.Lines__Expression] = [line[this._fieldMap.Lines__Expression]];
                this._newObject[this._fieldMap.Lines].push(tempLine);
                // 新旧映射
                this._oldNewMap.Lines.push({old: {
                    SourceNodeId: line[this._fieldMap.Lines__SourceNodeId],
                    TargetNodeId: line[this._fieldMap.Lines__TargetNodeId]
                }, new: {
                    SourceNodeId: this._oldNewMap.Nodes.find(p => p.old === line[this._fieldMap.Lines__SourceNodeId]).new,
                    TargetNodeId: this._oldNewMap.Nodes.find(p => p.old === line[this._fieldMap.Lines__TargetNodeId]).new
                }});
            } else {
                if (line[this._fieldMap.Lines__Name] && line[this._fieldMap.Lines__Name] !== '') {
                  newLine[this._fieldMap.Lines__Name].push(line[this._fieldMap.Lines__Name]);
                }
                if (line[this._fieldMap.Lines__Expression] && line[this._fieldMap.Lines__Expression] !== '') {
                    newLine[this._fieldMap.Lines__Expression].push(line[this._fieldMap.Lines__Expression]);
                }

                // 新旧映射
                this._oldNewMap.Lines.push({old: {
                    SourceNodeId: line[this._fieldMap.Lines__SourceNodeId],
                    TargetNodeId: line[this._fieldMap.Lines__TargetNodeId]
                }, new: {
                    SourceNodeId: newLine[this._fieldMap.Lines__SourceNodeId],
                    TargetNodeId: newLine[this._fieldMap.Lines__TargetNodeId]
                }});
            }
        });
    }
    // 找出新线集合_合并名称、条件
    findNewLine_MergeNameAndExpression() {
        // 将Name、Expression 集合 按或拼接成字符串
        this._newObject[this._fieldMap.Lines].forEach((item: any) => {
            const nameArray = item[this._fieldMap.Lines__Name];
            if ( nameArray && nameArray.length > 1 ) {
                // 如果有多个，用圆括号包裹然后再以“或”拼接
                item[this._fieldMap.Lines__Name] = (nameArray as string[]).filter(o => o && o !== '' ).map(o => `(${o})`).join(' 或 ');
            } else {
                item[this._fieldMap.Lines__Name] = nameArray[0];
            }

            const expressionArray = item[this._fieldMap.Lines__Expression];
            if ( expressionArray && expressionArray.length > 1 ) {
                // 如果有多个，用圆括号包裹然后再以“||”拼接
                item[this._fieldMap.Lines__Expression] =
                (expressionArray as string[]).filter(o => o && o !== '' ).map(o => `(${o})`).join(' || ');
            } else {
                item[this._fieldMap.Lines__Expression] = expressionArray[0];
            }
        });
    }
    // 找出新线集合_拆分名称、条件(有可能 有合并之后的节点再次分开走)
    findNewLine_SplitNameAndExpression() {
        this._newObject[this._fieldMap.Nodes].forEach((item: any) => {
            // 在新线集合中找出有多个下级的线
            const lines = this._newObject[this._fieldMap.Lines].filter((o: any) =>
            o[this._fieldMap.Lines__SourceNodeId] === item[this._fieldMap.Nodes__NodeId]);
            if (lines && lines.length > 1) {
                // 循环每个新下级线
                lines.forEach((line: any) => {
                    if (!line[this._fieldMap.Lines__Expression] || line[this._fieldMap.Lines__Expression] === '') {
                        // 通过新旧映射找到之前的线
                        const oldStLines = this._oldNewMap.Lines
                        .filter(o => o.new.SourceNodeId ===
                            line[this._fieldMap.Lines__SourceNodeId] && o.new.TargetNodeId === line[this._fieldMap.Lines__TargetNodeId]);
                        // 递归循环找到旧流程图中 之前线上的条件
                        const oldLines: any[] = [];
                        oldStLines.forEach(o => {
                            this.findAllParentLines(oldLines, this._oldObject[this._fieldMap.Lines], o.old['TargetNodeId']);
                        });

                        const oldLinesE = oldLines.filter(o => o[this._fieldMap.Lines__Expression]
                            && o[this._fieldMap.Lines__Expression] !== '');
                        if (oldLinesE.length > 1) {
                            // 如果有多个，用圆括号包裹然后再以“或”拼接
                            line[this._fieldMap.Lines__Name] = oldLinesE.map(o => `(${o[this._fieldMap.Lines__Name]})`).join(' 或 ');

                            // 如果有多个，用圆括号包裹然后再以“||”拼接
                            line[this._fieldMap.Lines__Expression] =
                            oldLinesE.map(o => `(${o[this._fieldMap.Lines__Expression]})`).join(' || ');
                        } else {
                            line[this._fieldMap.Lines__Name] = oldLinesE[0][this._fieldMap.Lines__Name];
                            line[this._fieldMap.Lines__Expression] = oldLinesE[0][this._fieldMap.Lines__Expression];
                        }
                    }
                });
            }
        });
    }
    //#endregion

    //#region
    // 树型布局
    dagreLayout() {
        const graph = new DagreLayout(this._dagreLayoutDefaultCfg);
        const data = {
            nodes: this._newObject[this._fieldMap.Nodes].map((o: any) => {
              return {
                id: o[this._fieldMap.Nodes__NodeId],
                x: o[this._fieldMap.Nodes__x],
                y: o[this._fieldMap.Nodes__y]
              };
            }),
            edges: this._newObject[this._fieldMap.Lines].map((o: any) => {
              return {
                source: o[this._fieldMap.Lines__SourceNodeId],
                target: o[this._fieldMap.Lines__TargetNodeId],
              };
            })
          };
        graph.init(data);
        graph.execute();
        graph.nodes.forEach((o: any) => {
            const node = this._newObject[this._fieldMap.Nodes].find((p: any) => p[this._fieldMap.Nodes__NodeId] === o.id);
            if (node) {
              node[this._fieldMap.Nodes__x] = o.x;
              node[this._fieldMap.Nodes__y] = o.y;
            }
        });
    }
    //#endregion

    // 找出所有上级线
    findAllParentLines(result: any[], lines: any[], nodeId: string) {
        const oldLine = lines.find(o => o[this._fieldMap.Lines__TargetNodeId] === nodeId);
        if (oldLine && oldLine[this._fieldMap.Lines__SourceNodeId] !== oldLine[this._fieldMap.Lines__TargetNodeId]) {
            result.push(oldLine);
            this.findAllParentLines(result, lines, oldLine[this._fieldMap.Lines__SourceNodeId]);
        }
    }

    // 根据路径获取对象属性值
    getValueByPath(obj: any, path: string) {
        const paths = path.split('.');
        let res = obj;
        let prop: string = paths.shift();
        while (prop && res) {
            res = res[prop];
            prop = paths.shift();
        }
        return res;
    }
}

// export const toFlowchartService = new ToFlowchartService();
