import { Component, Prop, Vue } from 'vue-property-decorator';
import { UserTransferInput } from '@/PlatformStandard/components';
import { OrganizationDto } from '../types';
import { UserItemDto } from '@/PlatformStandard/components/OrganizationUser/types';
import { WrappedFormUtils } from 'ant-design-vue/types/form/form';
import { formHelper } from '@/PlatformStandard/common/utils';

@Component(
  { components: { UserTransferInput } }
)
export class OrganizationEdit extends Vue {
  @Prop() selectOrganization!: OrganizationDto;
  @Prop() option!: number; // 0:添加顶级组织 1：添加子组织 2：编辑组织
  private currentOrganization: OrganizationDto = {};
  private selectLeader: UserItemDto[] = [];
  private form!: WrappedFormUtils;

  save(): OrganizationDto {
    return this.currentOrganization;
  }

  validateForm(): string[] {
    return formHelper.validateForm(this.form);
  }

  created() {
    this.form = this.$form.createForm(this, { name: 'organizationForm' });
    switch (this.option) {
      case 0:
        this.currentOrganization = { level: 0 , sortCode: 0};
        break;
      case 1:
        this.currentOrganization = {
          level: Number(this.selectOrganization.level) + 1,
          upperId: this.selectOrganization.organizationId,
          upperName: this.selectOrganization.name
        };
        break;
      case 2:
      default:
        this.currentOrganization = JSON.parse(JSON.stringify(this.selectOrganization));
        if (this.currentOrganization.manager) {
          this.selectLeader = [{ userId: this.currentOrganization.manager, userName: this.currentOrganization.managerName }];
        }
        break;
    }
  }

  render() {
    return (
      <div>
        <a-form form={this.form} labelCol={{ span: 5 }} wrapperCol={{ span: 16 }}>
          <a-row>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.organization.superiorOrganization')}>
                {this.currentOrganization.upperName}
              </a-form-item>
            </a-col>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.fields.level')}>
                {this.currentOrganization.level}
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.fields.name')} required>
                <a-input on-change={(e: any) => { this.currentOrganization.name = e.target.value; }}
                  placeholder={this.$l.getLocale(['controls.input', 'platform.fields.name'])}
                  v-decorator={['name', {
                    initialValue: this.currentOrganization.name,
                    rules: [{ required: true, message: this.$l.getLocale(['controls.input', 'platform.fields.name']) }]
                  }]} />
              </a-form-item>
            </a-col>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.fields.code')} required>
                <a-input on-change={(e: any) => { this.currentOrganization.organizationCode = e.target.value; }}
                  placeholder={this.$l.getLocale(['controls.input', 'platform.fields.code'])}
                  v-decorator={['code', {
                    initialValue: this.currentOrganization.organizationCode,
                    rules: [{ required: true, message: this.$l.getLocale(['controls.input', 'platform.fields.code']) }]
                  }]} />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.fields.leader')}>
                <user-transfer-input
                  multiple={false}
                  value={this.selectLeader}
                  placeholder={this.$l.getLocale(['controls.input', 'platform.fields.leader'])}
                  on-change={(data: UserItemDto) => {
                    this.currentOrganization.manager = data.userId;
                    this.currentOrganization.managerName = data.userName;
                    this.selectLeader = [data];
                  }}
                />
              </a-form-item>
            </a-col>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.fields.phone')}>
                <a-input v-model={this.currentOrganization.telephone}
                  placeholder={this.$l.getLocale(['controls.input', 'platform.fields.phone'])} />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.fields.order')}>
                <a-input-number v-model={this.currentOrganization.sortCode} min={0}
                  placeholder={this.$l.getLocale(['controls.input', 'platform.fields.order'])} style='width:100%'/>
              </a-form-item>
            </a-col>
            <a-col span='12'>
              <a-form-item label={this.$t('platform.fields.remark')}>
                <a-textarea v-model={this.currentOrganization.remark}
                  placeholder={this.$l.getLocale(['controls.input', 'platform.fields.remark'])} rows='4' />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
    );
  }
}
