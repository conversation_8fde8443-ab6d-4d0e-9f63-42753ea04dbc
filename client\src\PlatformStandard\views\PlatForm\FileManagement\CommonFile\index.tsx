import { Component, Vue } from 'vue-property-decorator';
import { commonFileService } from './service';
import { CommonFileDto } from './types';
import { VueModule } from '@/PlatformStandard/common/defines';
import styles from './commonfile.module.less';
import { i18nHelper, notificationHelper, removeNullValueProperty } from '@/PlatformStandard/common/utils';
import { CompCard, CompTableHeader } from '@/PlatformStandard/components';
import { languageService } from '@/PlatformStandard/services/language';
import { CommonFileEdit } from './Edit/index';
@Component({
    components: { CompCard, CompTableHeader, CommonFileEdit }
})
export class CommonFile extends VueModule {
    pageActions: string[] = [];
    private fieldsSlotMap: any = {};
    private loading = false;
    private listCommonFile: CommonFileDto[] = [];
    private modelType = 'and';
    private modelShow = false;
    private fileName = '';
    private fileTag = '';
    private fileType = '';
    private fileState = 1;
    private editCommonFile: CommonFileDto = {};
    private statusDataset = i18nHelper.getLocaleObject('platform.dataPermission.statusDataset');
    private pagination = {
        total: 0,
        current: 1,
        pageSize: 10,
        showSizeChanger: true,
        size: 'small',
        showTotal: (total: string) => i18nHelper.getReplaceLocale(`${this.paginationsI18n}.total`, total),
    };
    private columns = [
        { key: 'fileName', dataIndex: 'fileName', width: '25%', scopedSlots: { customRender: 'fileName' }, slots: { title: 'fileName' } },
        { key: 'fileTag', dataIndex: 'fileTag', width: '25%', slots: { title: 'fileTag' } },
        { key: 'fileType', dataIndex: 'fileType', width: '8%', slots: { title: 'fileType' } },
        { key: 'state', dataIndex: 'state', width: '8%', scopedSlots: { customRender: 'state' }, slots: { title: 'state' } },
        { dataIndex: 'operation', scopedSlots: { customRender: 'operation' }, slots: { title: 'operation' } },
    ];
    private onTableChange(pagination: any, filters: any, sorter: any): void {
        this.pagination = pagination;
        this.fileName = '';
        this.load();
    }
    // 重载数据
    private load(pageIndex?: number): void {
        this.loading = true;
        if (pageIndex) {
            this.pagination.current = pageIndex;
        }
        const params: any = {
            'page-size': this.pagination.pageSize,
            'page-index': this.pagination.current,
            'name': this.fileName,
            'tag': this.fileTag,
            'type': this.fileType,
            'state': this.fileState
        };
        commonFileService.getCommonFiles(removeNullValueProperty(params)).subscribe(data => {
            this.pagination.total = data.total;
            this.listCommonFile = data.items;
            this.loading = false;
        });
    }
    private add() {
        this.editCommonFile = {
            state: 1
        };
        this.modelType = 'add';
        this.modelShow = true;
    }
    private edit(record: any) {
        this.editCommonFile = record;
        this.modelType = 'edit';
        this.modelShow = true;
    }
    private copyTxt(str: string) {
        this.$copyText(str).then(
            () => {
                notificationHelper.success(i18nHelper.getLocale('messages.copySuccess'));
            },
            () => {
                notificationHelper.success(i18nHelper.getLocale('messages.copyFailed'));
            }
        );
    }
    private copyLink(record: any) {
        commonFileService.previewCommonFile(record.fileId).subscribe(
            (res: any) => {
             if (res.startsWith('http')) {
                this.copyTxt(res);
             } else {
                const basePath = window.location.pathname.substring(0, window.location.pathname.indexOf('/', 1));
                this.copyTxt(`${window.location.protocol}//${window.location.host}${basePath}${res}`);
             }
             this.loading = false;
            },
            (msg: string) => {
              this.loading = false;
              this.$notify.error(msg);
            }
          );
    }
    private copyDownload(record: any) {
        commonFileService.downCommonFile(record.fileId).subscribe(
            (res: any) => {
                const basePath = window.location.pathname.substring(0, window.location.pathname.indexOf('/', 1));
                this.copyTxt(`${window.location.protocol}//${window.location.host}${basePath}${res}`);
                this.loading = false;
            },
            (msg: string) => {
              this.loading = false;
              this.$notify.error(msg);
            }
          );
    }
    private delete(record: any): void {
        commonFileService.deleteCommonFile(record.fileId || '').subscribe(data => {
            notificationHelper.success(i18nHelper.getLocale('messages.success'));
            this.load();
        });
    }
    private saveEdit(): void {
        const errorMsgs = (this.$refs.editCommonFileInfo as any).validateForm() as string[];
        if (errorMsgs.length === 0) {
            const commonFile = (this.$refs.editCommonFileInfo as any).getCommonFileInfo() as CommonFileDto;
            commonFileService.updateCommonFile(commonFile.fileId || '', commonFile).subscribe(data => {
                notificationHelper.success(i18nHelper.getLocale('messages.success'));
                this.modelShow = false;
                this.load();
            });
        } else {
            notificationHelper.error(errorMsgs);
        }
    }
    // 查询
    private onSearch(): void {
        this.load(1);
    }
    // 重置
    private onReset(): void {
        this.fileName = '';
        this.fileState = 1;
        this.load(1);
    }
    private  previewFile(id: string) {
        commonFileService.previewCommonFile(id).subscribe(
          (res: any) => {
            window.open(res, '_blank');
            this.loading = false;
          },
          (msg: string) => {
            this.loading = false;
            this.$notify.error(msg);
          }
        );
    }
    created() {
        languageService.language$.subscribe(lang => {
            this.statusDataset = i18nHelper.getLocaleObject('platform.dataPermission.statusDataset');
        });
        this.fieldsSlotMap['fileName'] = (text: number, record: any, index: number) =>  {
            return (
                <a
                  class={styles.name}
                  on-click={this.previewFile.bind(this, record.fileId)} title={record.fileNme}>
                  <span>{record.fileName}</span>
                </a>
            );
          };
        this.fieldsSlotMap['state'] = (text: number, record: any, index: number) => {
            return (
                <div>
                    {text === 1 ? this.$t('platform.fields.valid') : this.$t('platform.fields.inValid')}
                </div>
            );
        };
        this.fieldsSlotMap['operation'] = (text: number, record: any, index: number) => {
            return ([
                <a-button type='link' on-click={() => this.copyLink(record)}>
                    {this.$t('buttons.copyLink')}
                </a-button>,
                <a-button type='link' on-click={() => this.copyDownload(record)}>
                    {this.$t('buttons.copyDownload')}
                </a-button>,
                <a-button type='link' on-click={() => this.edit(record)}>
                    {this.$t('buttons.edit')}
                </a-button>,
                <a-popconfirm
                    title={this.$t('messages.delete')}
                    on-confirm={() => this.delete(record)}
                >
                    <a-button type='link'>
                        {this.$t('buttons.delete')}
                    </a-button>
                </a-popconfirm>
            ]);
        };
        this.load(1);
    }
    render() {
        return (
            <div>
                <comp-card class={styles.card_top}>
                    <comp-table-header class={styles.table_header}
                        on-search={() => {
                            this.onSearch();
                        }}
                        on-reset={() => {
                            this.onReset();
                        }}
                    >
                        <template slot='base'>
                            <a-row >
                                <a-col span='5' class='mr-1'>
                                    <a-form-item label={this.$t('platform.fields.name')}>
                                        <a-input v-model={this.fileName}></a-input>
                                    </a-form-item>
                                </a-col>
                                <a-col span='5' class='mr-1'>
                                    <a-form-item label={this.$t('platform.fields.keyword')}>
                                        <a-input v-model={this.fileTag}></a-input>
                                    </a-form-item>
                                </a-col>
                                <a-col span='5' class='mr-1'>
                                    <a-form-item label={this.$t('platform.fields.type')}>
                                        <a-input v-model={this.fileType}></a-input>
                                    </a-form-item>
                                </a-col>
                                <a-col span='5'>
                                    <a-form-item label={this.$t('platform.fields.inUse')}>
                                        <a-select v-model={this.fileState}>
                                            {this.statusDataset.map((item: any) => (
                                                <a-select-option value={item.value}>{item.label}</a-select-option>
                                            ))}
                                        </a-select>
                                    </a-form-item>
                                </a-col>
                            </a-row>
                        </template>
                    </comp-table-header>
                </comp-card>
                <comp-card>
                    <a-card class={styles.base_table} bordered={false}>
                        <div slot='extra'>
                            <a-row>
                                <a-col span='24' style='text-align:right;padding-bottom:10px'>
                                    <a-button type='primary' class={styles.common_btn} on-click={this.add}>
                                        {this.$t('buttons.add')}
                                    </a-button>
                                </a-col>
                            </a-row>
                        </div>
                        <a-table
                            size='small'
                            rowKey='fileId'
                            columns={this.columns}
                            dataSource={this.listCommonFile}
                            pagination={this.pagination}
                            scopedSlots={this.fieldsSlotMap}
                            on-change={this.onTableChange}
                        >
                            <span slot='fileName'>{this.$t('platform.fields.name')}</span>
                            <span slot='fileTag'>{this.$t('platform.fields.keyword')}</span>
                            <span slot='fileType'>{this.$t('platform.fields.type')}</span>
                            <span slot='state'>{this.$t('platform.fields.inUse')}</span>
                            <span slot='operation'>{this.$t('platform.fields.operation')}</span>
                        </a-table>
                    </a-card>
                </comp-card>
                <a-modal width='600px'
                    title={this.modelType === 'add' ? this.$t('buttons.add') : this.$t('buttons.edit')}
                    visible={this.modelShow}
                    destroyOnClose={true}
                    maskClosable={false}
                    on-cancel={(c: any) => this.modelShow = false}
                    on-ok={this.saveEdit}>
                    <common-file-edit selectCommonFile={this.editCommonFile} modelType={this.modelType} ref='editCommonFileInfo'>
                    </common-file-edit>
                </a-modal>
            </div >
        );
    }
}
