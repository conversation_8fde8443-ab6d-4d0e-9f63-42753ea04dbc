import { AES, enc, mode, pad } from 'crypto-js';

class EncryptHelper {
  encryptByEnAES(data: string, key: string = '', iv: string = ''): string {
    const tmpAES = AES.encrypt(data, enc.Utf8.parse(key), {
      iv: enc.Utf8.parse(iv),
      mode: mode.CBC,
      padding: pad.Pkcs7
    });
    return tmpAES.toString();
  }

  decryptByEnAES(data: string, key: string = '', iv: string = ''): string {
    const decrypt = AES.decrypt(data, enc.Utf8.parse(key), { iv: enc.Utf8.parse(iv), mode: mode.CBC, padding: pad.Pkcs7 });
    const decryptedStr = enc.Utf8.stringify(decrypt);
    return decryptedStr;
  }
}

export const encryptHelper = new EncryptHelper();
