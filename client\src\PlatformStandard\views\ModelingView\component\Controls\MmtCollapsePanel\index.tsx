import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator';
import styles from './collapse-panel.module.less';
import { guidHelper } from '../../../common/utils';
import Draggable from 'vuedraggable';
import { MmtFormItem } from '../MmtFormItem';
import { MmtLayout } from '../MmtLayout';
import { MmtTabPanel } from '../MmtTabPanel';
import { BaseControl } from '../base-control';
@Component({
    components: {
        Draggable,
        MmtFormItem,
        MmtLayout,
        MmtTabPanel
    }
})
export class MmtCollapsePanel extends BaseControl {
    @Prop() currentCheckedItemId!: string;
    @Prop({ default: true }) isMove!: boolean;
    @Prop({ default: true }) isEdit!: boolean;
    @Prop({ default: true }) isDeleted!: boolean;
    @Emit('checked')
    checked(item: any, isFormTable: boolean) {
    }
    @Emit('deleted')
    deleted(item: any, parentItem: any, index: number) {
    }
    private add(newIndex: number, items: any) {
        const newItem = JSON.parse(JSON.stringify(items[newIndex]));
        newItem.info.id = guidHelper.generate();
        if (newItem.info.type === 'tab-panel') {
            newItem.config.options.map((m: any) => m.value = guidHelper.generate());
        }
        items.splice(newIndex, 1, newItem);
        this.checked(items[newIndex], false);
    }
    private customTag(m: any, i: number) {
        if (m.info.type === 'form-item') {
            return (
                <mmt-form-item
                    style={{ 'margin-bottom': '5px' }}
                    controlConfig={m}
                    currentCheckedItemId={this.currentCheckedItemId}
                    key={m.info.id}
                    on-checked={() => this.checked(m, false)}
                    on-deleted={() => this.deleted(m, this.controlConfig.info.columns, i)}
                ></mmt-form-item>
            );
        } else {
            const CustomTag = `mmt-${m.info.type}`;
            return (
                <CustomTag
                    style={{ 'margin-bottom': '5px' }}
                    controlConfig={m}
                    currentCheckedItemId={this.currentCheckedItemId}
                    key={m.info.id}
                    on-checked={(item: any, isFormTable: boolean) => this.checked(item, isFormTable)}
                    on-deleted={(item: any, parentItem: any, index: number) =>
                        this.deleted(item, parentItem ? parentItem :
                            this.controlConfig.info.columns, index > -1 ? index : i)}
                ></CustomTag>
            );
        }
    }
    created(): void {
    }
    render() {
        return (
            this.controlConfig ? (
                <div class={styles.div + ` ${this.currentCheckedItemId === this.controlConfig.info.id
                    ? styles.active : null}`}>
                    <a-collapse
                        default-active-key={this.controlConfig.info.id}
                    >
                        <a-collapse-panel
                            key={this.controlConfig.info.id}
                            header={this.controlConfig.config.label}
                            show-arrow={false}
                        >
                            <draggable
                                v-model={this.controlConfig.info.columns}
                                group={{ name: 'people' }}
                                animation={200}
                                ghostClass={styles.ghost}
                                on-add={(event: any) => this.add(event.newIndex,
                                    this.controlConfig.info.columns)}
                            >
                                <transition-group
                                    name='fade'
                                    tag='div'
                                    class={styles.collapse_draggable}
                                >
                                    {
                                        this.controlConfig.info.columns.map((m: any, i: number) => (
                                            m.info.id ? (
                                                this.customTag(m, i)
                                            ) : null

                                        ))
                                    }
                                </transition-group>
                            </draggable>
                        </a-collapse-panel>
                    </a-collapse>
                    {
                        this.isEdit || this.isDeleted ? (
                            <div class={styles.form_item_action}>
                                {
                                    this.isEdit ? (
                                        <a-icon type='edit' title='属性设置'
                                            nativeOnClick={(e: any) => {
                                                this.checked(this.controlConfig, false);
                                                e.stopPropagation();
                                            }}
                                        />
                                    ) : null
                                }
                                {
                                    this.isDeleted ? (
                                        <a-icon type='delete' title='删除'
                                            nativeOnClick={(e: any) => {
                                                this.deleted(this.controlConfig, null, -1);
                                                e.stopPropagation();
                                            }}
                                        />
                                    ) : null
                                }
                            </div>
                        ) : null
                    }
                    {
                        this.isMove && this.currentCheckedItemId === this.controlConfig.info.id ? (
                            <div class={styles.form_item_drag}>
                                <a-icon type='drag' class='drag-widget' />
                            </div>
                        ) : null
                    }
                </div>
            ) : null
        );
    }
}
