import { Component, Vue, Prop, Watch } from 'vue-property-decorator';

import { getComponentFromProp } from '@/PlatformStandard/common/utils';
import styles from './component.comp-card.module.less';

@Component
export class CompCard extends Vue {
  @Prop() title!: string;
  @Prop({ default: true }) defaultStyle!: boolean;
  @Prop({ default: true }) bordered!: boolean;
  @Prop({ default: false }) cardTitleBg!: boolean;

  render() {
    const extraDom = getComponentFromProp(this, 'extra');
    const titleDom = getComponentFromProp(this, 'title');
    return (<div>
      {this.cardTitleBg ? <a-card class={this.cardTitleBg ? styles.cardTitleBg : undefined}>
      {titleDom ? <template slot='title'>{titleDom}</template> : null}
      {extraDom ? <template slot='extra'>{extraDom}</template> : null}
      {this.$slots.default}
    </a-card> :  <a-card class={this.defaultStyle ? styles.card : undefined}>
      {titleDom ? <template slot='title'>{titleDom}</template> : null}
      {extraDom ? <template slot='extra'>{extraDom}</template> : null}
      {this.$slots.default}
    </a-card>}
    </div>
    );
  }
}
