import { Observable, of } from 'rxjs';

import { httpHelper } from '@/PlatformStandard/common/utils';
import { map } from 'rxjs/operators';
import { DictionaryDto } from './dictionary.types';
import { ValueLabelPair } from '@/PlatformStandard/common/defines';

class DictionaryService {
  /**
   * 字典树
   * @param parentId 父级字典ID
   */
  getTreeDictionaries(parentId: string): Observable<any[]> {
    const path = '/api/platform/v1/manage/tree-dictionaries';
    return httpHelper
      .get(path, { params: { 'parent-id': parentId } })
      .pipe(map(data => data.map((m: any) => ({ ...m, title: m.label, key: m.value, datum: m.code }))));
  }

  /**
   * 字典列表
   * @param groupCode 分组编码
   * @param pageIndex 页码
   * @param pageSize 页条
   */
  getDictionaryList(groupCode: string, pageIndex: number, pageSize: number): Observable<any> {
    if (!groupCode) {
      return of({});
    }
    const path = '/api/platform/v1/manage/dictionaries';
    return httpHelper.get(path, { params: { 'group-id': groupCode, 'page-index': pageIndex + '', 'page-size': pageSize + '' } });
  }

  /**
   * 获取字典详情
   * @param id 字典ID
   */
  getDictionary(id: string): Observable<any> {
    const path = `/api/platform/v1/manage/dictionaries/${id}`;
    return httpHelper.get(path);
  }

  /**
   * 字典更新
   * @param id 字典ID
   * @param data 字典更新数据
   */
  updateDictionary(id: string, data: DictionaryDto): Observable<void> {
    const path = `/api/platform/v1/manage/dictionaries/${id}`;
    return httpHelper.put(path, data);
  }

  /**
   * 字典删除
   * @param id 字典ID
   */
  deleteDictionary(id: string): Observable<void> {
    const path = `/api/platform/v1/manage/dictionaries/${id}`;
    return httpHelper.delete(path);
  }

  /**
   * 字典添加
   * @param data 添加数据
   */
  addDictionary(data: DictionaryDto): Observable<void> {
    const path = '/api/platform/v1/manage/dictionaries';
    return httpHelper.post(path, data);
  }

  /**
   * 字典分类
   * @param group 分类编码
   */
  groupDictionary(group: string): Observable<any> {
    const path = '/api/platform/v1/manage/tree-dictionaries';
    return httpHelper
      .get(path, { params: { code: group } })
      .pipe(map(data => data.map((m: any) => ({ label: m.label, value: m.parameter, code: m.code, id: m.value }))));
  }

  /**
   * 获取字典选择列表
   * @param group 分类编码
   */
  getDictionaries(params: string[]): Observable<{ [key: string]: ValueLabelPair[] } | ValueLabelPair[]> {
    let paramsString = '';
    params.forEach((item: string, index: number) => {
      paramsString += `${index === 0 ? '?' : '&'}group=${item}`;
    });
    const _url = `/api/platform/v1/select-dictionaries${paramsString}`;
    return httpHelper.get(_url);
  }
}

export const dictionaryService = new DictionaryService();
