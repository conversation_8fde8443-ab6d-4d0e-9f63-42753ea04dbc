import { environment } from './../environment';
import Koa from 'koa';
import agent from 'superagent';
import { objectToBase64 } from '../utils/base64';
import { noStateHandle } from '../utils/no-auth-state-handle';
import { getSsoConfig } from '../utils/sso-config';
import { removeUserState } from '../utils/user-state';
import { getUserInfo } from '../utils/user-info';

export function authStateCheck(): Koa.Middleware {
  return async (ctx: Koa.Context, next: () => Promise<any>) => {
    let stopPipeline = false;
    if ((ctx.path === `/${environment.urlKey}/auth/state` || ctx.path === '/auth/state') && ctx.method === 'GET') {
      console.log('authStateCheck middleware triggered for:', ctx.path);

      // 临时模拟用户会话 - 仅用于调试
      if (!ctx.session) {
        ctx.session = {
          cookie: { maxAge: 3600000, tick: Date.now() }
        } as any;
      }
      if (!ctx.session.user) {
        ctx.session.user = {
          id: 'test-user',
          name: 'Test User',
          language: 'zh'
        };
      }
      // 如果开启 SSO，从服务器换取用户状态
      const ssoConfig = getSsoConfig(ctx);
      if (
        ssoConfig &&
        ctx.session &&
        ctx.session.user &&
        ctx.session.accessToken
      ) {
        const { redirectUri, loginUri, userStateUri, callbackUri, localNetwork } = ssoConfig;
        await agent
          .get((localNetwork || redirectUri) + userStateUri)
          .set('Authorization', `Bearer ${ctx.session.accessToken}`)
          .then(async res => {
            const { account } = res.body;
            if (ctx.session.user && !ctx.session.user.id) {
              await getUserInfo(ctx, account);
            }
          })
          .catch(err => {
            // 出错清空用户状态
            removeUserState(ctx);
            ctx.status = 308;
            ctx.body = redirectUri + loginUri + encodeURI(`?callBackUrl=${callbackUri}`);
            // 终止请求
            stopPipeline = true;
          });
      }

      if (noStateHandle(ctx)) {
        // 终止请求
        stopPipeline = true;
      }
    }

    if (ctx.session && ctx.session.user) {
      if (!ctx.session.user.language) {
        ctx.session.user.language = 'zh';
      }
      ctx.header['current_user'] = objectToBase64(ctx.session.user);
    }

    if (!stopPipeline) {
      return await next();
    }
  };
}
