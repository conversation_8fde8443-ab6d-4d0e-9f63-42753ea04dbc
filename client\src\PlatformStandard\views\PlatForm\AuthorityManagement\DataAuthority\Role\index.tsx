import { Component, Vue } from 'vue-property-decorator';
import styles from './roles.module.less';
import { RoleDto } from './types';
import { roleService } from './service';
import { i18nHelper, notificationHelper } from '@/PlatformStandard/common/utils';
@Component

export class Roles extends Vue {
  private fieldsSlotMap: any = {};
  private listRole: RoleDto[] = [];
  private selectedRowKeys: string[] = [];
  private selectedRows: any = [];
  private authorityId = '';
  private authorityName = '';
  private columns = [
    { key: 'roleCode', dataIndex: 'roleCode', width: '40%', slots: { title: 'roleCode' } },
    { key: 'roleName', dataIndex: 'roleName', width: '40%', slots: { title: 'roleName' } },
  ];
  private pagination = {
    total: 0,
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    size: 'default',
    showTotal: (total: string) =>
      this.$l
        .getLocale('paginations.total')
        .toString()
        .replace('{{value}}', total),
  };
  private load() {
    this.authorityId = this.$route.query['id'] as string;
    const params = { 'page-index': this.pagination.current, 'page-size': this.pagination.pageSize };
    roleService.getRoles(params).subscribe(data => {
      this.listRole = data.items;
      this.pagination.total = data.total;
      this.$forceUpdate();
      roleService.getDataAuthoritysRole(this.authorityId).subscribe(s => {
        this.selectedRowKeys = [];
        this.selectedRows = [];
        s.forEach(item => {
          this.selectedRowKeys = [...this.selectedRowKeys, item.roleId];
          this.selectedRows.push({ id: item.roleId, name: item.roleName });
        });
      });
    });
  }
  private get rowSelection() {
    return {
      selectedRowKeys: this.selectedRowKeys,
      onChange: (selectedRowKeys: any, selectedRows: any) => {
        this.selectedRowKeys = selectedRowKeys;
        this.selectedRows = selectedRows;
      },
      /* getCheckboxProps: (record:any) => ({
        props: {
          defaultChecked: record.code === 'MY_ERP', // Column configuration not to be checked
          name: record.name,
        }
      }) */
    };
  }
  private onTableChange(pagination: any, filters: any, sorter: any): void {
    this.pagination = pagination;
    this.load();
  }

  private save() {
    const saveData = this.selectedRows
      .map((m: any) => {
        return {
          id: m.roleId,
          name: m.roleName,
        };
      });
    roleService.saveDataAuthoritysRole(this.authorityId, saveData).subscribe(s => {
      notificationHelper.success(i18nHelper.getLocale('messages.success'));
    });
  }
  back() {
    this.$router.push('/authority-management/data-authority');
  }
  created() {
    this.authorityId = this.$route.query['id'] as string;
    this.authorityName = this.$route.query['name'] as string;
    this.load();

  }
  render() {
    return <a-card title={this.$t('bpm.data-authority.name') + '：' + this.authorityName}
    bodyStyle={{ overflow: 'auto', height: '100%' }}>
    <div slot='extra'>
      <a-button on-click={this.back}>
        {this.$t('buttons.back')}
      </a-button>
    </div>
      <a-row class={styles.row} gutter={8} align='middle' type='flex'>
        <a-button type='primary' on-click={this.save}>
          {this.$t('buttons.save')}
        </a-button>
      </a-row>

      <a-table
        size='small'
        rowKey='roleId'
        columns={this.columns}
        dataSource={this.listRole}
        pagination={this.pagination}
        on-change={this.onTableChange}
        scopedSlots={this.fieldsSlotMap}
        row-selection={this.rowSelection}
      >
        <span slot='roleCode'>{this.$t('platform.fields.code')}</span>
        <span slot='roleName'>{this.$t('platform.fields.name')}</span>
      </a-table>
    </a-card>;
  }
}
