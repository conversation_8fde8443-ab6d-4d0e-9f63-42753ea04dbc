@import '../../../../../themes/default/variables.less';

.page {
  display: flex;
  .tree_list {
    // margin: @card-padding-base 0px @card-padding-base @card-padding-base;
    width: 330px;
    height: 510px;
    .tree_card {
      width: 100%;
      display: flex !important;
      flex-direction: column;
      height: 100%;
      :global(.ant-card-body) {
        height: calc(~'100%' - 51px);
      }
    }
  }
  .content_info {
     width: 100%;
    .edit_info {
      width: 100%;
      margin-left: @card-padding-base;
      .info_card {
        display: flex !important;
        width: 100%;
        flex-direction: column;
        height: auto;
        .buttons {
          height: @btn-height-sm;
        }
      }
    }
    .edit_info2 {
      width: 100%;
      margin-top: @base-size;
      margin-left: @card-padding-base;
      .info_card {
        display: flex !important;
        width: 100%;
        flex-direction: column;
        height: auto;
        .buttons {
          height: @btn-height-sm;
        }
        .row_outer{
          background-color: @content-bg-1;
          margin: @base-size 0px !important;
        }
        .row{
          padding: @content-padding-size;
        }
        .operation{
          text-align: right;
          cursor: pointer;
        }
      }
    }
    .list_button {
      padding: 0 3px !important
    } 
  }
}
:global(.ant-tree li .ant-tree-node-content-wrapper){
  margin-left: -10px !important;
}