import moment from 'moment';
import { Component, Prop, Vue } from 'vue-property-decorator';
import { BaseControl } from '../base-control';

@Component({
})
export class MmtDatePicker extends BaseControl {
    private onDefaultChange(date: any, dateString: any) {
        let cdata = '';
        if (this.controlConfig.config.model === 'daterange') {
            cdata = dateString.join(';');
        } else {
            cdata = dateString;
        }
        this.onChange(cdata);
    }
    get defaultValue() {
        if (this.controlConfig.config.model === 'daterange') {
            const defaultValues = [];
            if (this.value) {
                const arrs = this.value.split(';');
                let start = moment(arrs[0], this.controlConfig.config.format) as any;
                if (!start.isValid()) {
                    start = undefined;
                }
                let end = moment(
                    arrs.length > 0 ? arrs[1] : '',
                    this.controlConfig.config.format
                ) as any;
                if (!end.isValid()) {
                    end = undefined;
                }
                defaultValues.push(start);
                defaultValues.push(end);
            }
            return defaultValues;
        } else {
            let defaultValues = null;
            if (this.value) {
                const start = moment(
                    this.value,
                    this.controlConfig.config.format
                );
                if (start.isValid()) {
                    defaultValues = start;
                }
            }
            return defaultValues;
        }
    }
    created(): void {
    }
    render() {
        return (
            this.pageModel === 'view' ? (
                <div>{this.value}</div>
            ) : (
                this.controlConfig.config.model === 'date' ? (
                    <a-date-picker
                        value={this.defaultValue}
                        placeholder={this.controlConfig.config.placeholder}
                        disabled={!this.controlConfig.config.disabled}
                        format={this.controlConfig.config.format}
                        showTime={this.controlConfig.config.format === 'YYYY-MM-DD' ? false : true}
                        style={{ width: '100%' }}
                        on-change={this.onDefaultChange}
                    ></a-date-picker>
                ) : (
                    <a-range-picker
                        value={this.defaultValue}
                        disabled={!this.controlConfig.config.disabled}
                        format={this.controlConfig.config.format}
                        showTime={this.controlConfig.config.format === 'YYYY-MM-DD' ? false : true}
                        style={{ width: '100%' }}
                        on-change={this.onDefaultChange}
                    ></a-range-picker>
                )
            ));
    }
}
