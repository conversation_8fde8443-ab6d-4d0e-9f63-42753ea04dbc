import { Observable } from 'rxjs';
import { httpHelper } from '@/PlatformStandard/common/utils';
import { DataAuthorityListDto } from './types';
class DataAuthorityService {
    getDataAuthoritys(params: any): Observable<DataAuthorityListDto> {
        const _url = `/api/platform/v1/manage/data-authoritys`;
        return httpHelper.get(_url, { params });
    }
    updateDataAuthority(id: string, data: any): Observable<void> {
        const _url = `/api/platform/v1/manage/data-authority/${id}`;
        return httpHelper.put(_url, data);
    }
    addDataAuthority(data: any): Observable<void> {
        const _url = `/api/platform/v1/manage/data-authority`;
        return httpHelper.post(_url, data);
    }
    deleteDataAuthority(id: string): Observable<void> {
        const path = `/api/platform/v1/manage/data-authority/${id}`;
        return httpHelper.delete(path);
    }
}
export const dataAuthorityService = new DataAuthorityService();
