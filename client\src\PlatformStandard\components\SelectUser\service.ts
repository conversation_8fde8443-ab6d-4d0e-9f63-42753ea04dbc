import { Observable } from 'rxjs';

import { httpHelper } from '@/PlatformStandard/common/utils';

class DepartmentService {
  getDepartmentTree(params: any): Observable<any[]> {
    const _url = '/api/platform/v1/manage/tree-organizations';
    return httpHelper.get(_url, { params });
  }

  getOrganization(organizationId: string): Observable<any> {
    const _url = `/api/platform/v1/manage/bpm/organization/${organizationId}`;
    return httpHelper.get(_url);
  }

  getUserList(id: string, invalidState: number, workingState: number): Observable<any> {
    // 查询有效的用户
    // const _url = `/api/platform/v1/select-users?keyword=${id}&count=20`;
    // 根据传入的状态找对应的用户
    const _url = `/api/platform/v1/select-users-status?keyword=${id}&count=20&invalidState=${invalidState}&workingState=${workingState}`;
    return httpHelper.get(_url, {}, { loading: false});
  }

  getUserListByOrg(id: string, invalidState: number, workingState: number): Observable<any> {
    // const _url = `/api/platform/v1/organizations/${id}/select-users`;
    const _url = `/api/platform/v1/organizations/${id}/select-users-status?invalidState=${invalidState}&workingState=${workingState}`;
    return httpHelper.get(_url);
  }
}

export const departmentService = new DepartmentService();
