import { i18nHelper, notificationHelper } from '@/PlatformStandard/common/utils';
import i18n from '@/i18n';
import { languageService } from '@/PlatformStandard/services/language';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Component, Prop, Vue } from 'vue-property-decorator';
import styles from './component.comp-expression-designer.module.less';
import { expressionDesignerService } from './service';
import { Attribute, EditItem, OperatorItem } from './types';

@Component
export class CompExpressionDesigner extends Vue {
  // 入参:表达式值
  @Prop() expression!: string;
  // 入参:业务对象编号
  @Prop() businessObjectCode!: string;
  private infoLoading = false;
  // 数据源
  private dataSource: { businessObjectList: Attribute[]; operatorList?: OperatorItem[]; logicalOperatorList?: OperatorItem[] } = {
    // 业务对象
    businessObjectList: [],
    // 符号集合
    operatorList: [],
    // 逻辑符号集合
    logicalOperatorList: []
  };
  // 编辑集合
  private editList: EditItem[] = [];
  // 编辑符号左右边编辑
  private editOperatorLR = {
    lr: '',
    editIndex: -1,
    search: '',
    businessObjectList: [{}],
    customType: '',
    customInput: ''
  };
  // 编辑符号
  private editOperator = {
    editIndex: -1,
  };
  // 编辑逻辑符号
  private editLogicalOperator = {
    editIndex: -1,
  };
  // 选显卡选中索引
  private nzSelectedIndex = 0;

  created() {
    // 符号集合
    this.dataSource.operatorList = JSON.parse(
      JSON.stringify(i18n.t(`components.comp-expression-designer.operatorList`))) as OperatorItem[];
    // 逻辑符号集合
    this.dataSource.logicalOperatorList =
      JSON.parse(JSON.stringify(i18n.t(`components.comp-expression-designer.logicalOperatorList`))) as OperatorItem[];
    languageService.language$.subscribe(() => {
      // 符号集合
      this.dataSource.operatorList =
        JSON.parse(JSON.stringify(i18n.t(`components.comp-expression-designer.operatorList`))) as OperatorItem[];
      // 逻辑符号集合
      this.dataSource.logicalOperatorList =
        JSON.parse(JSON.stringify(i18n.t(`components.comp-expression-designer.logicalOperatorList`))) as OperatorItem[];
    });
    // 获取业务对象
    this.infoLoading = true;
    expressionDesignerService.getBusinessObjectDetail(this.businessObjectCode)
      .subscribe(data => {
        this.infoLoading = false;
        this.dataSource.businessObjectList = data;
        this.editOperatorLRInit();
        this.fromExpressionString();
      });
  }

  // 从表达式字符串转换集合
  private fromExpressionString() {
    if (!this.expression || this.expression === '') {
      return;
    }
    try {
      this.editList = this.getEditItemListByStr(this.expression.replace(/[\n\r]/g, '')
        .replace(/\\\"/g, '[@:“]').replace(/\\\'/g, '[@:’]'), 0);
    } catch { }
    if (!this.editList) {
      this.editList = [];
    } else {
      this.editList = this.editList.filter((value, index) => value);
      if (!this.editList) {
        this.editList = [];
      }
    }
  }

  // 根据字符串获取Edititem集合
  private getEditItemListByStr(str: string, tabLength: number): EditItem[] {
    if (!str || str === '') {
      return [];
    }

    let itemList: EditItem[] = [];
    const itemL: EditItem = { tabLength: tabLength };
    const itemC: EditItem = { tabLength: tabLength };
    const itemR: EditItem = { tabLength: tabLength };

    const firstChar = str.substring(0, 1);

    if (firstChar === '(') {
      // 左括号
      str = str.substring(1);
      itemL.brackets = '(';
      itemList.push(itemL);
      if (str && str.length > 0) {
        // 如果后续还有字符串继续递归查找
        itemList = itemList.concat(this.getEditItemListByStr(str, itemC.tabLength + 1));
      }
    } else if (firstChar !== '(' && firstChar !== ')') {
      // 不是左右括号
      let isNot = false; // 是否有非
      let index = str.length;
      let operator!: OperatorItem; // 中间符号
      let oL: string;
      let oR: string; // 符号左右边的字符串
      let oLFirstChar: { isQ: boolean, str: string };
      let oRFirstChar: { isQ: boolean, str: string }; // 符号左右边的第一个字符
      let oLEnd = 0;
      let oREnd = 0; // 查找的符号左右边的引号结束的位置
      // 判断是否 Not
      if (str.startsWith('!')) {
        isNot = true;
        str = str.substring(1);
      }

      oLFirstChar = this.getFirstChar(str);
      if (oLFirstChar.isQ) {
        oLEnd = str.indexOf(oLFirstChar.str, 1);
      }

      // 查找匹配的符号(最前面)
      if (this.dataSource.operatorList) {
        this.dataSource.operatorList.forEach(o => {
          if (str.indexOf(o.expression, oLEnd) > -1 && str.indexOf(o.expression, oLEnd) <= index) {
            index = str.indexOf(o.expression, oLEnd);
            operator = o;
          }
        });
        // 如果找到符号
        if (operator) {
          // 获取符号
          {
            // Not符号的存在重叠可能,需要再次判断下
            if (!isNot && operator.value.includes('Not ')) {
              operator = this.dataSource.operatorList.find(o => o.value === operator.value.replace('Not ', '')) as OperatorItem;
            }
            if (isNot && !operator.value.includes('Not ')) {
              operator = this.dataSource.operatorList.find(o => o.value === `Not ${operator.value}`) as OperatorItem;
            }
            itemC.operator = operator;
          }

          // 获取符号左边的
          {
            oL = str.substring(0, str.indexOf(operator.expression, oLEnd));
            itemC.operatorL = this.getAttributeByValue(oL.replace(/\[@:“\]/g, '\\\"').replace(/\[@:’\]/g, '\\\''));
          }

          // 获取符号右边的
          {
            str = str.substring(str.indexOf(operator.expression, oLEnd) + operator.expression.length);
            // 如果符号是函数方法
            if (operator.expression.includes('.')) {
              str = str.substring(1);
              oRFirstChar = this.getFirstChar(str);
              if (oRFirstChar.isQ) {
                oREnd = str.indexOf(oRFirstChar.str, 1);
              }
              // 如果是方法符号,根据右括号找到结尾
              oR = str.substring(0, str.indexOf(')', oREnd));
              str = str.substring(str.indexOf(')', oREnd) + 1);
            } else {
              oRFirstChar = this.getFirstChar(str);
              if (oRFirstChar.isQ) {
                oREnd = str.indexOf(oRFirstChar.str, 1);
              }
              // 如果是非方法符号,需要根据&&,||,),没有了  4种情况(按最前面)找到结尾
              index = str.length;
              let tempEnd!: string;

              ['&&', '||', ')'].forEach(o => {
                if (str.indexOf(o, oREnd) > -1 && str.indexOf(o, oREnd) < index) {
                  index = str.indexOf(o, oREnd);
                  tempEnd = o;
                }
              });

              if (tempEnd && tempEnd !== '') {
                // 如果找到&&,||,)
                oR = str.substring(0, str.indexOf(tempEnd, oREnd));
                str = str.substring(str.indexOf(tempEnd, oREnd));
              } else {
                // 如果后面没有了
                oR = str;
                str = '';
              }
            }

            itemC.operatorR = this.getAttributeByValue(oR.replace(/\[@:“\]/g, '\\\"').replace(/\[@:’\]/g, '\\\''));
          }

          // 获取逻辑符号
          {
            if (str.startsWith('&&') || str.startsWith('||')) {
              itemC.logicalOperator = (this.dataSource.logicalOperatorList as OperatorItem[])
                .find(o => o.expression === str.substring(0, 2));
              str = str.substring(2);
            }
          }

          itemList.push(itemC);
          if (str && str.length > 0) {
            // 如果后续还有字符串继续递归查找
            itemList = itemList.concat(this.getEditItemListByStr(str, itemC.tabLength));
          }
        }
      }
    } else {
      // 右括号
      itemR.tabLength--;
      str = str.substring(1);
      itemR.brackets = ')';
      // 获取逻辑符号
      {
        if (str.startsWith('&&') || str.startsWith('||')) {
          itemR.logicalOperator = (this.dataSource.logicalOperatorList as OperatorItem[]).find(o => o.expression === str.substring(0, 2));
          str = str.substring(2);
        }
      }
      itemList.push(itemR);
      if (str && str.length > 0) {
        // 如果后续还有字符串继续递归查找
        itemList = itemList.concat(this.getEditItemListByStr(str, itemR.tabLength));
      }
    }
    return itemList;
  }

  // 是否有引号str
  private getFirstChar(str: string): { isQ: boolean, str: string } {
    if (!str || str === '') {
      return { isQ: false, str: '' };
    }
    if (str.substring(0, 1) === '\'' || str.substring(0, 1) === '"') {
      return { isQ: true, str: str.substring(0, 1) };
    } else {
      return { isQ: false, str: '' };
    }
  }

  // 获取Attribute根据值
  private getAttributeByValue(val: string): Attribute {
    if (!val || val === '') {
      return { label: val, value: '', type: 'number' };
    }
    // 是否引号
    const firstChar = this.getFirstChar(val);
    let valueWithoutQ: string = val;
    if (firstChar.isQ) {
      // 如果有引号,去掉首尾
      valueWithoutQ = val.substring(1, val.length - 2);
    }
    if (!this.dataSource.businessObjectList || this.dataSource.businessObjectList.length === 0) {
      return { label: val, value: '', type: firstChar.isQ ? 'text' : 'number' };
    }
    // 从业务对象中匹配
    const busObj = this.dataSource.businessObjectList.find(o => `[@:${o.value}]` === valueWithoutQ);
    if (busObj) {
      return busObj;
    } else {
      return { label: val, value: '', type: firstChar.isQ ? 'text' : 'number' };
    }
  }

  // 从集合转换表达式字符串
  private toExpressionString() {
    if (this.nzSelectedIndex === 0) {
      let str = '';
      if (!this.editList || this.editList.length === 0) {
        return str;
      }
      this.editList.forEach(o => {
        if (o.brackets && o.brackets !== '') {
          str = str + `${o.brackets}`;
        }
        if (o.operator) {
          switch (o.operator.value) {
            case 'Contains':
            case 'StartsWith':
            case 'EndsWith': {
              if (o.operator) {
                str = str + `${this.getOperatorLRValue(o.operatorL as Attribute)}${o.operator.expression}(${this.getOperatorLRValue(o.operatorR as Attribute)})`;
              }
              break;
            }
            case 'Not Contains':
            case 'Not StartsWith':
            case 'Not EndsWith': {
              if (o.operator) {
                str = str + `!${this.getOperatorLRValue(o.operatorL as Attribute)}${o.operator.expression}(${this.getOperatorLRValue(o.operatorR as Attribute)})`;
              }
              break;
            }
            default: {
              str = str + `${this.getOperatorLRValue(o.operatorL as Attribute)}${o.operator.value}${this.getOperatorLRValue(o.operatorR as Attribute)}`;
              break;
            }
          }
        }

        if (o.logicalOperator) {
          str = str + `${o.logicalOperator.value}`;
        }
      });
      return str;
    } else {
      return this.expression;
    }
  }

  // 获取并检查表达式
  getAndCheckExpression(): Observable<{ status: boolean, data?: any }> {
    const exp = this.toExpressionString();
    if (exp === '') {
      return new Observable(observer => {
        observer.next({ status: true, data: exp });
      });
    }
    return expressionDesignerService.checkExpression(exp, this.businessObjectCode).pipe(map(m => {
      return { status: m, data: exp };
    }));
  }

  // 获取符号左右边值
  private getOperatorLRValue(attr: Attribute): string {
    let str = '';
    if (!attr) {
      return str;
    }
    if (attr.value && attr.value !== '') {
      str = `[@:${attr.value}]`;
    } else {
      str = `${attr.label}`;
    }
    return str;
  }

  // 添加行
  private editAdd(index: number) {
    let tabLength = 0;
    // index:-1为初始空白时
    if (index > -1) {
      tabLength = this.editList[index].tabLength || 0;
      if (this.editList && this.editList[index].brackets === '(') {
        // 如果是左括号tab加1
        tabLength++;
      } else {
        // 如果不是 本行增加逻辑符号
        this.editList[index].logicalOperator = (this.dataSource.logicalOperatorList as OperatorItem[]).find(o => o.value === '&&');
      }
    }
    if (this.editList) {
      this.editList.splice(index + 1, 0, {
        operatorL: { label: '???', value: '', type: 'text' },
        operator: (this.dataSource.operatorList as OperatorItem[]).find(o => o.value === '=='),
        operatorR: { label: '???', value: '', type: 'text' },
        // 新增行是否需要增加 逻辑符号(最后一行或后一行是右括号不需要增加)
        logicalOperator: index === this.editList.length - 1 || this.editList[index + 1].brackets === ')'
          ? undefined : (this.dataSource.logicalOperatorList as OperatorItem[]).find(o => o.value === '&&'),
        tabLength: tabLength
      });
    }
  }

  // 添加行(带括号)
  private editAdd_Bracketed(index: number) {
    let tabLength = 0;
    // index:-1为初始空白时
    if (index > -1) {
      tabLength = this.editList[index].tabLength || 0;
      if (this.editList && this.editList[index].brackets === '(') {
        // 如果是左括号tab加1
        tabLength++;
      } else {
        // 如果不是 本行增加逻辑符号
        this.editList[index].logicalOperator = (this.dataSource.logicalOperatorList as OperatorItem[]).find(o => o.value === '&&');
      }
    }
    if (this.editList) {
      const isAddlogicalOperator = !(index === this.editList.length - 1 || this.editList[index + 1].brackets === ')');

      this.editList.splice(index + 1, 0, {
        brackets: '(',
        tabLength: tabLength
      },
        {
          operatorL: { label: '???', value: '', type: 'text' },
          operator: (this.dataSource.operatorList as OperatorItem[]).find(o => o.value === '=='),
          operatorR: { label: '???', value: '', type: 'text' },
          tabLength: tabLength + 1
        },
        {
          brackets: ')',
          // 新增行是否需要增加 逻辑符号(最后一行或后一行是右括号不需要增加)
          logicalOperator: isAddlogicalOperator ?
            (this.dataSource.logicalOperatorList as OperatorItem[]).find(o => o.value === '&&') : undefined,
          tabLength: tabLength
        });
    }
  }

  // 编辑符号左右边初始化
  private editOperatorLRInit() {
    this.editOperatorLR.lr = '';
    this.editOperatorLR.editIndex = -1;
    this.editOperatorLR.search = '';
    this.editOperatorLR.businessObjectList = this.dataSource.businessObjectList;
    this.editOperatorLR.customType = 'text';
    this.editOperatorLR.customInput = '';
  }

  // 编辑符号左右边搜索
  private operatorLRSearchChange() {
    this.editOperatorLR.businessObjectList =
      this.dataSource.businessObjectList.filter(o => String(o.label).indexOf(this.editOperatorLR.search) > -1
        || String(o.value).indexOf(this.editOperatorLR.search) > -1);
  }

  // 符号左右边点击
  private operatorLRClick(lr: string, index: number) {
    this.editOperatorLRInit();
    this.editOperatorLR.lr = lr;
    this.editOperatorLR.editIndex = index;
  }

  // 符号左右边选中
  private operatorLRSelect(item: Attribute) {
    if (this.editOperatorLR.lr === 'L') {
      this.editList[this.editOperatorLR.editIndex].operatorL = item;
      this.editList[this.editOperatorLR.editIndex].popoverVisibleL = false;
    } else if (this.editOperatorLR.lr === 'R') {
      this.editList[this.editOperatorLR.editIndex].operatorR = item;
      this.editList[this.editOperatorLR.editIndex].popoverVisibleR = false;
    }
  }

  // 符号左右边自定义
  private operatorLRCudtomClick() {
    if (this.editOperatorLR.customInput === '') {
      return;
    }
    const attr: Attribute = { value: '', type: this.editOperatorLR.customType };
    if (this.editOperatorLR.customType === 'text') {
      this.editOperatorLR.customInput = this.editOperatorLR.customInput.replace(/\"/g, '\\\"').replace(/\'/g, '\\\'');
      attr.label = `"${this.editOperatorLR.customInput}"`;
    } else {
      if (!parseFloat(this.editOperatorLR.customInput)) {
        notificationHelper.error('请输入数字');
        return;
      }
      attr.label = this.editOperatorLR.customInput;
    }
    if (this.editOperatorLR.lr === 'L') {
      this.editList[this.editOperatorLR.editIndex].operatorL = attr;
      this.editList[this.editOperatorLR.editIndex].popoverVisibleL = false;
    } else if (this.editOperatorLR.lr === 'R') {
      this.editList[this.editOperatorLR.editIndex].operatorR = attr;
      this.editList[this.editOperatorLR.editIndex].popoverVisibleR = false;
    }
  }

  // 符号点击
  private operatorClick(index: number) {
    this.editOperator.editIndex = index;
  }

  // 符号选中
  private operatorSelect(item: OperatorItem) {
    this.editList[this.editOperator.editIndex].operator = item;
    this.editList[this.editOperator.editIndex].popoverVisibleO = false;
  }

  // 逻辑符号点击
  private logicalOperatorClick(index: number) {
    this.editLogicalOperator.editIndex = index;
  }

  // 逻辑符号选中
  private logicalOperatorSelect(item: OperatorItem) {
    this.editList[this.editLogicalOperator.editIndex].logicalOperator = item;
    this.editList[this.editLogicalOperator.editIndex].popoverVisibleLO = false;
  }

  // 删除行
  private editDelete(index: number) {
    if (!this.editList[index].brackets || this.editList[index].brackets === '') {
      if (index === this.editList.length - 1 || this.editList[index + 1].brackets === ')') {
        // 如果是(最后一行或后一行是右括号),上一行的逻辑符号需要去掉
        if (index > 0) {
          this.editList[index - 1].logicalOperator = undefined;
        }
      }
      // 没有括号删除
      this.editList.splice(index, 1);
    } else {
      // 有括号删除
      if (this.editList[index].brackets === '(') {
        let lastIndex = index + 1;
        for (; lastIndex < this.editList.length; lastIndex++) {
          if (this.editList[lastIndex].brackets === ')' && this.editList[index].tabLength === this.editList[lastIndex].tabLength) {
            break;
          }
        }
        if (lastIndex === this.editList.length - 1 || this.editList[lastIndex + 1].brackets === ')') {
          if (index > 0) {
            // 如果是(最后一行或后一行是右括号),上一行的逻辑符号需要去掉
            this.editList[index - 1].logicalOperator = undefined;
          }
        }
        this.editList.splice(index, lastIndex - index + 1);
      } else if (this.editList[index].brackets === ')') {
        let firstIndex = index - 1;
        for (; firstIndex >= 0; firstIndex--) {
          if (this.editList[firstIndex].brackets === '(' && this.editList[index].tabLength === this.editList[firstIndex].tabLength) {
            break;
          }
        }
        if (index === this.editList.length - 1 || this.editList[index + 1].brackets === ')') {
          if (firstIndex > 0) {
            // 如果是(最后一行或后一行是右括号),上一行的逻辑符号需要去掉
            this.editList[firstIndex - 1].logicalOperator = undefined;
          }
        }
        this.editList.splice(firstIndex, index - firstIndex + 1);
      }
    }
  }

  render() {
    return (
      <div>
        <a-tabs defaultActiveKey={'1'}>
          <a-tab-pane key='1' tab={this.$t('components.comp-expression-designer.simpleEdit')}>
            <a-card loading={this.infoLoading}>
              <div class={styles.list}>
                {
                  !this.editList || this.editList.length === 0 ?
                    <div>
                      <span class={styles.operation} style='display:inline-block;'>
                        <a-tag on-click={() => this.editAdd(-1)}>+</a-tag>
                        <a-tag on-click={() => this.editAdd_Bracketed(-1)}>(+)</a-tag>
                      </span>
                    </div>
                    :
                    this.editList.map((item, i) => (
                      <div>
                        {/* 前缀空格 */}
                        {
                          item.tabLength ?
                            <span domPropsInnerHTML={'&nbsp;&nbsp;&nbsp;&nbsp;'.repeat(item.tabLength)}></span>
                            :
                            null
                        }
                        {/* 括号 */}
                        {
                          item.brackets && item.brackets !== '' ?
                            <span>
                              {item.brackets}
                            </span>
                            :
                            null
                        }
                        {/* 符号左边 */}
                        {
                          item.operatorL ?
                            <a-popover
                              class={styles.operatorL}
                              trigger={'click'}
                              placement={'bottomLeft'}
                              on-click={() => this.operatorLRClick('L', i)}>
                              {item.operatorL.label}
                              {/* 符号左右边弹出 */}
                              <div slot='content'>
                                <div class={styles.operatorLR}>
                                  <div>
                                    <a-input-group size='small'>
                                      <a-icon slot='suffix' type='search' />
                                      <a-input
                                        v-model={this.editOperatorLR.search}
                                        placeholder={this.$l.getLocale('controls.serachText')}
                                        on-change={this.operatorLRSearchChange}
                                      ></a-input>
                                    </a-input-group>
                                  </div>
                                  <h4 class={styles.h4}>{this.$t('components.comp-expression-designer.businessObjects')}:</h4>
                                  <ul class={styles.ul}>
                                    {
                                      this.editOperatorLR.businessObjectList.map((obj: any) => (
                                        <li on-click={() => this.operatorLRSelect(obj)}>{obj.label}({obj.value})</li>
                                      ))
                                    }
                                  </ul>
                                  <h4 class={styles.h4}>{this.$t('components.comp-expression-designer.custom')}:</h4>
                                  <div>
                                    <a-input-group size='small'>
                                      <a-input
                                        class={styles.customInput}
                                        v-model={this.editOperatorLR.customInput}
                                        placeholder={this.$t('controls.input')}
                                      >
                                        <template slot='addonBefore'>
                                          <a-select v-model={this.editOperatorLR.customType} size='small'>
                                            <a-select-option value='text'>
                                              {this.$t('components.comp-expression-designer.text')}
                                            </a-select-option>
                                            <a-select-option value='number'>
                                              {this.$t('components.comp-expression-designer.number')}
                                            </a-select-option>
                                          </a-select>
                                        </template>
                                        <template slot='addonAfter'>
                                          <a-button type='primary' size='small' on-click={() => this.operatorLRCudtomClick()}>{this.$t('buttons.ok')}</a-button>
                                        </template>
                                      </a-input>
                                    </a-input-group>
                                  </div>
                                </div>
                              </div>
                            </a-popover>
                            :
                            null
                        }
                        {/* 符号 */}
                        {
                          item.operator ?
                            <a-popover
                              class={styles.operator}
                              trigger={'click'}
                              placement={'bottomLeft'}
                              on-click={() => this.operatorClick(i)}
                            >
                              {item.operator.label}
                              {/*  符号弹出 */}
                              <template slot='content'>
                                <div class={styles.operatorTemp}>
                                  <ul class={styles.ul}>
                                    {
                                      this.dataSource.operatorList ?
                                        this.dataSource.operatorList.map(option => (
                                          <li on-click={() => this.operatorSelect(option)}>
                                            {option.label}
                                          </li>
                                        ))
                                        :
                                        null
                                    }
                                  </ul>
                                </div>
                              </template>
                            </a-popover>
                            :
                            null
                        }
                        {/* 符号右边 */}
                        {
                          item.operatorR ?
                            <a-popover
                              class={styles.operatorR}
                              trigger={'click'}
                              placement={'bottomLeft'}
                              on-click={() => this.operatorLRClick('R', i)}
                            >
                              {item.operatorR.label}
                              {/* 符号左右边弹出 */}
                              <div slot='content'>
                                <div class={styles.operatorLR}>
                                  <div>
                                    <a-input-group size='small'>
                                      <a-icon slot='suffix' type='search' />
                                      <a-input
                                        v-model={this.editOperatorLR.search}
                                        placeholder={this.$l.getLocale('controls.serachText')}
                                        on-change={this.operatorLRSearchChange}
                                      ></a-input>
                                    </a-input-group>
                                  </div>
                                  <h4 class={styles.h4}>{this.$t('components.comp-expression-designer.businessObjects')}:</h4>
                                  <ul class={styles.ul}>
                                    {
                                      this.editOperatorLR.businessObjectList.map((obj: any) => (
                                        <li on-click={() => this.operatorLRSelect(obj)}>{obj.label}({obj.value})</li>
                                      ))
                                    }
                                  </ul>
                                  <h4 class={styles.h4}>{this.$t('components.comp-expression-designer.custom')}:</h4>
                                  <div>
                                    <a-input-group size='small'>
                                      <a-input
                                        class={styles.customInput}
                                        v-model={this.editOperatorLR.customInput}
                                        placeholder={this.$t('controls.input')}
                                      >
                                        <template slot='addonBefore'>
                                          <a-select v-model={this.editOperatorLR.customType} size='small'>
                                            <a-select-option value='text'>
                                              {this.$t('components.comp-expression-designer.text')}
                                            </a-select-option>
                                            <a-select-option value='number'>
                                              {this.$t('components.comp-expression-designer.number')}
                                            </a-select-option>
                                          </a-select>
                                        </template>
                                        <template slot='addonAfter'>
                                          <a-button type='primary' size='small' on-click={() => this.operatorLRCudtomClick()}>{this.$t('buttons.ok')}</a-button>
                                        </template>
                                      </a-input>
                                    </a-input-group>
                                  </div>
                                </div>
                              </div>
                            </a-popover>
                            :
                            null
                        }
                        {/* 逻辑符号 */}
                        {
                          item.logicalOperator ?
                            <a-popover
                              class={styles.logicalOperator}
                              trigger={'click'}
                              placement={'bottomLeft'}
                              on-click={() => this.logicalOperatorClick(i)}
                            >
                              {item.logicalOperator.label}
                              {/* 逻辑符号弹出 */}
                              <template slot='content'>
                                <div class={styles.logicalOperatorTemp}>
                                  <ul class={styles.ul}>
                                    {
                                      this.dataSource.logicalOperatorList ?
                                        this.dataSource.logicalOperatorList.map(option => (
                                          <li on-click={() => this.logicalOperatorSelect(option)}>
                                            {option.label}
                                          </li>
                                        ))
                                        :
                                        null
                                    }
                                  </ul>
                                </div>
                              </template>
                            </a-popover>
                            :
                            null
                        }
                        {/* 操作区 */}
                        <span class={styles.operation}>
                          <a-tag on-click={() => this.editAdd(i)}>+</a-tag>
                          <a-tag on-click={() => this.editAdd_Bracketed(i)}>(+)</a-tag>
                          <a-tag on-click={() => this.editDelete(i)}>-</a-tag>
                        </span>
                      </div>
                    ))
                }
              </div>
            </a-card>
          </a-tab-pane>
          <a-tab-pane key='2' tab={this.$t('components.comp-expression-designer.seniorEdit')}>
            <a-textarea
              placeholder={this.$t('components.comp-expression-designer.customEditPlaceholder')}
              v-model={this.expression}
              autoSize={{ minRows: 3 }}
            >

            </a-textarea>
          </a-tab-pane>
        </a-tabs>
        <div class={styles.explain} domPropsInnerHTML={this.$t('components.comp-expression-designer.explain')}></div>
      </div >
    );
  }
}
