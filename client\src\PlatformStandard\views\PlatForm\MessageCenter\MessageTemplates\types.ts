export interface MessageTemplateQueryDto {
  templateCode?: string;
  templateName?: string;
  templateType?: number;
  status?: number;
  appCode?: string;
}

export interface MessageTemplateDto {
  templateId?:      string;
  templateCode?:    string;
  templateName?:    string;
  templateContent?: string;
  templateType?:    number;
  status?:          number;
  templateSubject?: string;
  applicationId?:   string;
  applicationName?:  string;
}

export interface TemplateStatusChange {
  templateId?:      string;
  status?:          number;
}
