/***************************************
 * 类型声明
 * 模块内部的 class、interface、enum 等
 ***************************************/

export interface Attribute {
  id?: string;
  label?: string;
  value?: string;
  type?: string;
  defaultValue?: string;
  children?: Attribute[];
}

// 符号明细
export interface OperatorItem {
  label: string;
  value: string;
  expression: string;
}

export interface EditItem {
  brackets?: string;
  operatorL?: Attribute;
  operator?: OperatorItem;
  operatorR?: Attribute;
  tabLength: number;
  logicalOperator?: OperatorItem;
  popoverVisibleL?: boolean;
  popoverVisibleR?: boolean;
  popoverVisibleO?: boolean;
  popoverVisibleLO?: boolean;
}
