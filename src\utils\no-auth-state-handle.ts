import Koa from 'koa';
import querystring from 'querystring';
import { environment } from '../environment';
import { getSsoConfig } from './sso-config';

export function noStateHandle(ctx: Koa.Context): boolean {
  debugger; // 调试 noStateHandle - 测试用
  console.log('noStateHandle called for:', ctx.path, 'session:', !!ctx.session, 'user:', !!(ctx.session && ctx.session.user));
  if (!ctx.session || !ctx.session.user) {
    const refererUrl = ctx.header['referer'] && ctx.header['referer'] !== '/' ? new URL(ctx.header['referer'] + '') : undefined;
    const params = refererUrl ? { referer: refererUrl.pathname + refererUrl.search } : {};
    const ssoConfig = getSsoConfig(ctx);
    let ssoUrl = '';
    if (refererUrl && ssoConfig) {
      ssoUrl = `${ssoConfig.redirectUri}${ssoConfig.loginUri}` + encodeURI(`?callBackUrl=${ssoConfig.callbackUri}`);
    }
    const uri = `${ssoConfig ? ssoUrl : '/login'}?${querystring.stringify(params)}`;

    if (ssoConfig) {
      ctx.status = (ctx.path === `/${environment.urlKey}/auth/state` || ctx.path === '/auth/state') ? 308 : 440;
      ctx.body = uri;
    } else {
      ctx.status = (ctx.path === `/${environment.urlKey}/auth/state` || ctx.path === '/auth/state') ? 401 : 440;
      ctx.body = uri;
    }

    return true;
  }

  return false;
}
