import { Settings } from '@/PlatformStandard/common/defines';
import VueI18n from 'vue-i18n';

class I18nService {
  private i18n!: VueI18n;

  init(instance: VueI18n) {
    this.i18n = instance;
    const lang = localStorage.getItem(Settings.UserLanguageCacheKey);
    if (!!lang) {
      this.i18n.locale = lang;
    }
  }

  use(lang: string) {
    this.i18n.locale = lang;
  }
}

export const i18nService = new I18nService();
