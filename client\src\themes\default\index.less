@import '../../../node_modules/ant-design-vue/dist/antd.less';
@import './variables.less';
@import './color.less';
@import './spacing.less';
@import './heading.less';
@import './text.less';
@import './form.reset.less';

/* 重写zorro组件样式 */
/* 无法通过变量修改的样式复写 */
.ant-pagination .ant-pagination-item-active a {
  color: @white-color;
}

.ant-card .ant-card-head-wrapper {
  position: relative;
}

.ant-modal .ant-modal-header {
  padding: @base-size * 1.5 @base-size * 2;
  background: @primary-color;
}

.ant-modal .ant-modal-content .ant-modal-close-x {
  width: @card-head-height;
  height: @card-head-height;
  line-height: @card-head-height;
  color: @white-color;
}
