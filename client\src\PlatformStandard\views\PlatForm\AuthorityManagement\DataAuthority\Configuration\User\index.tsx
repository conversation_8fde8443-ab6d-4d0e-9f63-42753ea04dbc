import { Component, Vue } from 'vue-property-decorator';
import styles from './user.module.less';
import { CompCard, CompTableHeader, SelectUser } from '@/PlatformStandard/components';
import { VueModule } from '@/PlatformStandard/common/defines';
import { UserDto } from './types';
import { userService } from './service';
import { i18nHelper, notificationHelper, removeNullValueProperty } from '@/PlatformStandard/common/utils';
import { UserItemDto } from '@/PlatformStandard/components/OrganizationUser/types';
@Component({
    components: { CompCard, CompTableHeader, SelectUser }
})

export class User extends VueModule {
    pageActions: string[] = [];
    private visible = false;
    private userData: UserItemDto[] = [];
    private authorityId = '';
    private account = '';
    private username = '';
    private list: UserDto[] = [];
    private fieldsSlotMap: any = {};
    private columns = [
        { key: 'account', dataIndex: 'account', width: '20%', slots: { title: 'account' } },
        { key: 'userName', dataIndex: 'userName', width: '20%', slots: { title: 'userName' } },
        { key: 'orgPath', dataIndex: 'orgPath', width: '50%', slots: { title: 'orgPath' } },
        { dataIndex: 'operation', scopedSlots: { customRender: 'operation' }, slots: { title: 'operation' } },
    ];
    private pagination = {
        total: 0,
        current: 1,
        pageSize: 10,
        showSizeChanger: true,
        size: 'small',
        showTotal: (total: string) => i18nHelper.getReplaceLocale(`${this.paginationsI18n}.total`, total),
    };
    // 重载数据
    private load(pageIndex?: number): void {
        this.authorityId = this.$route.query['id'] as string;
        if (pageIndex) {
            this.pagination.current = pageIndex;
        }
        const params: any = {
            'page-size': this.pagination.pageSize,
            'page-index': this.pagination.current,
            'authorityId': this.authorityId,
            'account': this.account,
            'username': this.username
        };
        userService.getUsers(removeNullValueProperty(params)).subscribe(data => {
            this.pagination.total = data.total;
            this.list = data.items;
        });
    }
    private add() {
        this.visible = true;
    }
    // 查询
    private onSearch(): void {
        this.load(1);
    }
    // 重置
    private onReset(): void {
        this.account = '';
        this.username = '';
        this.load(1);
    }

    private onTableChange(pagination: any, filters: any, sorter: any): void {
        this.pagination = pagination;
        this.load();
    }

    private deleteUser(row: UserDto) {
        userService.deleteUser(String(row.relationId)).subscribe(() => {
            this.load(1);
            notificationHelper.success(i18nHelper.getLocale('messages.success'));
        });
    }

    private onClosepop(result: boolean, resultList: any, iscancel: boolean) {
        if (iscancel) {
            this.visible = false;
            return;
        }
        this.userData = resultList;
        if ((this.userData || []).length === 0) {
            this.$message.error(`${i18nHelper.getLocale('controls.select')}${i18nHelper.getLocale('platform.fields.user')}`);
            return;
        }
        this.visible = false;
        const users: UserDto[] = this.userData.map(m => ({
            dataAuthorityId: this.authorityId,
            userId: m.value,
            userName: m.label,
            account: m.account,
            orgPath: m.organizationNamePath
        }));

        const submitData = {
            dataAuthorityId: this.authorityId,
            dataAuthorityUsers: users
        };
        userService.addUser(submitData)
            .subscribe(() => {
                this.load(1);
                notificationHelper.success(i18nHelper.getLocale('messages.success'));
            });
    }
    created() {
        this.fieldsSlotMap['operation'] = (text: UserDto[], record: UserDto, index: number) => {
            return (
                <div>
                    <span class='mr-1'>
                        <a-popconfirm title={this.$t('messages.delete')} on-confirm={() => this.deleteUser(record)}>
                            <a-button type='link'>
                                {this.$l.getLocale('buttons.delete')}
                            </a-button>
                        </a-popconfirm>
                    </span>
                </div>
            );
        };
        this.load(1);
    }
    render() {
        return (
            <div>
                <comp-card class={styles.card_top}>
                    <comp-table-header class={styles.table_header}
                        on-search={() => {
                            this.onSearch();
                        }}
                        on-reset={() => {
                            this.onReset();
                        }}
                    >
                        <template slot='base'>
                            <a-row >
                                <a-col span='6' class='mr-1'>
                                    <a-form-item label={this.$t('bpm.data-authority.account')}>
                                        <a-input v-model={this.account}></a-input>
                                    </a-form-item>
                                </a-col>
                                <a-col span='6'>
                                    <a-form-item label={this.$t('bpm.data-authority.username')}>
                                        <a-input v-model={this.username}></a-input>
                                    </a-form-item>
                                </a-col>
                            </a-row>
                        </template>
                    </comp-table-header>
                </comp-card>
                <comp-card>
                    <div slot='extra'>
                        <a-row>
                            <a-col span='24' style='text-align:right;padding-bottom:10px'>
                                <a-button type='primary' class={styles.common_btn} on-click={this.add}>
                                    {this.$t('buttons.add')}
                                </a-button>
                            </a-col>
                        </a-row>
                    </div>
                    <a-table
                        size='small'
                        rowKey='relationId'
                        columns={this.columns}
                        dataSource={this.list}
                        pagination={this.pagination}
                        scopedSlots={this.fieldsSlotMap}
                        on-change={this.onTableChange}
                    >
                        <span slot='account'>{this.$t('bpm.data-authority.account')}</span>
                        <span slot='userName'>{this.$t('bpm.data-authority.username')}</span>
                        <span slot='orgPath'>{this.$t('bpm.data-authority.orgpath')}</span>
                        <span slot='operation'>{this.$t('platform.fields.operation')}</span>
                    </a-table>
                </comp-card>
                <select-user visible={this.visible} valueNew={this.userData}
                    multiple={true}
                    uStatus={1}
                    wStatus={1}
                    on-closepop={(result: boolean, resultList: any, iscancel: boolean) => this.onClosepop(result, resultList, iscancel)}
                ></select-user>
            </div>
        );
    }
}
