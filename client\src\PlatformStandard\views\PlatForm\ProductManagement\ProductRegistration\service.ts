import { httpHelper } from '@/PlatformStandard/common/utils';
import { Observable } from 'rxjs';

class ProductRegistrationService {
  getLicense(): Observable<any> {
    const url = '/api/platform/v1/product-license';
    return httpHelper.get(url);
  }

  registeProduct(data: FormData): Observable<any> {
    const url = '/api/platform/v1/registered-product';
    return httpHelper.post(url, data);
  }
}
export const productRegistrationService = new ProductRegistrationService();
