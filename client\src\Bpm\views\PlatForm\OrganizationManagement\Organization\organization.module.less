@import '../../../../../themes/default/variables.less';

.page {
  display: flex;
  .tree_list {
    // margin: @card-padding-base 0px @card-padding-base @card-padding-base;
    width: 330px;
    height: 510px;
    .tree_card {
      width: 100%;
      display: flex !important;
      flex-direction: column;
      height: 100%;
      :global(.ant-card-body) {
        height: calc(~'100%' - 51px);
      }
    }
  }
  .content_info {
     width: 100%;
    .edit_info {
      width: 100%;
      // width: calc(100% - 200px);
      // margin-left: @card-padding-base;
      padding-left: @card-padding-base;
      .info_card {
        display: flex !important;
        width: 100%;
        flex-direction: column;
        height: auto;
        .buttons {
          height: @btn-height-sm;
        }
      }
    }
    .edit_info2 {
      width: 100%;
      margin-top: @base-size;
      // margin-left: @card-padding-base;
      padding-left: @card-padding-base;
      .info_card {
        display: flex !important;
        width: 100%;
        flex-direction: column;
        height: auto;
        .buttons {
          height: @btn-height-sm;
        }
        .row_outer{
          background-color: @content-bg-1;
          margin: @base-size 0px !important;
        }
        .row{
          // padding: @content-padding-size;
        }
        .operation{
          text-align: right;
          cursor: pointer;
        }
      }     
    }
    .list_button {
      padding: 0 3px !important
    } 
  }
  .labelStyle{
    color: #333;
  }
  .labelStyle font{
    color: #666;
  }
}
.marginRightWidth{
  padding-right: 8px !important;
  :global(.ant-form-item-label) {
    line-height: 20px !important;
  }
}
.common_btn {
  padding:0;
  margin: 0  0 0 10px;
  width: 70px !important;
  height: 30px !important;
  text-align: center;
  line-height: 32px;
  border-radius: 5px !important;
  border: 1px solid #ddd !important;
  background-color: #fff !important;
  color: #333 !important;
  opacity: 1;
  text-shadow: none !important;
  box-shadow: none !important;
}
.searchBtn{
  // margin-right: 10px;
  width: 72px;
  height: 32px;
  opacity: 1;
  background: @btn-color;
  text-align: center;
  line-height: 32px;
  border-radius: 5px !important;
}
.noResultTips{
  display: block;
  margin:0 auto;
}
.textlength{
  overflow: hidden;
  text-overflow:ellipsis;
  white-space:nowrap;
  width:80px;
}
.labelIcon{
  width:40px;
  height: 41px;
  background: url(../../../../../assets/images/icon_com.png) no-repeat;
  background-size: 100% 100%;
  float: left;
  margin-left: 40px;
}