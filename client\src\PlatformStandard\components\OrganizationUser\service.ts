import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { httpHelper, guidHelper, toCasedStyleObject } from '@/PlatformStandard/common/utils';
import { TreeItem, OrganizationTreeSelectDto } from './types';

class OrganizationUserService {
  getOrganizationsTree(id?: string, value?: string): Observable<TreeItem[]> {
    const _url = `/api/platform/v1/manage/tree-organizations`;
    return httpHelper.get(_url, { params: toCasedStyleObject({ parentId: value }) }, { loading: false }).pipe(
      map(data =>
        data.map((item: OrganizationTreeSelectDto) => ({
          id: guidHelper.generate(),
          pId: id,
          value: String(item.value).toLowerCase(),
          title: item.label,
          isLeaf: item.childCount > 0 ? false : true,
          scopedSlots: { icon: 'icon' },
          datum: item,
        }))
      )
    );
  }

  searchOrganizations(organizationId: string, id?: string): Observable<any[]> {
    const _url = `/api/platform/v1/manage/search-organizations/${organizationId}`;
    return httpHelper.get(_url).pipe(
      map(data =>
        data.map((item: OrganizationTreeSelectDto) => ({
          id: guidHelper.generate(),
          pId: id,
          value: String(item.value).toLowerCase(),
          title: item.label,
          isLeaf: item.childCount > 0 ? false : true,
          scopedSlots: { icon: 'icon' },
          datum: item,
        }))
      )
    );
  }

  getUsersByOrganization(id: string) {
    const _url = `/api/platform/v1/organizations/${id}/select-users`;
    return httpHelper.get(_url, undefined, { loading: false }).pipe(
      map(data =>
        data.map((item: any) => ({
          id: guidHelper.generate(),
          pId: id,
          value: String(item.value).toLowerCase(),
          title: `${item.label}(${item.account},${item.fullDivision})`,
          isLeaf: true,
          scopedSlots: { icon: 'icon' },
          datum: item,
        }))
      )
    );
  }

  searchUsers(kw: string) {
    const _url = `/api/platform/v1/select-users`;
    return httpHelper.get(_url, { params: { keyword: kw, count: '20' } }, { loading: false }).pipe(
      map(data =>
        data.map((item: any) => ({
          value: String(item.value).toLowerCase(),
          title: `${item.label}(${item.account},${item.fullDivision})`,
          isLeaf: true,
          scopedSlots: { icon: 'icon' },
          datum: item,
        }))
      )
    );
  }
}

export const organizationUserService = new OrganizationUserService();
