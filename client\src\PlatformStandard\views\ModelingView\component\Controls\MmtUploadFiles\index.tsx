import { Component, Prop, Vue } from 'vue-property-decorator';
import { BaseControl } from '../base-control';
import { guidHelper } from '../../../common/utils';
import { modelingViewService } from '../../../service';

@Component({
})
export class MmtUploadFiles extends BaseControl {
    private onUploadChange(info: any) {
        if (info.file.status === 'done') {
            const copyItems = JSON.parse(JSON.stringify(this.value));
            const itemIndex = copyItems.findIndex((f: any) => f.uid === info.file.uid);
            copyItems.splice(itemIndex, 1);
            this.onChange([...copyItems, {
                uid: guidHelper.generate(),
                name: info.file.name,
                status: 'done',
                url: info.file.response.fileUrl
            }]);
        } else if (info.file.status === 'removed' || info.file.status === 'error') {
            const copyItems = JSON.parse(JSON.stringify(this.value));
            const itemIndex = copyItems.findIndex((f: any) => f.uid === info.file.uid);
            copyItems.splice(itemIndex, 1);
            this.onChange(copyItems);
        } else if (info.file.status === 'uploading') {
            const copyItems = JSON.parse(JSON.stringify(this.value));
            const itemIndex = copyItems.findIndex((f: any) => f.uid === info.file.uid);
            if (itemIndex === -1) {
                this.onChange([...copyItems, {
                    uid: info.file.uid,
                    name: info.file.name,
                    status: 'uploading',
                    url: ''
                }]);
            }
        }
    }
    created(): void {
    }
    render() {
        return (
            this.pageModel === 'view' ? (
                <a-upload
                    fileList={this.value}
                    disabled={true}
                >
                </a-upload>
            ) : (
                <a-upload
                    action={modelingViewService.uploadFilesUrl}
                    fileList={this.value}
                    disabled={!this.controlConfig.config.disabled}
                    multiple
                    on-change={this.onUploadChange}
                >
                    <a-button>
                        <a-icon type='upload' />上传
                    </a-button>
                </a-upload>
            ));
    }
}
