export interface OrganizationTree {
  code: string;
  value: string;
  label: string;
  childCount: number;
}

export interface OrganizationDto {
  organizationId?: string;
  name?: string;
  organizationCode?: string;
  upperId?: string;
  upperName?: string;
  manager?: string;
  // managerName?: string;
  telephone?: string;
  level?: number;
  status?: number;
  remark?: string;
  sortCode?: number;
  fullPathCode?: string;
  fullPathText?: string;
  extendColumns: OrganizationExt;
  // fgfzName?: string;
  hasChild?: boolean;
}

export interface OrganizationExt {
  organizationId?: string;
  domainId?: string;
  domainName?: string;
  domainLevelId?: string;
  domainLevelCode?: string;
  weight?: number;
  domainLevelName?: string;
  cLevelId?: string;
  cLevelName?: string;
  fgfzId?: string;
  fgfzName?: string;
  managerName?: string;
  fgfzIds?: string;
  fgfzNames?: string;
  managerIds?: string;
  managerNames?: string;
}

export interface UserPositionQueryDto {
  organizationId?: string;
  orgInfo?: string;
  positionInfo?: string;
  userInfo?: string;
}

export interface UserPositionDto {
  organizationId?: string;
  organizationCode?: string;
  organizationName?: string;
  positionId?: string;
  positionCode?: string;
  positionName?: string;
  positionType?: number;
  primaryPosition?: boolean;
  userId?: string;
  userName?: string;
}

export interface DomainLevelDto {
  id?: string;
  levelCode?: string;
  levelName?: string;
  weight?: number;
}
