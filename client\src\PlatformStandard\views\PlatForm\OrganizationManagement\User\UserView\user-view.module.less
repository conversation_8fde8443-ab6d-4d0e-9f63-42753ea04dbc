@import '../../../../../../themes/default/variables.less';

.tab {
    .ant-tabs .ant-tabs-left-bar .ant-tabs-tab {
        text-align: left;
    }
    .ant-tabs-tab-active {
        background-color: #c1dbf8;
    }      
}

.title {
    color: #333;
    font-size: 16px;
    font-weight: 700;
    background: #fff;
    padding: 8px 16px;
    margin: -16px -16px 16px;
}

.footer_toolbar {
    text-align: center;
    position: fixed;
    bottom: 0px;
    width: 100%;
    height: 50px;
    line-height: 50px;
    background-color: #eee;
    z-index: 9999;
}

.org_title {
    cursor: default;
    display: inline;
    padding-right: @base-size - 4px;
    font-weight: normal;
    &:extend(.parent_title);
  
    i {
      color: @text-color-desc;
      margin-left: 2px;
    }
}