stages:
  - lint
  - build
  - deploy
  - release

before_script:
  - if [[ -z $PROJECT_NAME ]]; then echo 'Missing Secret Variable -> $PROJECT_NAME' && exit 10; fi
  - if [[ -z $PROJECT_PORT ]]; then echo 'Missing Secret Variable -> $PROJECT_PORT' && exit 11; fi
  - if [[ -z $MODULENAME ]]; then echo 'Missing Secret Variable -> $MODULENAME' && exit 10; fi
  - git submodule sync
  - git submodule update --init
  - HARBOR=*************:8858/shuiwu
  - npm config set registry https://registry.npm.taobao.org 

koa_lint:
  stage: lint
  except:
    - tags
    - shuiwu-release
    - master
  script:
    - yarn add tslint typescript --dev --prefer-offline
    - yarn run lint

vue_lint:
  stage: lint
  except:
    - tags
    - shuiwu-release
    - master
  script:
    - cd client
    - yarn add @vue/cli-service --dev --prefer-offline
    - yarn run lint

koa_build:
  stage: build
  except:
    - tags
    - shuiwu-release
    - master
  script:
    - npm install --prefer-offline
    - npm run build

vue_build:
  stage: build
  except:
    - tags
    - shuiwu-release
    - master
  script:
    - cd client
    - npm install --prefer-offline
    - npm run build

dev_deploy:
  stage: deploy
  only:
    - shuiwu-release
  script:
    - cd client
    - npm install --prefer-offline
    - npm run build
    - cd ..
    - npm install --prefer-offline
    - npm run build
    - sed -i -e "s/4100/$PROJECT_PORT/g" dist/config.json
    - tar -cvzf /tmp/$PROJECT_NAME.tgz dist public package.json
    - deploy_run "rm -rf /var/www/shuiwu-project/$PROJECT_NAME/tmp"
    - deploy_run "mkdir /var/www/shuiwu-project/$PROJECT_NAME/tmp"
    - deploy_copy /tmp/$PROJECT_NAME.tgz /var/www/shuiwu-project/$PROJECT_NAME/tmp
    - deploy_run "cd /var/www/shuiwu-project/$PROJECT_NAME/tmp && tar -xvzf $PROJECT_NAME.tgz"
    - deploy_run "cd /var/www/shuiwu-project/$PROJECT_NAME/tmp && rm $PROJECT_NAME.tgz"
    - deploy_run "cd /var/www/shuiwu-project/$PROJECT_NAME/tmp && npm install --prefer-offline"
    - deploy_run "rm -rf /var/www/shuiwu-project/$PROJECT_NAME/src/dist"
    - deploy_run "rm -rf /var/www/shuiwu-project/$PROJECT_NAME/src/node_modules"
    - deploy_run "rm -rf /var/www/shuiwu-project/$PROJECT_NAME/src/public/$MODULENAME"
    - deploy_run "rsync -av /var/www/shuiwu-project/$PROJECT_NAME/tmp/ /var/www/shuiwu-project/$PROJECT_NAME/src/"
    - deploy_run "rm -rf /var/www/shuiwu-project/$PROJECT_NAME/tmp"

release_task:
  stage: release
  only:
    - tags
  script:
    # - cd client
    # - sed -i -e "s/EnableMovitechLogo:\ true/EnableMovitechLogo:\ false/g" src/PlatformStandard/common/defines/settings.ts
    # - sed -i -e "s/EnableOrganizationFilter:\ true/EnableOrganizationFilter:\ false/g" src/PlatformStandard/common/defines/settings.ts
    # - sed -i -e "s/EnableProcessProxy:\ true/EnableProcessProxy:\ false/g" src/PlatformStandard/common/defines/settings.ts
    # - npm install --prefer-offline
    # - npm run build
    # - cd ..
    # - npm install --prefer-offline
    # - npm run build
    # - sed -i -e "s/4100/$PROJECT_PORT/g" dist/config.json
    # - if [[ ! -d /tmp/releases/$PROJECT_NAME ]]; then mkdir -p /tmp/releases/$PROJECT_NAME; fi
    # - cp -r dist public package.json /tmp/releases/$PROJECT_NAME
    # - echo $CI_COMMIT_TAG > /tmp/releases/$PROJECT_NAME/VERSION
    # - tar -cvzf /tmp/releases/$PROJECT_NAME.tgz -C /tmp/releases $PROJECT_NAME
    # - deploy_run "rm -rf /tmp/releases/$PROJECT_NAME"
    # - deploy_run "if [[ ! -d /tmp/releases ]]; then mkdir -p /tmp/releases; fi"
    # - deploy_copy /tmp/releases/$PROJECT_NAME.tgz /tmp/releases
    - deploy_run "cd /var/www/shuiwu-project/$PROJECT_NAME/src && echo 'FROM node:12\n\nWORKDIR /app\n\nCOPY package*.json ./\n\nCOPY . .\n\nEXPOSE 8806\nCMD [ \"node\", \"dist/app.js\" ]'>Dockerfile && docker build -t $PROJECT_NAME:$CI_COMMIT_TAG  ."
    - deploy_run "docker tag $PROJECT_NAME:$CI_COMMIT_TAG  $HARBOR/$PROJECT_NAME:$CI_COMMIT_TAG"
    - deploy_run "docker push $HARBOR/$PROJECT_NAME:$CI_COMMIT_TAG"
    - deploy_run "docker rmi $HARBOR/$PROJECT_NAME:$CI_COMMIT_TAG && docker rmi $PROJECT_NAME:$CI_COMMIT_TAG"
