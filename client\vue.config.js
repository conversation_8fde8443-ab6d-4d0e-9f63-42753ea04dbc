const CompressionWebpackPlugin = require('compression-webpack-plugin');
module.exports = {
  outputDir: '../public/platform',
  publicPath: 'platform',
  runtimeCompiler: true,

  devServer: {
    disableHostCheck: true,
    proxy: {
      '/platform/api/': {
        target: 'http://localhost:4100',
        changeOrigin: true,
      },
      '/platform/auth/': {
        target: 'http://localhost:4100',
        changeOrigin: true,
      },
      '/platform/version/': {
        target: 'http://localhost:4100',
        changeOrigin: true,
      },
      '/platform/static-form/': {
        target: 'http://localhost:4100',
        changeOrigin: true,
      },
      '/platform/socket.io/': {
        target: 'http://localhost:4100',
        changeOrigin: true,
        ws: true,
      },
    },
  },
  configureWebpack: {
    plugins: [
      new CompressionWebpackPlugin({
        filename: '[path].gz[query]',
        algorithm: 'gzip',
        test: /\.(js|css)(\?.*)?$/i,
        threshold: 10240, // 对超过10k的数据进行压缩
        minRatio: 0.8, // 只有压缩率小于这个值的资源才会被处理
        deleteOriginalAssets: false, // 删除原文件
        cache: false,
      }),
    ],
  },
  pluginOptions: {
    i18n: {
      locale: 'zh',
      fallbackLocale: 'zh',
      localeDir: 'locales',
      enableInSFC: false,
    },
  },
};
