import Koa from 'koa';
import bodyParser from 'koa-body';
import { environment, i18Languages } from '../environment';
import { getSsoConfig } from '../utils/sso-config';
import { removeUserState, setUserState } from '../utils/user-state';
import agent from 'superagent';

export async function LoginAsync(ctx: Koa.ParameterizedContext, next: Koa.Next) { }

export async function LogoutAsync(ctx: Koa.ParameterizedContext, next: Koa.Next) {
  removeUserState(ctx);
  ctx.status = 204;
}

export async function GetUserStateAsync(ctx: Koa.ParameterizedContext, next: Koa.Next) {
  debugger; // 强制断点 - 测试用
  if (ctx.session && ctx.session.user) {
    const ssoConfig = getSsoConfig(ctx);
    const logoutUri = ssoConfig ?
      `${ssoConfig.redirectUri}${ssoConfig.logoutUri}` + encodeURI(`?callBackUrl=${ssoConfig.callbackUri}`) : '/logout';
    const adminUri = environment.adminUri;

    await agent
      .get(`${environment.apiGateway.uri}/platform/v1/platform/menus`)
      .set(ctx.header)
      .then((res: agent.Response) => {
        ctx.status = 200;
        ctx.body = { ...ctx.session.user, menus: res.body, logoutUri, adminUri };
      })
      .catch((err: any) => {
        if (err.status === 404) {
          ctx.status = 200;
          ctx.body = { ...ctx.session.user, menus: [], logoutUri, adminUri };
        } else {
          const message = err.response ? err.response.text : '';
          ctx.throw(err.status, message);
        }
      });
  } else {
    ctx.throw(404, 'No Authentication!');
  }
}

export async function ModifyUserLanguage(ctx: Koa.ParameterizedContext, next: Koa.Next) {
  await bodyParser().call(null, ctx, next);

  if (ctx.session && ctx.session.user) {
    ctx.session.user.language = ctx.request.body.lang;
  }
  ctx.status = 204;
}

export async function Geti18Languages(ctx: Koa.ParameterizedContext, next: Koa.Next) {
  await i18Languages().then(res => {
    ctx.status = 200;
    ctx.body = res;
  });
}
