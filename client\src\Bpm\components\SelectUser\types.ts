export interface OrganizationTree {
  code: string;
  value: string;
  label: string;
  childCount: number;
}
export interface OrganizationDto {
  organizationId?: string;
  name?: string;
  organizationCode?: string;
  upperId?: string;
  parentName?: string;
  manager?: string;
  managerName?: string;
  telephone?: string;
  level?: number;
  status?: number;
  remark?: string;
  sortCode?: number;
  tagId?: string;
  tagName?: string;
}
