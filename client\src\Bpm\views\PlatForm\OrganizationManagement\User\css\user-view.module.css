/*
变量参考 https://github.com/vueComponent/ant-design-vue/blob/master/components/style/themes/default.less
或 https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/components/style/themes/default.less
*/
/* 重写 AntDesign 的主题样式 */
/* 重写 AntDesign 结束 */
/* 自定义相关 */
/* 基础边距 */
.tab .ant-tabs .ant-tabs-left-bar .ant-tabs-tab {
  text-align: left;
}
.tab .ant-tabs-tab-active {
  background-color: #c1dbf8;
}
.title {
  color: #333;
  font-size: 16px;
  font-weight: 700;
  background: #fff;
  padding: 8px 16px;
  margin: -16px -16px 16px;
}
.footer_toolbar {
  text-align: center;
  position: fixed;
  bottom: 0px;
  width: 100%;
  height: 50px;
  line-height: 50px;
  background-color: #eee;
  z-index: 9999;
}
.org_title {
  cursor: default;
  display: inline;
  padding-right: 4px;
  font-weight: normal;
}
.org_title i {
  color: #bfbfbf;
  margin-left: 2px;
}
