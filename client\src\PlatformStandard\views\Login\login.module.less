@import '../../../themes/default/variables.less';

@cover-width: 28%;

@media (max-width: 1366px) {
  .banner_size {
    width: 630px;
    height: 296px;
    margin-top: -148px !important;
  }

  .form_size {
    width: 380px;
    height: 390px;
    margin-right: -190px !important;
    margin-top: -195px !important;
  }
}

@media (min-width: 1367px) and(max-width: 1680px) {
  .banner_size {
    width: 830px;
    height: 391px;
    margin-top: -195.5px !important;
  }

  .form_size {
    width: 440px;
    height: 450px;
    margin-right: -220px !important;
    margin-top: -225px !important;
  }
}

@media (min-width: 1681px) {
  .banner_size {
    width: 849px;
    height: 400px;
    margin-top: -200px !important;
  }

  .form_size {
    width: 410px;
    height: 464px;
    margin-right: -205px !important;
    margin-top: -232px !important;
  }
}

.container {
  display: flex;
  width: 100vw;
  height: 100vh;
  min-width: 1366px;
  background-image: url(../../../assets/images/content_bg.png);
}

.right_cover {
  position: relative;
  background: url(../../../assets/images/right_bg.png) no-repeat 0px 0px;
  background-size: 100%;
  width: @cover-width;
  height: 100%;

  .mask {
    position: absolute;
    background-color: @primary-color;
    opacity: 0.8;
    width: 100%;
    height: 100%;
  }
}

.main_view {
  position: relative;
  width: 100% - @cover-width;
  height: 100%;

  .form {
    &:extend(.form_size);
    position: absolute;
    top: 50%;
    right: 0;
    background-color: @white-color;
    border-radius: @border-radius-base;
    padding: 50px !important;
    box-shadow: 0 10px 20px 0px rgba(0, 0, 0, 0.05);
    z-index: @zindex-float;

    .title label {
      line-height: 1.5;
      text-align: left;
      font-weight: @font-weight-bold;
      font-size: @font-size-lg + 4;
      border-left: 7px solid @primary-color;
      padding-left: 7px;
      color: @heading-color;
    }

    .sub_title {
      margin-top: 30px;
      margin-bottom: @base-size * 2;

      label {
        font-size: @font-size-lg;
        line-height: 1.5;
        font-weight: @font-weight-bold;
        color: @heading-color;
      }
    }

    :global(.ant-input-prefix) {
      user-select: none;

      .prefix {
        color: @text-color-secondary;
        display: flex;
        align-items: center;

        .prompt {
          display: inline-block;
          width: 42px;
          text-align: justify;
          margin-left: 6px;
          height: 14px;
          line-height: 14px;
          padding-top: 1px;

          &::after {
            content: ' ';
            display: inline-block;
            width: 100%;
          }
        }
      }
    }

    input {
      padding-left: @font-size-base * 5 + @base-size !important;
      height: 36px;
    }

    .login_button {
      width: 100%;
      height: 40px;
      line-height: 40px;
    }
  }

  .banner_container {
    &:extend(.banner_size);
    position: absolute;
    top: 50%;
    left: 20.16%; // 72% * 28%

    .banner {
      position: absolute;
      width: 100%;
      height: 100%;
    }

    .logo {
      position: absolute;
      width: 140px;
      top: -40px;
    }

    .copyright {
      position: absolute;
      margin: 0;
      bottom: -30px;
    }
  }
}
