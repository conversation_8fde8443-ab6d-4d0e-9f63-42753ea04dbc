import { Observable } from 'rxjs';
import { httpHelper } from '@/PlatformStandard/common/utils';
class EnumService {
  /**
   * 获取枚举列表
   * @param name 枚举名称
   */
  getEnumItem(name: string | string[]): Observable<any> {
    const path = '/api/platform/v1/commons/enum-items' ;
    if (name instanceof Array) {
      name = name.join('&name=');
    }
    return httpHelper.get(`${path}?name=${name}`);
  }

  /**
   * 获取枚举列表
   * @param name 枚举名称
   */
  getProcessEnumItem(name: string | string[]): Observable<any> {
    const path = '/api/process/v1/commons/enum-items';
    if (name instanceof Array) {
      name = name.join('&name=');
    }
    return httpHelper.get(`${path}?name=${name}`);
  }
}

export const enumService = new EnumService();
