/*
变量参考 https://github.com/vueComponent/ant-design-vue/blob/master/components/style/themes/default.less
或 https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/components/style/themes/default.less
*/
/* 重写 AntDesign 的主题样式 */
/* 重写 AntDesign 结束 */
/* 自定义相关 */
/* 基础边距 */
.card_top {
  margin-bottom: 8px !important;
  padding: 0px 0px 0 0px !important;
}
.card_top :global(.ant-card-body) {
  padding: 0px 10px 0 10px !important;
}
.card_top :global(.ant-card-body) :global(.ant-layout) {
  padding-bottom: 0 !important;
}
.card_top :global(.ant-form-item-label) {
  line-height: 20px !important;
}
.button_top {
  text-align: right;
  float: right;
  padding-top: 22px;
}
.page {
  display: flex;
}
.page .tree_list {
  width: 330px;
  height: 510px;
}
.page .tree_list .tree_card {
  width: 100%;
  display: flex !important;
  flex-direction: column;
  height: 100%;
}
.page .tree_list .tree_card :global(.ant-card-body) {
  height: 90% !important;
}
.page .content_info {
  width: 100%;
}
.page .content_info .edit_info {
  width: 100%;
  padding-left: 8px;
}
.page .content_info .edit_info .info_card {
  display: flex !important;
  width: 100%;
  flex-direction: column;
  height: auto;
}
.page .content_info .edit_info .info_card .buttons {
  height: 22px;
}
.common_btn {
  padding: 0;
  margin: 0  0 0 10px;
  width: 70px !important;
  height: 30px !important;
  text-align: center;
  line-height: 32px;
  border-radius: 5px !important;
  border: 1px solid #ddd !important;
  background-color: #fff !important;
  color: #333 !important;
  opacity: 1;
  text-shadow: none !important;
  box-shadow: none !important;
}
