import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import { SearchList } from './SearchList';
import { FormList } from './FormList';
import { PageModelingDto } from './types';
import { modelingViewService } from './service';

@Component({
    components: { SearchList, FormList }
})
export class PageModelingView extends Vue {
    @Prop() id!: string;
    private pageDesgin: PageModelingDto = {};
    created(): void {
        const paths = this.$route.path.split('/');
        const routerId = paths.length > 0 ? paths[paths.length - 1] : '';
        if (routerId || this.id) {
            modelingViewService.getPageModeling(routerId ? routerId : this.id).subscribe(data => {
                this.pageDesgin = data;
            });
        }
    }
    render() {
        return (
            <div>
                {
                    this.pageDesgin.type ? ([
                        this.pageDesgin.type === 1 ? (
                            <search-list pageDesginJson={this.pageDesgin.pageDesginJson} />
                        ) : null,
                        this.pageDesgin.type === 2 ? (
                            <form-list pageDesginJson={this.pageDesgin.pageDesginJson} />
                        ) : null
                    ]) : null
                }
            </div >
        );
    }
}
