import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import { MenuEntity } from '../types';
import { ValueLabelPair } from '@/PlatformStandard/common/defines';
import { i18nHelper } from '@/PlatformStandard/common/utils';
import { WrappedFormUtils } from 'ant-design-vue/types/form/form';
import { ActionEnum, MenuTypeEnum } from '@/PlatformStandard/services/menu';

@Component
export class MenuEdit extends Vue {
  @Prop() selectMenu!: MenuEntity;
  private currentMenu: MenuEntity = {};
  private options: ValueLabelPair[] = [];
  private form!: WrappedFormUtils;
  private checked = false;
  private actions: string[] = [];

  getMenuInfo(): MenuEntity {
    this.currentMenu.actionIds = this.actions;
    this.currentMenu.hidden = this.checked;
    return this.currentMenu;
  }

  validateForm(): string[] {
    const msgs: string[] = [];
    this.form.validateFields(error => {
      if (error) {
        Object.keys(error).forEach(err => {
          (((error as any)[err] as any)['errors'] as []).forEach((e: any) => {
            msgs.push(e.message as string);
          });
        });
      }
    });
    return msgs;
  }

  created() {
    this.form = this.$form.createForm(this, { name: 'menuForm' });
    this.currentMenu = JSON.parse(JSON.stringify(this.selectMenu));
    this.checked = this.currentMenu.hidden || false;
    this.actions = this.currentMenu.actionIds || [];
    this.options = Object.keys(ActionEnum)
      .map<ValueLabelPair>(key => (
        {
          label: i18nHelper.getLocale(`buttons.${(ActionEnum as any)[key]}`),
          value: (ActionEnum as any)[key]
        }));
  }

  render() {
    return (
      <div>
        <a-form form={this.form} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          <a-form-item label={this.$t('platform.fields.chineseName')} required>
            <a-input
              on-change={(v: any) => this.currentMenu.name = v.target.value}
              placeholder={this.$l.getLocale(['controls.input', 'platform.fields.chineseName'])}
              v-decorator={[
                'name',
                {
                  initialValue: this.currentMenu.name,
                  rules: [
                    {
                      required: true,
                      message: this.$l.getLocale(['controls.input', 'platform.fields.chineseName']),
                    },
                  ],
                },
              ]}></a-input>
          </a-form-item>
          <a-form-item label={this.$t('platform.fields.englishName')} required>
            <a-input
              on-change={(v: any) => this.currentMenu.englishName = v.target.value}
              placeholder={this.$l.getLocale(['controls.input', 'platform.fields.englishName'])}
              v-decorator={[
                'englishName',
                {
                  initialValue: this.currentMenu.englishName,
                  rules: [
                    {
                      required: true,
                      message: this.$l.getLocale(['controls.input', 'platform.fields.englishName']),
                    },
                  ],
                },
              ]}></a-input>
          </a-form-item>
          <a-form-item label={this.$t('platform.menu.route')} required>
            <a-input
              on-change={(v: any) => this.currentMenu.path = v.target.value}
              placeholder={this.$l.getLocale(['controls.input', 'platform.menu.route'])}
              v-decorator={[
                'path',
                {
                  initialValue: this.currentMenu.path,
                  rules: [
                    {
                      required: true,
                      message: this.$l.getLocale(['controls.input', 'platform.menu.route']),
                    },
                  ],
                },
              ]}></a-input>
          </a-form-item>
          {
            this.currentMenu.type === MenuTypeEnum.page ? (
              <a-form-item label={this.$t('platform.menu.path')}>
                <a-input
                  placeholder={this.$l.getLocale(['controls.input', 'platform.menu.path'])}
                  v-model={this.currentMenu.filePath}></a-input>
              </a-form-item>
            ) : (
              <a-form-item label={this.$t('platform.menu.icon')}>
                <a-input v-model={this.currentMenu.icon}
                  placeholder={this.$l.getLocale(['controls.select', 'platform.menu.icon'])}></a-input>
              </a-form-item>
            )
          }
          <a-form-item label={this.$t('platform.fields.order')}>
            <a-input-number placeholder={this.$l.getLocale(['controls.input', 'platform.fields.order'])}
            v-model={this.currentMenu.order} min={0}>
            </a-input-number>
          </a-form-item>
          <a-form-item label={this.$t('platform.menu.hidden')}>
            <a-switch checked={this.checked}
              on-change={(checked: boolean) => this.checked = checked} />
          </a-form-item>
          {
            this.currentMenu.type === MenuTypeEnum.page ? ([
              <a-form-item label={this.$t('platform.menu.action')} required>
                <a-checkbox-group options={this.options} on-change={(checkedValue: any) => this.actions = checkedValue}
                  v-decorator={[
                    'action',
                    {
                      initialValue: this.actions,
                      rules: [
                        {
                          required: true,
                          message: this.$l.getLocale(['controls.select', 'platform.menu.action']),
                        },
                      ],
                    },
                  ]}
                ></a-checkbox-group>
              </a-form-item>
            ]) : ''
          }
        </a-form>
      </div>
    );
  }
}
