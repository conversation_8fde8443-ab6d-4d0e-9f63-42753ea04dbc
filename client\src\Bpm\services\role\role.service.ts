import { Observable } from 'rxjs';
import { httpHelper } from '@/PlatformStandard/common/utils';

class RoleService {
  getCommonRoles(domainId: string, orgLevelId: string) {
    const path = '/api/process/v1/manage/select-common-roles';
    return httpHelper.get(path, {params: {domainId: domainId, orgLevelId: orgLevelId}});
  }

  getSpecificRoles() {
    const path = '/api/process/v1/manage/select-specific-roles';
    return httpHelper.get(path);
  }

  getMatrixRoleTree() {
    const path = '/api/process/v1/manage/tree-matrix-roles';
    return httpHelper.get(path);
  }
}

export const roleService = new RoleService();
