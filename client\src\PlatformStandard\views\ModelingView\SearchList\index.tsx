import { Component, Vue, Prop } from 'vue-property-decorator';
import styles from './search-list.module.less';
import { TableData, TableColmun } from '../common/defines';
import { i18nHelper, dateFormat, guidHelper } from '../common/utils';
import { ComModal } from '../component/ComModal';
import { WrappedFormUtils } from 'ant-design-vue/types/form/form';
import dynamicApi from '../dynamic-api-json';
import * as com from '../component/Controls';
import { modelingViewService } from '../service';

@Component({
    components: { ComModal, ...com }
})
export class SearchList extends Vue {
    @Prop() pageDesginJson!: string;
    private currentPageDsegin: any = {};
    private tableData: TableData = { total: 0, items: [] };
    private tableColumns: TableColmun[] = [];
    private fieldsSlotMap: any = {};
    private pageIndex = 1;
    private pageSize = 10;
    private expand = false;
    private searchData: any = {};
    private comModalVisible = false;
    private buttonConfig: any = {};
    private form!: WrappedFormUtils;
    private scroll: any = false;
    private columnsWidth = 0;
    get getRowSelection() {
        if (this.currentPageDsegin.tableConfig.config.hasRowSelection) {
            return {
                onChange: (selectedRowKeys: any, selectedRows: any) => {
                    console.log(
                        `selectedRowKeys: ${selectedRowKeys}`,
                        'selectedRows: ',
                        selectedRows
                    );
                },
                getCheckboxProps: (record: any) => ({
                    style: { display: record.isTotal ? 'none' : 'inline-block' },
                    props: {
                        disabled: record.isTotal ? true : false
                    }
                })
            };
        } else {
            return null;
        }
    }
    private onChange(page: number, pageSize: number) {
        this.pageSize = pageSize;
        this.pageIndex = page;
        this.onLoad();
    }
    private onShowSizeChange(current: number, size: number) {
        this.pageSize = size;
        this.pageIndex = current;
        this.onLoad();
    }
    private onSearch() {
        this.pageIndex = 1;
        this.onLoad();
    }
    private getColumnText(col: any, text: any, record: any, index: number) {
        if (record.isTotal) {
            return text;
        }
        if (col.config.format.type === 'date') {
            return text
                ? dateFormat(text, col.config.format.value.displayFormat)
                : '';
        } else if (col.config.format.type === 'number') {
            let num =
                col.config.format.value.precision || col.config.format.value.precision === 0
                    ? Number(text).toFixed(col.config.format.value.precision)
                    : text;
            if (col.config.format.value.hasThousandth) {
                const arr = num.split('.');
                let result = '';
                arr.forEach((f: any, i: number) => {
                    if (i === 0) {
                        result += `${f}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                    } else {
                        result += `.${f}`;
                    }
                });
                num = result;
            }
            return num;
        } else if (col.config.format.type === 'enum') {
            const enumItem = col.config.format.value.enum.find(
                (e: any) => e.value === text.toString()
            );
            return enumItem ? enumItem.label : '';
        }
        return text;
    }
    private onButtonClick(item: any, record: any = null) {
        if (item.config.format.openModel === 'newTab') {
            window.open('/page-modeling-view/' + guidHelper.generate() +
                '?id=' + item.config.format.page +
                '&type=' + item.config.format.editModel, '_blank');
        } else if (item.config.format.openModel === 'eject') {
            item.config.format.parameter.forEach((f: any) => {
                f['actualValue'] = record ? record[f.value.replace('.', '_')] : '';
            });
            this.comModalVisible = true;
            this.buttonConfig = item;
        }
    }
    private onComModalChange(isChange: boolean) {
        this.comModalVisible = false;
        if (isChange) {
            this.onSearch();
        }
    }
    private onReset() {
        this.currentPageDsegin.generalSearchColumns.info.columns.map((x: any) => (
            x.component.forEach((c: any) => {
                c.cdata = c.info.key === 'mmt-input-number' ? null : '';
            })
        ));
        this.currentPageDsegin.advancedSearchColumns.info.columns.map((x: any) => (
            x.component.forEach((c: any) => {
                c.cdata = c.info.key === 'mmt-input-number' ? null : '';
            })
        ));
        this.form.resetFields();
        this.onSearch();
    }
    private onExpand() {
        this.expand = !this.expand;
    }
    private onLoad() {
        this.searchData = {};
        this.currentPageDsegin.generalSearchColumns.info.columns.map((x: any) => (
            x.component.map((c: any) => (
                this.searchData[c.info.id] = c.cdata
            ))
        ));
        this.currentPageDsegin.advancedSearchColumns.info.columns.map((x: any) => (
            x.component.map((c: any) => (
                this.searchData[c.info.id] = c.cdata
            ))
        ));
        const mainTable = this.currentPageDsegin.dataSource.tables.find(
            (f: any) => f.isMain
        );
        const params = dynamicApi.selectJson(this.currentPageDsegin.dataSource,
            this.searchData, this.$route.query, this.currentPageDsegin.tableConfig.config.hasPagination,
            this.pageIndex, this.pageSize, 'newTab', {});
        modelingViewService.getDynamicData(params, this.currentPageDsegin.dataSource.dataBaseId).subscribe((data: any) => {
            if (data.msg === 'success') {
                this.tableData.total = data.total;
                this.tableData.items = [];
                data['[]'].forEach((e: any) => {
                    this.tableData.items.push(
                        e[mainTable.key]
                    );
                });
                if (this.currentPageDsegin.tableConfig.config.totalColumn.length > 0) {
                    const totalColumnData: any = {};
                    this.currentPageDsegin.tableConfig.config.totalColumn.forEach((e: any) => {
                        const key = e.replace('.', '_');
                        const columnData: any = this.tableData.items.map(m => {
                            return (typeof m[key] === 'number' && !isNaN(m[key] as number)) ? m[key] : 0;
                        });
                        totalColumnData[key] = eval(columnData.join('+'));
                        totalColumnData[key] = totalColumnData[key] === 0 ? '' : totalColumnData[key];
                    });
                    this.tableData.items.push(
                        { ...totalColumnData, isTotal: true }
                    );
                }
            }
        });
    }
    // formItem
    private getFormItem(item: any) {
        const customCom = item.info.key;
        return <a-form-item
            class={styles.form_item + ' ' + styles.comp}
            label={item.config.vshow ? item.config.label : ''}
        >
            {
                <customCom
                    controlConfig={item}
                    pageModel='add'
                    value={item.cdata}
                    on-change={(value: any) => item.cdata = value}
                />
            }
        </a-form-item>;
    }
    created(): void {
        this.tableColumns = [];
        this.currentPageDsegin = this.pageDesginJson ? JSON.parse(this.pageDesginJson) : {};
        this.form = this.$form.createForm(this, { name: guidHelper.generate() });
        if (this.currentPageDsegin.tableConfig.config.hasSerial) {
            this.tableColumns.push({
                title: i18nHelper.getLocale('modeling.common.serial-number'),
                width: 80,
                align: 'center',
                customRender: (text: any, record: any, index: number) => {
                    return record.isTotal ? i18nHelper.getLocale('modeling.common.total') : index + 1;
                }
            });
            this.columnsWidth += 80;
        }
        this.currentPageDsegin.tableConfig.info.columns.forEach((f: any) => {
            this.tableColumns.push(
                {
                    title: f.config.label,
                    align: f.config.columnAlign,
                    dataIndex: f.config.dataKey.replace('.', '_'),
                    width: f.config.columnWidth ? f.config.columnWidth : undefined,
                    customRender: (text: any, record: any, index: number) => {
                        return this.getColumnText(f, text, record, index);
                    }
                }
            );
            this.columnsWidth += f.config.columnWidth ? f.config.columnWidth : 100;
        });
        if (this.currentPageDsegin.tableConfig.config.tableButtons.rowAction.length > 0) {
            this.tableColumns.push({
                title: i18nHelper.getLocale('modeling.common.operation'),
                align: 'center',
                width: this.currentPageDsegin.tableConfig.config.tableButtons.rowAction.length * 70,
                scopedSlots: { customRender: 'operation' }
            });
            this.columnsWidth += this.currentPageDsegin.tableConfig.config.tableButtons.rowAction.length * 70;
        }
        this.fieldsSlotMap['operation'] = (text: any, record: any, index: number) => {
            if (record.isTotal) {
                return '';
            }
            const buttons: any = [];
            this.currentPageDsegin.tableConfig.config.tableButtons.rowAction.forEach((f: any) => {
                buttons.push(
                    <a-button
                        type={f.config.style}
                        on-click={() => this.onButtonClick(f, record)}
                    >{f.config.label}</a-button>);
            });
            return buttons;
        };
        if (this.currentPageDsegin.tableConfig.config.tableButtons.left.length > 0
            || this.currentPageDsegin.tableConfig.config.tableButtons.right.length > 0) {
            this.fieldsSlotMap['title'] = (currentPageData: any) => {
                const leftButtons: any = [];
                const rightButtons: any = [];
                this.currentPageDsegin.tableConfig.config.tableButtons.left.forEach((e: any) => {
                    leftButtons.push(<a-button class={styles.button}
                        type={e.config.style}
                        on-click={() => this.onButtonClick(e)}
                    >{e.config.label}</a-button>);
                });
                this.currentPageDsegin.tableConfig.config.tableButtons.right.forEach((e: any) => {
                    rightButtons.push(<a-button class={styles.button}
                        type={e.config.style}
                        on-click={() => this.onButtonClick(e)}
                    >{e.config.label}</a-button>);
                });
                return (
                    <div style='display:flex;'>
                        <div style='width:50%;'>
                            {leftButtons}
                        </div>
                        <div style='width:50%;text-align:right;'>
                            {rightButtons}
                        </div>
                    </div>
                );
            };
        }
        this.onLoad();
        this.$nextTick(() => {
            const clientWidth = (this.$refs.tableDom as any).$el.clientWidth;
            this.scroll = this.columnsWidth > clientWidth ? true : false;
        });
    }
    render() {
        return (
            <div class={styles.search}>
                {
                    this.currentPageDsegin.generalSearchColumns.info.columns.length > 0
                        || this.currentPageDsegin.advancedSearchColumns.info.columns.length > 0 ? (
                        <a-form
                            form={this.form}
                            label-align={this.currentPageDsegin.config.alignMode}
                            label-col={{ style: `flex:0 0 ${this.currentPageDsegin.config.labelWidth}px` }}
                            wrapper-col={{ style: 'flex:1 1 auto' }}
                        >
                            <div class={`${styles.searcher} mx-2 my-3`}>
                                <a-row justify='space-between' gutter={8}>
                                    {
                                        this.currentPageDsegin.generalSearchColumns.info.columns.map((x: any) => (
                                            x.component.map((c: any) => (
                                                <a-col span={x.span}>
                                                    {
                                                        this.getFormItem(c)
                                                    }
                                                </a-col>)
                                            )
                                        ))
                                    }
                                    {this.expand && this.currentPageDsegin.advancedSearchColumns.info.columns.length > 0 ? (
                                        this.currentPageDsegin.advancedSearchColumns.info.columns.map((x: any) => (
                                            x.component.map((c: any) => (
                                                <a-col span={x.span}>
                                                    {
                                                        this.getFormItem(c)
                                                    }
                                                </a-col>)
                                            )
                                        ))
                                    ) : ''}
                                    <a-col span={8} class={styles.buttons}>
                                        <a-button class={styles.button} type='primary'
                                            icon='search'
                                            on-click={this.onSearch}>{this.$t('modeling.common.search')}</a-button>
                                        <a-button class={styles.button}
                                            icon='redo'
                                            on-click={this.onReset}>{this.$t('modeling.common.reset')}</a-button>
                                        {this.currentPageDsegin.advancedSearchColumns.info.columns.length > 0 ? (<a-button type='link'
                                            on-click={this.onExpand}>
                                            {this.expand ? this.$t('modeling.common.up') :
                                                this.$t('modeling.common.down')}
                                            <a-icon type={this.expand ? 'up' : 'down'} />
                                        </a-button>) : ''}
                                    </a-col>
                                </a-row>
                            </div>
                        </a-form>
                    ) : null
                }
                <a-table
                    rowKey={(record: any, index: number) => index}
                    bordered
                    columns={this.tableColumns}
                    dataSource={this.tableData.items}
                    pagination={false}
                    scopedSlots={this.fieldsSlotMap}
                    rowSelection={this.getRowSelection}
                    scroll={{ x: this.scroll ? 'max-content' : false }}
                    ref='tableDom'
                >
                </a-table>
                {this.currentPageDsegin.tableConfig.config.hasPagination ? (
                    <a-pagination
                        class={styles.pagination}
                        show-size-changer
                        current={this.pageIndex}
                        pageSize={this.pageSize}
                        size='small'
                        total={this.tableData.total}
                        showTotal={(total: number) => `共${total}条`}
                        on-change={this.onChange}
                        on-showSizeChange={this.onShowSizeChange}
                    />
                ) : ''}
                <com-modal
                    visible={this.comModalVisible}
                    buttonConfig={this.buttonConfig}
                    on-change={(isChange: boolean) => this.onComModalChange(isChange)}
                ></com-modal>
            </div>
        );
    }
}
