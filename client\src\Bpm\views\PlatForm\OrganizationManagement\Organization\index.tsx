import { Component, Vue } from 'vue-property-decorator';
import { CompCard } from '@/PlatformStandard/components';
import styles from './organization.module.less';
import { organizationService } from './service';
import { OrganizationDto, OrganizationExt, OrganizationTree, UserPositionDto, UserPositionQueryDto } from './types';
import { i18nHelper, notificationHelper, toCasedStyleObject } from '@/PlatformStandard/common/utils';
import { languageService } from '@/PlatformStandard/services/language';
import { OrganizationEdit } from './OrganizationEdit';
import { UserPositionEdit } from './UserPositionEdit';
import { zip } from 'rxjs';
import { authService } from '@/PlatformStandard/services/auth';
// import { json } from 'stream/consumers';

@Component({ components: { OrganizationEdit, UserPositionEdit, CompCard } })
export class Organization extends Vue {
  private leftHeight = 530;
  private treeData = [{}];
  private selectOrganization: OrganizationDto = { extendColumns: {} };
  private orgEditShow = false;
  private orgOption = 2;
  private userEditShow = false;
  private userOption = 2;
  private imgSrc = '';
  private userQuery: UserPositionQueryDto = {};
  private userLoading = false;
  private userDataSource: UserPositionDto[] = [];
  private fieldsSlotMap: any = {};
  private selectUserPosition: UserPositionDto = {};
  private positionTypeItems = i18nHelper.getLocaleObject('platform.organization.positionTypeDataset');
  private dataAuth = false;

  private pagination = {
    total: 0,
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    size: 'default',
    showTotal: (total: string) =>
      this.$l
        .getLocale('paginations.total')
        .toString()
        .replace('{{value}}', total),
  };

  private userColumns = [
    {
      dataIndex: 'organizationCode',
      slots: { title: 'organizationCode' },
      scopedSlots: { customRender: 'organizationCode' },
      width: '100px',
    },
    {
      dataIndex: 'organizationName',
      slots: { title: 'organizationName' },
    },
    {
      dataIndex: 'positionCode',
      slots: { title: 'positionCode' }
    },
    {
      dataIndex: 'positionName',
      slots: { title: 'positionName' },
    },
    {
      dataIndex: 'positionType',
      slots: { title: 'positionType' },
      scopedSlots: { customRender: 'positionType' },
      width: '90px'
    },
    {
      dataIndex: 'primaryPosition',
      slots: { title: 'primaryPosition' },
      scopedSlots: { customRender: 'primaryPosition' },
      width: '90px'
    },
    {
      dataIndex: 'userName',
      slots: { title: 'userName' },
    },
    {
      dataIndex: 'action',
      slots: { title: 'operation' },
      scopedSlots: { customRender: 'action' },
      width: '100px'
    },
  ];

  created() {
    const params = { 'parent-id': '' };
    organizationService.getOrganizationTree(params).subscribe(rs => {
      this.treeData = this.transformTreeData(rs);
    });
    languageService.langeAsync$.subscribe(s => {
      import(`@/assets/images/no-result-tips-${s}.png`).then(v => {
        this.imgSrc = v.default;
      });
    });
    this.fieldsSlotMap['organizationCode'] = (text: any, record: any) => {
      return (<div class={styles.textlength} title={text}>{text}</div>);
    };

    this.fieldsSlotMap['primaryPosition'] = (text: UserPositionDto[], record: UserPositionDto, index: number) => {
      return (
        <div>
          {
            record.primaryPosition ?
              '是'
              :
              '否'
          }
        </div>
      );
    };

    this.fieldsSlotMap['positionType'] = (text: UserPositionDto[], record: UserPositionDto, index: number) => {
      return (
        <div>
          {
            record.positionType === undefined || record.positionType === null ?
              ''
              :
              ((this.positionTypeItems as []).find((d: any) => d.value === record.positionType) as any).label
          }
        </div>
      );
    };

    this.fieldsSlotMap['action'] = (text: UserPositionDto[], record: UserPositionDto, index: number) => {
      return (
        <div>
          {
            this.dataAuth ?
              <div>
                <span class='mr-1'>
                  <a-button type='link' on-click={() => this.editUserPostion(record)} size='small' class={styles.list_button}>
                    {this.$l.getLocale('buttons.edit')}
                  </a-button>
                </span>
                <span class='mr-1'>
                  <a-popconfirm title={this.$t('messages.delete')} on-confirm={() => this.deleteUserPostion(record)}>
                    <a-button type='link' size='small' class={styles.list_button}>
                      {this.$l.getLocale('buttons.delete')}
                    </a-button>
                  </a-popconfirm>
                </span>
              </div>
              :
              null
          }
        </div>
      );
    };
  }

  mounted() {
    this.leftHeight = document.documentElement.offsetHeight - 50;
  }

  private onExpand(expandedKeys: string, e: any) {
    const params = { 'parent-id': e.node.dataRef.key + '' };
    if (e.node.dataRef.children) {
      return;
    }
    organizationService.getOrganizationTree(params).subscribe(rs => {
      e.node.dataRef.children = this.transformTreeData(rs);
      this.treeData = [...this.treeData];
    });
  }

  private onSelect(selectedKeys: string, e: any) {
    if (selectedKeys && selectedKeys.length > 0) {
      zip(organizationService.getOrganization(selectedKeys[0]),
        organizationService.getUserOrganizationDataAuthority(String(authService.user.id), selectedKeys[0]))
        .subscribe(rs => {
          this.selectOrganization = rs[0];
          this.dataAuth = rs[1];
          this.loadUserData(true);
        });
    } else {
      this.selectOrganization = { extendColumns: {} };
      this.userDataSource = [];
      this.selectUserPosition = {};
    }
  }

  private transformTreeData(data: OrganizationTree[]) {
    return data.map(o => ({
      title: o.label,
      key: o.value,
      datum: o.childCount,
      isLeaf: o.childCount === 0,
      slots: { icon: o.childCount === 0 ? 'file' : 'folder' },
    }));
  }

  private searchOrganization(value: string) {
    if (value && value !== '') {
      const params = { 'key-word': value };
      organizationService.searchOrganizations(params).subscribe(rs => {
        this.treeData = this.transformTreeData(rs);
      });
    } else {
      const params = { 'parent-id': '' };
      organizationService.getOrganizationTree(params).subscribe(rs => {
        this.treeData = this.transformTreeData(rs);
      });
    }
  }

  private addTop() {
    this.orgOption = 0;
    this.orgEditShow = true;
  }

  private addSub() {
    if (this.selectOrganization.organizationId) {
      this.orgOption = 1;
      this.orgEditShow = true;
    } else {
      notificationHelper.warning(i18nHelper.getLocale(['controls.select', 'platform.organization.org']));
    }
  }

  private editOrg() {
    if (this.selectOrganization.organizationId) {
      this.orgOption = 2;
      this.orgEditShow = true;
    } else {
      notificationHelper.warning(i18nHelper.getLocale(['controls.select', 'platform.organization.org']));
    }
  }

  private deleteOrg() {
    if (this.selectOrganization.organizationId) {
      organizationService.deleteOrganization(this.selectOrganization).subscribe(() => {
        this.deleteTreeNode(String(this.selectOrganization.organizationId));
        notificationHelper.success(i18nHelper.getLocale('messages.success'));
      });
    } else {
      notificationHelper.warning(i18nHelper.getLocale(['controls.select', 'platform.organization.org']));
    }
  }

  private cancelOrganization() {
    this.orgEditShow = false;
  }

  private saveOrganization() {
    // alert(JSON.stringify(this.selectOrganization));
    const errorMsgs = (this.$refs.editOrganization as OrganizationEdit).validateForm() as string[];
    if (errorMsgs.length === 0) {
      const organization = (this.$refs.editOrganization as any).save() as OrganizationDto;
      // alert(JSON.stringify(organization));
      if (this.orgOption === 2) {
        organizationService.saveOrganization(organization).subscribe(rs => {
          this.selectOrganization = organization;
          this.changeOrganizationName(String(organization.organizationId), String(organization.name));
          this.orgEditShow = false;
          notificationHelper.success(i18nHelper.getLocale('messages.success'));
        });
      } else {
        organizationService.saveOrganization(organization).subscribe(rs => {
          this.orgEditShow = false;
          const treeNode = {
            title: rs.name,
            key: rs.organizationId,
            isLeaf: true,
            slots: { icon: 'file' },
          };
          if (this.orgOption === 0) {
            this.treeData = [...this.treeData, treeNode];
          } else {
            this.addTreeNode(String(rs.upperId), treeNode);
          }
          notificationHelper.success(i18nHelper.getLocale('messages.success'));
        });
      }
    } else {
      notificationHelper.error(errorMsgs);
    }
  }

  private changeOrganizationName(organizationId: string, organizationName: string) {
    this.treeData.forEach((data: any) => {
      if (String(data.key).toLowerCase() === organizationId.toLowerCase()) {
        data.title = organizationName;
      }
      if (data.children) {
        this.changeChildOrganizationName(organizationId, organizationName, data.children);
      }
    });
  }

  private changeChildOrganizationName(organizationId: string, organizationName: string, children: []) {
    children.forEach((data: any) => {
      if (String(data.key).toLowerCase() === organizationId.toLowerCase()) {
        data.title = organizationName;
      }
      if (data.children) {
        this.changeChildOrganizationName(organizationId, organizationName, data.children);
      }
    });
  }

  private addTreeNode(organizationId: string, treeNode: any) {
    this.treeData.forEach((data: any) => {
      if (String(data.key).toLowerCase() === organizationId.toLowerCase()) {
        if (data.children === undefined || data.children.length === 0) {
          data.children = [treeNode];
          data.isLeaf = false;
          data.slots = { icon: 'folder' };
        } else {
          data.children = [...data.children, treeNode];
        }
      }
      if (data.children) {
        this.addSubTreeNode(organizationId, treeNode, data.children);
      }
    });
  }

  private addSubTreeNode(organizationId: string, treeNode: any, children: []) {
    children.forEach((data: any) => {
      if (String(data.key).toLowerCase() === organizationId.toLowerCase()) {
        if (data.children === undefined || data.children.length === 0) {
          data.children = [treeNode];
          data.isLeaf = false;
          data.slots = { icon: 'folder' };
        } else {
          data.children = [...data.children, treeNode];
        }
      }
      if (data.children) {
        this.addSubTreeNode(organizationId, treeNode, data.children);
      }
    });
  }

  private deleteTreeNode(organizationId: string) {
    this.treeData.forEach((data: any, index: number) => {
      if (String(data.key).toLowerCase() === organizationId.toLowerCase()) {
        this.treeData.splice(index, 1);
        this.$forceUpdate();
      }
      if (data.children) {
        this.deleteSubTreeNode(organizationId, data.children);
        if (data.children.length === 0) {
          data.isLeaf = true;
          data.slots = { icon: 'file' };
        }
      }
    });
  }

  private deleteSubTreeNode(organizationId: string, children: []) {
    children.forEach((data: any, index: number) => {
      if (String(data.key).toLowerCase() === organizationId.toLowerCase()) {
        children.splice(index, 1);
        this.$forceUpdate();
      }
      if (data.children) {
        this.deleteSubTreeNode(organizationId, data.children);
      }
    });
  }

  private loadUserData(reset: boolean = false) {
    if (reset) {
      this.pagination.current = 1;
    }

    this.userQuery.organizationId = this.selectOrganization.organizationId;

    const params = {
      'page-index': this.pagination.current,
      'page-size': this.pagination.pageSize
    };

    organizationService.getPositionUsers({ ...toCasedStyleObject(this.userQuery), ...params }).subscribe(data => {
      // debugger;
      this.userDataSource = data.items;
      this.pagination.total = data.total;
      this.$forceUpdate();
    });

  }

  // 重置
  private onReset() {
    if (this.selectOrganization.organizationId) {
      this.userQuery = { organizationId: this.selectOrganization.organizationId };
    } else {
      this.userQuery = {};
    }
  }

  private addUserPostion() {
    if (this.selectOrganization.organizationId) {
      this.userOption = 1;
      this.userEditShow = true;
    } else {
      notificationHelper.warning(i18nHelper.getLocale(['controls.select', 'platform.organization.org']));
    }
  }

  private editUserPostion(userPosition: UserPositionDto) {
    if (this.selectOrganization.organizationId) {
      this.userOption = 2;
      this.selectUserPosition = userPosition;
      this.userEditShow = true;
    } else {
      notificationHelper.warning(i18nHelper.getLocale(['controls.select', 'platform.organization.org']));
    }
  }

  private deleteUserPostion(userPosition: UserPositionDto) {
    organizationService.deletePositionAndUser(userPosition).subscribe(() => {
      this.loadUserData(false);
      notificationHelper.success(i18nHelper.getLocale('messages.success'));
    });
  }

  private cancelUserPosition() {
    this.userEditShow = false;
  }

  private saveUserPosition() {
    const errorMsgs = (this.$refs.editUserPosition as UserPositionEdit).validateForm() as string[];
    if (errorMsgs.length === 0) {
      const userPosition = (this.$refs.editUserPosition as any).save() as UserPositionDto;
      organizationService.savePositionAndUser(userPosition).subscribe(() => {
        this.userEditShow = false;
        this.loadUserData(false);
        notificationHelper.success(i18nHelper.getLocale('messages.success'));
      });
    } else {
      notificationHelper.error(errorMsgs);
    }
  }

  render() {
    return (
      <div class={styles.page}>
        <div class={styles.tree_list} style={`height:${this.leftHeight}px`}>
          <comp-card title={this.$t('platform.organization.list')} class={styles.tree_card}>
            <a-input-search placeholder={this.$t('platform.organization.info')} enter-button on-search={this.searchOrganization} />
            <a-tree on-expand={this.onExpand} on-select={this.onSelect} tree-data={this.treeData} show-icon>
              <a-icon slot='switcherIcon' type='down' />
              <a-icon slot='folder' type='folder' style='color:#1890FF' theme='filled'/>
              <a-icon slot='file' type='file'  style='color:#9BB2C6' theme='filled'/>
            </a-tree>
          </comp-card>
        </div>
        <div class={styles.content_info}>
          <div class={styles.edit_info}>
            <comp-card title={this.$t('platform.organization.info')} class={styles.info_card}>
              <div slot='extra'>
                <a-dropdown class={styles.buttons}>
                  <a-menu slot='overlay'>
                    <a-menu-item key='addTop' on-click={() => this.addTop()}>
                      {this.$t('platform.organization.topOrganization')}
                    </a-menu-item>
                    {
                      this.dataAuth ?
                        <a-menu-item key='addSub' on-click={() => this.addSub()}>
                          {this.$t('platform.organization.subdivision')}
                        </a-menu-item>
                        :
                        null
                    }
                  </a-menu>
                  <a-button type='primary' class={styles.common_btn}>
                    {this.$t('buttons.add')}
                    <a-icon type='down' />
                  </a-button>
                </a-dropdown>
                {
                  this.dataAuth ?
                    <a-button type='primary' class={styles.common_btn} on-click={() => this.editOrg()}>
                      {this.$t('buttons.edit')}
                    </a-button>
                    :
                    null
                }
                {
                  this.dataAuth ?
                    <a-popconfirm
                      title={this.$t('messages.delete')}
                      ok-text={this.$t('buttons.ok')}
                      cancel-text={this.$t('buttons.cancel')}
                      on-confirm={() => this.deleteOrg()}>
                      {!this.selectOrganization.hasChild ? (
                        <a-button class={styles.common_btn}>
                          {this.$t('buttons.delete')}
                        </a-button>
                      ) : ''}
                    </a-popconfirm>
                    :
                    null
                }
              </div>
              {this.selectOrganization.organizationId ? (
                <div class={styles.labelStyle}>
                    <div class={styles.labelIcon} style='float:left; margin-left:16px;'></div>
                    <div class={styles.labelInfo} style='width:calc(100% - 80px);float:left;padding-left:16px'>
                      <div style='border-bottom:1px solid #ddd;padding-bottom:8px;margin-bottom:8px;'>
                        <a-row>
                          <a-col span='24'><b style='font-size: 14px;'>
                            {this.$t('platform.fields.name')}：{this.selectOrganization.name}</b></a-col>
                        </a-row>
                        <a-row>
                          <a-col span='24'><font>{this.$t('platform.fields.code')}：
                          {this.selectOrganization.organizationCode}</font></a-col>
                        </a-row>
                        <a-row>
                          <a-col span='24'><font>{this.$t('platform.menu.path')}：
                          {this.selectOrganization.fullPathCode}</font></a-col>
                        </a-row>
                        <a-row>
                          <a-col span='24'><font>{this.$t('platform.menu.namePath')}：
                          {this.selectOrganization.fullPathText}</font></a-col>
                        </a-row>
                      </div>
                      <a-row>
                        <a-col span='2'><font>{this.$t('platform.fields.leader')}：</font></a-col>
                        <a-col span='10'>{this.selectOrganization.extendColumns.managerNames}</a-col>
                        <a-col span='2'><font>{this.positionTypeItems[1].label}：</font></a-col>
                        <a-col span='10'>{this.selectOrganization.extendColumns.fgfzNames}</a-col>
                      </a-row>
                      <a-row> <a-col span='2'><font>{this.$t('platform.organization.businessType')}：</font></a-col>
                        <a-col span='10'>{this.selectOrganization.extendColumns.domainName}</a-col>
                        <a-col span='2'><font>{this.$t('platform.organization.businessTypeLevel')}：</font></a-col>
                        <a-col span='10'>{this.selectOrganization.extendColumns.domainLevelName}</a-col>
                      </a-row>
                      <a-row> <a-col span='2'><font>{this.$t('platform.fields.domainLevelCName')}：</font></a-col>
                        <a-col span='10'>{this.selectOrganization.extendColumns.cLevelName}</a-col>
                        <a-col span='2'><font>{this.$t('platform.fields.remark')}：</font></a-col>
                        <a-col span='10'>{this.selectOrganization.remark}</a-col>
                      </a-row>
                      <a-row>
                        <a-col span='2'><font>{this.$t('platform.fields.level')}：</font></a-col>
                        <a-col span='10'>{this.selectOrganization.level}</a-col>
                      </a-row>
                    </div>
                </div>
              ) : (
                // <img class='no-result-tips' src={this.imgSrc} />
                <p style='width:100%;text-align:center;padding:10px 0 0 0'>请在左侧组织列表选择具体组织</p>
              )}
            </comp-card>
          </div>
          <div class={styles.edit_info2}>
            <comp-card title={this.$t('platform.organization.organizationUser')} class={styles.info_card}>
            <div slot='extra'>
              <a-button type='primary' style='margin:0 0 0 10px' class={styles.common_btn} on-click={() => this.addUserPostion()}>
                {this.$t('buttons.add')}
              </a-button>
            </div>
              <div clas={styles.row_outer}>
                <a-row class={styles.row} gutter={8} align='middle' type='flex'>
                  <a-col span='6'>
                    <a-form-item label={this.$t('platform.organization.info')} class={styles.marginRightWidth}>
                      <a-input v-model={this.userQuery.orgInfo} ></a-input>
                    </a-form-item>
                  </a-col>
                  <a-col span='6'>
                    <a-form-item label={this.$t('platform.organization.positionInfo')} class={styles.marginRightWidth}>
                      <a-input
                        v-model={this.userQuery.positionInfo}></a-input>
                    </a-form-item>
                  </a-col>
                  <a-col span='6'>
                    <a-form-item label={this.$t('platform.organization.user')} class={styles.marginRightWidth}>
                      <a-input v-model={this.userQuery.userInfo}></a-input>
                    </a-form-item>
                  </a-col>
                  <a-col span='6' class={styles.operation} style='padding-top:15px'>
                    <a-button type='primary' class={styles.searchBtn} loading={this.userLoading} on-click={() => this.loadUserData(true)}>
                      {this.userLoading ? this.$t('buttons.searching') : this.$t('buttons.search')}
                    </a-button>
                    <a-button class={styles.common_btn} on-click={() => this.onReset}>
                      {this.$t('buttons.reset')}
                    </a-button>
                    {/* <a-button type='primary' class={styles.common_btn} on-click={() => this.addUserPostion()}>
                      {this.$t('buttons.add')}
                    </a-button> */}
                  </a-col>
                </a-row>
                <a-row>
                  {/* <a-col span='12'>
                  <a-button type='primary' style='margin:0 0 10px 0' class={styles.common_btn} on-click={() => this.addUserPostion()}>
                      {this.$t('buttons.add')}
                    </a-button>
                  </a-col> */}
                </a-row>
              </div>
              <a-table
                rowKey={(record: any, index: number) => index}
                columns={this.userColumns}
                data-source={this.userDataSource}
                pagination={this.pagination}
                scopedSlots={this.fieldsSlotMap}
                on-change={(pagination: any) => {
                  this.pagination = pagination;
                  this.loadUserData(false);
                }}
                size='small'
              >
                <span slot='organizationCode'>{this.$t('platform.organization.organizationCode')}</span>
                <span slot='organizationName'>{this.$t('platform.organization.organizationName')}</span>
                <span slot='positionCode'>{this.$t('platform.organization.positionCode')}</span>
                <span slot='positionName'>{this.$t('platform.organization.positionName')}</span>
                <span slot='positionType'>{this.$t('platform.organization.positionType')}</span>
                <span slot='primaryPosition'>{this.$t('platform.organization.isPrimary')}</span>
                <span slot='userName'>{this.$t('platform.organization.user')}</span>
                <span slot='operation'>{this.$t('platform.fields.operation')}</span>
              </a-table>
            </comp-card>
          </div>
        </div>
        <a-modal
          width='800px'
          title={this.$t('platform.organization.info')}
          visible={this.orgEditShow}
          destroyOnClose={true}
          maskClosable={false}
          on-cancel={() => this.cancelOrganization()}
          on-ok={() => this.saveOrganization()}
        >
          <organization-edit ref='editOrganization' selectOrganization={this.selectOrganization} option={this.orgOption} />
        </a-modal>
        <a-modal
          width='800px'
          title={this.$t('platform.organization.organizationUser')}
          visible={this.userEditShow}
          destroyOnClose={true}
          maskClosable={false}
          on-cancel={() => this.cancelUserPosition()}
          on-ok={() => this.saveUserPosition()}
        >
          <user-position-edit
            ref='editUserPosition'
            selectOrganization={this.selectOrganization}
            selectUserPosition={this.selectUserPosition}
            option={this.userOption} />
        </a-modal>
      </div>
    );
  }
}
