import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator';
import styles from './layout.module.less';
import { guidHelper } from '../../../common/utils';
import Draggable from 'vuedraggable';
import { MmtFormItem } from '../MmtFormItem';
import { BaseControl } from '../base-control';
@Component({
    components: {
        Draggable,
        MmtFormItem
    }
})
export class MmtLayout extends BaseControl {
    @Prop() currentCheckedItemId!: string;
    @Prop({ default: true }) isMove!: boolean;
    @Prop({ default: true }) isEdit!: boolean;
    @Prop({ default: true }) isDeleted!: boolean;
    @Emit('checked')
    checked(item: any, isFormTable: boolean) {
    }
    @Emit('deleted')
    deleted(item: any, parentItem: any, index: number) {
    }
    private add(newIndex: number, items: any) {
        const newItem = JSON.parse(JSON.stringify(items[newIndex]));
        newItem.info.id = guidHelper.generate();
        items.splice(0, items.length);
        items.splice(0, 0, newItem);
        newIndex = 0;
        this.checked(items[newIndex], false);
    }
    created(): void {
    }
    render() {
        console.log(this.controlConfig);
        return (
            this.controlConfig ? (
                <div class={styles.div + ` ${this.currentCheckedItemId === this.controlConfig.info.id
                    ? styles.active : null}`}>
                    <a-row>
                        {
                            this.controlConfig.info.columns.map((m: any, i: number) => (
                                <a-col
                                    span={m.span}
                                    class={styles.col}
                                >
                                    <draggable
                                        v-model={m.component}
                                        group={{ name: 'people' }}
                                        animation={200}
                                        ghostClass={styles.ghost}
                                        on-add={(event: any) => this.add(event.newIndex, m.component)}
                                    >
                                        <transition-group
                                            name='fade'
                                            tag='div'
                                            class={styles.col_component}
                                        >
                                            {m.component.length === 1 && m.component[0].info.id ? (
                                                <mmt-form-item
                                                    controlConfig={m.component[0]}
                                                    currentCheckedItemId={this.currentCheckedItemId}
                                                    isMove={false}
                                                    key={m.component[0].info.id}
                                                    on-checked={() => this.checked(m.component[0], false)}
                                                    on-deleted={() => this.deleted(m.component[0], m.component, 0)}
                                                ></mmt-form-item>
                                            ) : null}
                                        </transition-group>
                                    </draggable>
                                </a-col>
                            ))
                        }
                    </a-row>
                    {
                        this.isEdit || this.isDeleted ? (
                            <div class={styles.form_item_action}>
                                {
                                    this.isEdit ? (
                                        <a-icon type='edit' title='属性设置'
                                            nativeOnClick={(e: any) => {
                                                this.checked(this.controlConfig, false);
                                                e.stopPropagation();
                                            }}
                                        />
                                    ) : null
                                }
                                {
                                    this.isDeleted ? (
                                        <a-icon type='delete' title='删除'
                                            nativeOnClick={(e: any) => {
                                                this.deleted(this.controlConfig, null, -1);
                                                e.stopPropagation();
                                            }}
                                        />
                                    ) : null
                                }
                            </div>
                        ) : null
                    }
                    {
                        this.isMove &&
                            this.currentCheckedItemId === this.controlConfig.info.id ? (
                            <div class={styles.form_item_drag}>
                                <a-icon type='drag' class='drag-widget' />
                            </div>
                        ) : null
                    }
                </div>
            ) : null
        );
    }
}
