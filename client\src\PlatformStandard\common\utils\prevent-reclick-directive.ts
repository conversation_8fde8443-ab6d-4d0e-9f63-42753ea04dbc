import { isNumber } from 'lodash';
import { Vue } from 'vue-property-decorator';

class PreventReClickDirective {
  register() {
    Vue.directive('preventReClick', (el, binding, vnode) => {
      const sec = isNumber(binding.value ) ? binding.value : 1000;
      el.addEventListener('click', () => {
        if (!el.hasAttribute('disabled')) {
          el.setAttribute('disabled', '');
          setTimeout(() => {
            el.removeAttribute('disabled');
          }, sec);
        }
      });
    });
  }
}

export const preventReClickDirective = new PreventReClickDirective();
