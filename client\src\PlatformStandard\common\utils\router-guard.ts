import { Route } from 'vue-router';

import { authService } from '@/PlatformStandard/services/auth';

export function routerGuard(to: Route, from: Route, next: (option?: any) => void) {
  authService.getAuthState().subscribe(
    () => {
      next();
    },
    error => {
      if (error && error.response) {
        if (error.response.status === 401) {
          next(error.response.data);
        } else if (error.response.status === 308) {
          window.location.href = error.response.data;
        }
      }
    }
  );
}
